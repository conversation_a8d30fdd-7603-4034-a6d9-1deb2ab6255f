from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from users.models import CustomUser


class Property(models.Model):
    """
    Modelo para representar una propiedad/vivienda en el sistema GeoVigia
    """

    PROPERTY_TYPES = (
        ('casa', 'Casa'),
        ('apartamento', 'Apartamento'),
        ('oficina', 'Oficina'),
        ('local', 'Local Comercial'),
        ('bodega', 'Bodega'),
        ('otro', 'Otro'),
    )

    STATUS_CHOICES = (
        ('activa', 'Activa'),
        ('inactiva', 'Inactiva'),
        ('mantenimiento', 'En Mantenimiento'),
        ('suspendida', 'Suspendida'),
    )

    # Información básica
    name = models.CharField(
        max_length=100,
        verbose_name='Nombre de la Propiedad',
        help_text='Nombre identificativo de la propiedad'
    )

    property_type = models.CharField(
        max_length=20,
        choices=PROPERTY_TYPES,
        default='casa',
        verbose_name='Tipo de Propiedad'
    )

    # Dirección y ubicación
    address = models.TextField(
        verbose_name='Dirección Completa'
    )

    neighborhood = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='Barrio/Colonia'
    )

    city = models.CharField(
        max_length=100,
        default='Ciudad',
        verbose_name='Ciudad'
    )

    postal_code = models.CharField(
        max_length=10,
        blank=True,
        verbose_name='Código Postal'
    )

    # Coordenadas geográficas
    latitude = models.DecimalField(
        max_digits=10,
        decimal_places=8,
        verbose_name='Latitud',
        help_text='Coordenada de latitud de la propiedad'
    )

    longitude = models.DecimalField(
        max_digits=11,
        decimal_places=8,
        verbose_name='Longitud',
        help_text='Coordenada de longitud de la propiedad'
    )

    # Información adicional
    description = models.TextField(
        blank=True,
        verbose_name='Descripción',
        help_text='Descripción adicional de la propiedad'
    )

    area_size = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='Tamaño del Área (m²)',
        help_text='Tamaño del área en metros cuadrados'
    )

    floors = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(50)],
        verbose_name='Número de Pisos'
    )

    # Estado y configuración
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='activa',
        verbose_name='Estado'
    )

    is_monitored = models.BooleanField(
        default=True,
        verbose_name='Bajo Monitoreo',
        help_text='Indica si la propiedad está siendo monitoreada activamente'
    )

    priority_level = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name='Nivel de Prioridad',
        help_text='Nivel de prioridad para el monitoreo (1=Baja, 5=Crítica)'
    )

    # Información de contacto de emergencia
    emergency_contact_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='Contacto de Emergencia'
    )

    emergency_contact_phone = models.CharField(
        max_length=17,
        blank=True,
        verbose_name='Teléfono de Emergencia'
    )

    # Metadatos
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Fecha de Creación'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='Última Actualización'
    )

    class Meta:
        verbose_name = 'Propiedad'
        verbose_name_plural = 'Propiedades'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['latitude', 'longitude']),
            models.Index(fields=['status', 'is_monitored']),
            models.Index(fields=['priority_level']),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_property_type_display()})"

    @property
    def coordinates(self):
        """Retorna las coordenadas como tupla"""
        return (float(self.latitude), float(self.longitude))

    @property
    def full_address(self):
        """Retorna la dirección completa formateada"""
        address_parts = [self.address]
        if self.neighborhood:
            address_parts.append(self.neighborhood)
        address_parts.append(self.city)
        if self.postal_code:
            address_parts.append(self.postal_code)
        return ', '.join(address_parts)

    def is_high_priority(self):
        """Verifica si la propiedad es de alta prioridad"""
        return self.priority_level >= 4


class PropertyOwnership(models.Model):
    """
    Modelo para la relación entre clientes y propiedades
    Un cliente puede tener múltiples propiedades y una propiedad puede tener múltiples propietarios
    """

    OWNERSHIP_TYPES = (
        ('propietario', 'Propietario'),
        ('inquilino', 'Inquilino'),
        ('administrador', 'Administrador'),
        ('contacto', 'Contacto Autorizado'),
    )

    client = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'cliente'},
        related_name='property_ownerships',
        verbose_name='Cliente'
    )

    property = models.ForeignKey(
        Property,
        on_delete=models.CASCADE,
        related_name='ownerships',
        verbose_name='Propiedad'
    )

    ownership_type = models.CharField(
        max_length=20,
        choices=OWNERSHIP_TYPES,
        default='propietario',
        verbose_name='Tipo de Relación'
    )

    is_primary = models.BooleanField(
        default=False,
        verbose_name='Contacto Principal',
        help_text='Indica si es el contacto principal para esta propiedad'
    )

    can_receive_alerts = models.BooleanField(
        default=True,
        verbose_name='Puede Recibir Alertas',
        help_text='Indica si puede recibir alertas de esta propiedad'
    )

    notes = models.TextField(
        blank=True,
        verbose_name='Notas',
        help_text='Notas adicionales sobre la relación'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Fecha de Asignación'
    )

    class Meta:
        verbose_name = 'Asignación de Propiedad'
        verbose_name_plural = 'Asignaciones de Propiedades'
        unique_together = ['client', 'property']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.client.username} - {self.property.name} ({self.get_ownership_type_display()})"


class PropertyPerimeter(models.Model):
    """
    Modelo para definir perímetros de seguridad alrededor de las propiedades
    """

    PERIMETER_TYPES = (
        ('circular', 'Circular'),
        ('rectangular', 'Rectangular'),
        ('polygon', 'Polígono Personalizado'),
    )

    property = models.OneToOneField(
        Property,
        on_delete=models.CASCADE,
        related_name='perimeter',
        verbose_name='Propiedad'
    )

    perimeter_type = models.CharField(
        max_length=20,
        choices=PERIMETER_TYPES,
        default='circular',
        verbose_name='Tipo de Perímetro'
    )

    # Para perímetro circular
    radius_meters = models.PositiveIntegerField(
        default=50,
        verbose_name='Radio en Metros',
        help_text='Radio del perímetro circular en metros'
    )

    # Para perímetro rectangular
    width_meters = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='Ancho en Metros',
        help_text='Ancho del perímetro rectangular'
    )

    height_meters = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='Alto en Metros',
        help_text='Alto del perímetro rectangular'
    )

    # Para polígono personalizado (JSON con coordenadas)
    polygon_coordinates = models.JSONField(
        null=True,
        blank=True,
        verbose_name='Coordenadas del Polígono',
        help_text='Array de coordenadas [lat, lng] que definen el polígono'
    )

    # Configuración del perímetro
    is_active = models.BooleanField(
        default=True,
        verbose_name='Perímetro Activo',
        help_text='Indica si el perímetro está activo para monitoreo'
    )

    alert_on_entry = models.BooleanField(
        default=True,
        verbose_name='Alerta al Entrar',
        help_text='Generar alerta cuando el guardia entra al perímetro'
    )

    alert_on_exit = models.BooleanField(
        default=True,
        verbose_name='Alerta al Salir',
        help_text='Generar alerta cuando el guardia sale del perímetro'
    )

    description = models.TextField(
        blank=True,
        verbose_name='Descripción',
        help_text='Descripción del perímetro y sus características'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Fecha de Creación'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='Última Actualización'
    )

    class Meta:
        verbose_name = 'Perímetro de Propiedad'
        verbose_name_plural = 'Perímetros de Propiedades'
        ordering = ['-created_at']

    def __str__(self):
        return f"Perímetro {self.get_perimeter_type_display()} - {self.property.name}"

    def get_area_coverage(self):
        """Calcula el área de cobertura del perímetro en metros cuadrados"""
        if self.perimeter_type == 'circular':
            import math
            return math.pi * (self.radius_meters ** 2)
        elif self.perimeter_type == 'rectangular':
            if self.width_meters and self.height_meters:
                return self.width_meters * self.height_meters
        return None

    def clean(self):
        """Validación personalizada del modelo"""
        from django.core.exceptions import ValidationError

        if self.perimeter_type == 'rectangular':
            if not self.width_meters or not self.height_meters:
                raise ValidationError(
                    'Para perímetro rectangular se requieren ancho y alto'
                )
        elif self.perimeter_type == 'polygon':
            if not self.polygon_coordinates:
                raise ValidationError(
                    'Para perímetro de polígono se requieren las coordenadas'
                )
            if len(self.polygon_coordinates) < 3:
                raise ValidationError(
                    'Un polígono requiere al menos 3 puntos'
                )

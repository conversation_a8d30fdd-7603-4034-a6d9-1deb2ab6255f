/**
 * Dashboard específico para guardias
 */

class GuardiaDashboard {
    constructor() {
        this.currentRoute = null;
        this.activeAlerts = [];
        this.properties = [];
        this.isOnRoute = false;
        this.init();
    }

    init() {
        this.loadRouteData();
        this.loadProperties();
        this.loadActiveAlerts();
        this.startRealTimeUpdates();
    }

    async loadRouteData() {
        try {
            // Simular carga de datos de ruta
            this.currentRoute = {
                name: "Ruta Nocturna Sector A",
                totalProperties: 8,
                completedRounds: 2,
                estimatedTime: "45 min"
            };

            document.getElementById('route-name').textContent = this.currentRoute.name;
            document.getElementById('total-properties').textContent = this.currentRoute.totalProperties;
            document.getElementById('completed-rounds').textContent = this.currentRoute.completedRounds;
            document.getElementById('estimated-time').textContent = this.currentRoute.estimatedTime;
        } catch (error) {
            console.error('Error cargando datos de ruta:', error);
        }
    }

    async loadProperties() {
        try {
            // Simular carga de propiedades
            this.properties = [
                {
                    id: 1,
                    name: "Casa Residencial A",
                    address: "Av. Reforma 123",
                    status: "occupied",
                    hasAlert: false
                },
                {
                    id: 2,
                    name: "Casa Residencial B",
                    address: "Av. Reforma 125",
                    status: "empty",
                    hasAlert: false
                },
                {
                    id: 3,
                    name: "Casa Residencial C",
                    address: "Av. Reforma 127",
                    status: "occupied",
                    hasAlert: true,
                    alertType: "actitud_sospechosa"
                }
            ];

            this.renderProperties();
        } catch (error) {
            console.error('Error cargando propiedades:', error);
        }
    }

    renderProperties() {
        const propertiesList = document.getElementById('properties-list');
        propertiesList.innerHTML = this.properties.map(property => `
            <div class="property-item ${property.status} ${property.hasAlert ? 'alert' : ''}">
                <div class="property-icon">
                    <i class="fas fa-home"></i>
                </div>
                <div class="property-info">
                    <h4>${property.name}</h4>
                    <p>${property.address}</p>
                    <span class="occupancy-status ${property.status}">
                        ${property.status === 'occupied' ? 'Ocupada' : 'Casa Sola'}
                    </span>
                    ${property.hasAlert ? `
                        <div class="alert-indicator">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Actitud sospechosa reportada</span>
                        </div>
                    ` : ''}
                </div>
                <div class="property-actions">
                    <button class="check-btn ${property.hasAlert ? 'priority' : ''}"
                            onclick="checkProperty(${property.id})">
                        <i class="fas fa-${property.hasAlert ? 'exclamation' : 'check'}"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    async loadActiveAlerts() {
        try {
            // Simular carga de alertas activas
            this.activeAlerts = [
                {
                    id: 1,
                    type: "solicitar_ayuda",
                    priority: "high",
                    title: "Solicitud de Ayuda",
                    property: "Casa Residencial C",
                    client: "Ana Propietaria",
                    time: "Hace 2 minutos"
                },
                {
                    id: 2,
                    type: "llegando_casa",
                    priority: "medium",
                    title: "Propietario Llegando",
                    property: "Casa Residencial A",
                    client: "Juan Pérez",
                    time: "Hace 5 minutos"
                }
            ];

            this.renderAlerts();
        } catch (error) {
            console.error('Error cargando alertas:', error);
        }
    }

    renderAlerts() {
        const alertsContainer = document.getElementById('alerts-container');
        alertsContainer.innerHTML = this.activeAlerts.map(alert => `
            <div class="alert-item ${alert.priority}-priority">
                <div class="alert-icon">
                    <i class="fas fa-${this.getAlertIcon(alert.type)}"></i>
                </div>
                <div class="alert-content">
                    <h4>${alert.title}</h4>
                    <p>${alert.property} - ${alert.client}</p>
                    <span class="alert-time">${alert.time}</span>
                </div>
                <button class="respond-btn" onclick="respondToAlert(${alert.id})">
                    ${alert.priority === 'high' ? 'Responder' : 'Confirmar'}
                </button>
            </div>
        `).join('');
    }

    getAlertIcon(type) {
        const icons = {
            'solicitar_ayuda': 'sos',
            'actitud_sospechosa': 'exclamation-triangle',
            'llegando_casa': 'home',
            'casa_sola': 'suitcase'
        };
        return icons[type] || 'bell';
    }

    startRealTimeUpdates() {
        // Simular actualizaciones en tiempo real
        setInterval(() => {
            this.checkForNewAlerts();
            this.updateRouteProgress();
        }, 15000); // Cada 15 segundos
    }

    checkForNewAlerts() {
        // Simular nuevas alertas ocasionalmente
        if (Math.random() < 0.1) { // 10% de probabilidad
            const newAlert = {
                id: Date.now(),
                type: "casa_sola",
                priority: "medium",
                title: "Casa Sola",
                property: "Casa Residencial D",
                client: "María García",
                time: "Ahora"
            };

            this.activeAlerts.unshift(newAlert);
            this.renderAlerts();
            this.showNotification(`Nueva alerta: ${newAlert.title}`, 'info');
        }
    }

    updateRouteProgress() {
        // Simular progreso de ruta
        if (this.isOnRoute) {
            this.currentRoute.completedRounds++;
            document.getElementById('completed-rounds').textContent = this.currentRoute.completedRounds;
        }
    }

    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#3182ce'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            z-index: 1001;
            animation: slideIn 0.3s ease;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 4000);
    }
}

// Funciones globales
function startRoute() {
    guardiaDashboard.isOnRoute = true;
    const btn = document.querySelector('.start-route-btn');
    btn.innerHTML = '<i class="fas fa-stop"></i> Detener Recorrido';
    btn.onclick = stopRoute;

    guardiaDashboard.showNotification('Recorrido iniciado', 'success');
}

function stopRoute() {
    guardiaDashboard.isOnRoute = false;
    const btn = document.querySelector('.start-route-btn');
    btn.innerHTML = '<i class="fas fa-play"></i> Iniciar Recorrido';
    btn.onclick = startRoute;

    guardiaDashboard.showNotification('Recorrido detenido', 'info');
}

function checkProperty(propertyId) {
    const property = guardiaDashboard.properties.find(p => p.id === propertyId);
    if (property) {
        if (property.hasAlert) {
            property.hasAlert = false;
            property.alertType = null;
            guardiaDashboard.showNotification(`Alerta de ${property.name} atendida`, 'success');
        } else {
            guardiaDashboard.showNotification(`${property.name} verificada`, 'success');
        }

        guardiaDashboard.renderProperties();
    }
}

function respondToAlert(alertId) {
    const alertIndex = guardiaDashboard.activeAlerts.findIndex(a => a.id === alertId);
    if (alertIndex !== -1) {
        const alert = guardiaDashboard.activeAlerts[alertIndex];
        guardiaDashboard.activeAlerts.splice(alertIndex, 1);
        guardiaDashboard.renderAlerts();

        guardiaDashboard.showNotification(`Alerta "${alert.title}" atendida`, 'success');
    }
}

function emergencyCall() {
    const modal = document.getElementById('emergency-modal');
    modal.classList.add('show');
}

function closeEmergencyModal() {
    const modal = document.getElementById('emergency-modal');
    modal.classList.remove('show');
}

function confirmEmergency() {
    // Simular llamada de emergencia
    guardiaDashboard.showNotification('Contactando con operador de emergencia...', 'info');
    closeEmergencyModal();

    setTimeout(() => {
        guardiaDashboard.showNotification('Operador contactado exitosamente', 'success');
    }, 2000);
}

function centerMap() {
    guardiaDashboard.showNotification('Mapa centrado en tu ubicación', 'info');
}

function toggleSatellite() {
    guardiaDashboard.showNotification('Vista satelital activada', 'info');
}

function logout() {
    if (confirm('¿Está seguro de que desea cerrar sesión?')) {
        // Limpiar datos de sesión
        if (typeof Storage !== 'undefined') {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            localStorage.clear();
        }

        // Redirigir a la página principal
        window.location.href = 'index.html';
    }
}

// Inicializar dashboard
const guardiaDashboard = new GuardiaDashboard();

// Agregar estilos para animaciones
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
    }

    .modal.show {
        display: flex;
    }

    .modal-content {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        max-width: 400px;
        width: 90%;
        text-align: center;
    }

    .modal-actions {
        display: flex;
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .btn-cancel, .btn-emergency {
        flex: 1;
        padding: 0.75rem;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 500;
    }

    .btn-cancel {
        background: #e2e8f0;
        color: #4a5568;
    }
`;
document.head.appendChild(style);

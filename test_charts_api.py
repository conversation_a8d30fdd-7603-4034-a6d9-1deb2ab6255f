#!/usr/bin/env python3
"""
Script simple para probar la API de gráficos
"""

import requests

def test_charts():
    # Obtener token
    print("🔐 Obteniendo token...")
    auth_response = requests.post("http://127.0.0.1:8000/api-token-auth/", {
        'username': 'operador',
        'password': 'operador123'
    })
    
    if auth_response.status_code != 200:
        print(f"❌ Error obteniendo token: {auth_response.status_code}")
        return
    
    token = auth_response.json()['token']
    print(f"✅ Token obtenido: {token[:20]}...")
    
    # Probar API de gráficos
    print("\n📊 Probando API de gráficos...")
    headers = {'Authorization': f'Token {token}'}
    
    response = requests.get("http://127.0.0.1:8000/api/analytics/charts/", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ API de gráficos funcionando!")
        print(f"📈 Gráficos disponibles: {len(data['charts'])}")
        
        for chart_name, chart_data in data['charts'].items():
            print(f"   • {chart_data['title']} ({chart_data['type']})")
            if 'labels' in chart_data:
                print(f"     Labels: {len(chart_data['labels'])} elementos")
            if 'datasets' in chart_data:
                print(f"     Datasets: {len(chart_data['datasets'])} series")
    else:
        print(f"❌ Error: {response.status_code}")
        print(response.text)

if __name__ == '__main__':
    test_charts()

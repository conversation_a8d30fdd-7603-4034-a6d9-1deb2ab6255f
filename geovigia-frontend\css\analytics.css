/**
 * Estilos para el Dashboard de Analytics
 * KPIs en tiempo real y visualizaciones
 */

/* Sección principal de analytics */
.analytics-section {
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    color: white;
}

/* Header de analytics */
.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.analytics-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.analytics-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.last-update {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

.btn-refresh {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-refresh:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.btn-refresh:active {
    transform: translateY(0);
}

/* Container de KPIs */
.kpis-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

/* Tarjetas de KPI */
.kpi-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    color: #333;
    position: relative;
    overflow: hidden;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
    opacity: 0.8;
}

.kpi-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Icono del KPI */
.kpi-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Contenido del KPI */
.kpi-content {
    flex: 1;
}

.kpi-value {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
    transition: transform 0.2s ease;
}

.kpi-label {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
    margin-bottom: 8px;
}

.kpi-change {
    font-size: 0.85rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-block;
}

.kpi-change.positive {
    background: #d4edda;
    color: #155724;
}

.kpi-change.negative {
    background: #f8d7da;
    color: #721c24;
}

.kpi-change.neutral {
    background: #e2e3e5;
    color: #383d41;
}

/* Iconos específicos por KPI */
#kpi-guards .kpi-icon {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

#kpi-properties .kpi-icon {
    background: linear-gradient(135deg, #2196F3, #1976D2);
}

#kpi-alerts .kpi-icon {
    background: linear-gradient(135deg, #FF9800, #F57C00);
}

#kpi-response .kpi-icon {
    background: linear-gradient(135deg, #9C27B0, #7B1FA2);
}

#kpi-routes .kpi-icon {
    background: linear-gradient(135deg, #607D8B, #455A64);
}

#kpi-users .kpi-icon {
    background: linear-gradient(135deg, #E91E63, #C2185B);
}

/* Estados del sistema */
.analytics-status {
    margin-top: 15px;
}

.loading-indicator {
    text-align: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 0.9rem;
}

.loading-indicator i {
    margin-right: 8px;
}

.error-indicator {
    text-align: center;
    padding: 10px;
    background: rgba(244, 67, 54, 0.2);
    border: 1px solid rgba(244, 67, 54, 0.3);
    border-radius: 8px;
    font-size: 0.9rem;
    color: #ffebee;
}

.error-indicator i {
    margin-right: 8px;
}

/* Animaciones */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.kpi-card.updating {
    animation: pulse 1s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .analytics-section {
        margin: 10px;
        padding: 15px;
    }

    .analytics-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .analytics-header h2 {
        font-size: 1.5rem;
    }

    .kpis-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .kpi-card {
        padding: 15px;
    }

    .kpi-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .kpi-value {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .analytics-section {
        margin: 5px;
        padding: 10px;
    }

    .kpi-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .kpi-content {
        width: 100%;
    }
}

/* Efectos de hover mejorados */
.kpi-card:hover .kpi-icon {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}

.kpi-card:hover .kpi-value {
    color: #667eea;
    transition: color 0.3s ease;
}

/* Indicador de actualización en tiempo real */
.real-time-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 8px;
    height: 8px;
    background: #4CAF50;
    border-radius: 50%;
    animation: blink 2s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Estilos para modo oscuro (opcional) */
@media (prefers-color-scheme: dark) {
    .kpi-card {
        background: rgba(255, 255, 255, 0.1);
        color: white;
    }

    .kpi-value {
        color: #ecf0f1;
    }

    .kpi-label {
        color: #bdc3c7;
    }
}

/* Estilos para Modal de Login */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.login-modal {
    background: white;
    border-radius: 15px;
    padding: 30px;
    max-width: 450px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    text-align: center;
    margin-bottom: 25px;
}

.modal-header h2 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-size: 1.8rem;
}

.modal-header p {
    color: #7f8c8d;
    margin: 0;
}

.login-form {
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
    text-align: center;
    margin-bottom: 15px;
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 6px;
    margin-top: 15px;
    border: 1px solid #f5c6cb;
}

.login-help {
    border-top: 1px solid #e0e0e0;
    padding-top: 20px;
}

.login-help h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-size: 1rem;
}

.test-users {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.test-user {
    background: #f8f9fa;
    padding: 10px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 0.9rem;
}

.test-user:hover {
    background: #e9ecef;
}

.test-user strong {
    color: #495057;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10001;
}

.toast {
    background: #333;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: toastSlideIn 0.3s ease-out;
}

.toast-success {
    background: #28a745;
}

.toast-error {
    background: #dc3545;
}

.toast-warning {
    background: #ffc107;
    color: #212529;
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== SECCIÓN DE GRÁFICOS ===== */

/* Sección principal de gráficos */
.charts-section {
    margin: 30px 0;
    padding: 25px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

/* Header de gráficos */
.charts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.charts-header h2 {
    margin: 0;
    font-size: 1.6rem;
    font-weight: 600;
    color: #2c3e50;
}

.charts-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.charts-update {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
}

/* Grid de gráficos */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

/* Tarjetas de gráficos */
.chart-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* Tamaños de tarjetas */
.chart-card.large {
    grid-column: span 8;
}

.chart-card.medium {
    grid-column: span 6;
}

.chart-card.small {
    grid-column: span 4;
}

/* Header de cada gráfico */
.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f5f5f5;
}

.chart-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.chart-period {
    font-size: 0.8rem;
    color: #95a5a6;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
}

/* Container de gráficos */
.chart-container {
    position: relative;
    height: 250px;
    width: 100%;
}

.chart-card.large .chart-container {
    height: 300px;
}

.chart-card.small .chart-container {
    height: 200px;
}

/* Estados de gráficos */
.charts-status {
    margin-top: 15px;
}

/* Responsive Design para gráficos */
@media (max-width: 1200px) {
    .chart-card.large {
        grid-column: span 12;
    }

    .chart-card.medium {
        grid-column: span 6;
    }

    .chart-card.small {
        grid-column: span 6;
    }
}

@media (max-width: 768px) {
    .charts-section {
        margin: 15px;
        padding: 15px;
    }

    .charts-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .charts-header h2 {
        font-size: 1.4rem;
    }

    .charts-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .chart-card.large,
    .chart-card.medium,
    .chart-card.small {
        grid-column: span 1;
    }

    .chart-card {
        padding: 15px;
    }

    .chart-container {
        height: 220px;
    }

    .chart-header {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .charts-section {
        margin: 10px;
        padding: 10px;
    }

    .chart-card {
        padding: 12px;
    }

    .chart-container {
        height: 200px;
    }

    .chart-header h3 {
        font-size: 1rem;
    }
}

/* Animaciones para gráficos */
.chart-card {
    animation: chartSlideIn 0.5s ease-out;
}

@keyframes chartSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Efectos de hover mejorados para gráficos */
.chart-card:hover .chart-header h3 {
    color: #667eea;
    transition: color 0.3s ease;
}

/* Indicadores de estado específicos para gráficos */
.charts-loading {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 0.9rem;
    color: #6c757d;
}

.charts-error {
    text-align: center;
    padding: 15px;
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    font-size: 0.9rem;
    color: #c53030;
}

/* Estilos para tooltips de Chart.js (opcional) */
.chartjs-tooltip {
    background: rgba(0, 0, 0, 0.8) !important;
    border-radius: 6px !important;
    color: white !important;
    font-size: 12px !important;
    padding: 8px 12px !important;
}

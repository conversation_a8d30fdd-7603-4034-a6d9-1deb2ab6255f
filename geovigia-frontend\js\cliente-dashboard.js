/**
 * Dashboard específico para clientes
 */

class ClienteDashboard {
    constructor() {
        this.currentAlert = null;
        this.guardLocation = null;
        this.propertyData = null;
        this.init();
    }

    init() {
        this.loadPropertyData();
        this.loadGuardInfo();
        this.startLocationTracking();
        this.loadRecentActivity();

        // Actualizar datos cada 30 segundos
        setInterval(() => {
            this.updateGuardLocation();
            this.loadRecentActivity();
        }, 30000);
    }

    async loadPropertyData() {
        try {
            // Simular carga de datos de la propiedad
            this.propertyData = {
                name: "Casa Residencial",
                address: "Av. Reforma 123, CDMX",
                isMonitored: true,
                coordinates: { lat: 19.4326, lng: -99.1332 }
            };

            document.getElementById('property-name').textContent = this.propertyData.name;
            document.getElementById('property-address').textContent = this.propertyData.address;
        } catch (error) {
            console.error('Error cargando datos de propiedad:', error);
        }
    }

    async loadGuardInfo() {
        try {
            // Simular carga de información del guardia
            const guardData = {
                name: "<PERSON> Seguridad",
                status: "En servicio",
                location: { lat: 19.4330, lng: -99.1335 },
                patrolCount: 3,
                lastPatrol: "15 min"
            };

            document.getElementById('guard-name').textContent = guardData.name;
            document.getElementById('guard-status').textContent = guardData.status;
            document.getElementById('patrol-count').textContent = guardData.patrolCount;
            document.getElementById('last-patrol').textContent = guardData.lastPatrol;

            this.guardLocation = guardData.location;
            this.updateDistance();
        } catch (error) {
            console.error('Error cargando información del guardia:', error);
        }
    }

    updateDistance() {
        if (this.guardLocation && this.propertyData) {
            // Calcular distancia simple (en un proyecto real usarías una API de mapas)
            const distance = this.calculateDistance(
                this.propertyData.coordinates,
                this.guardLocation
            );

            document.getElementById('guard-distance').textContent = `${distance}m de tu propiedad`;
        }
    }

    calculateDistance(coord1, coord2) {
        // Fórmula simple para calcular distancia (aproximada)
        const R = 6371e3; // Radio de la Tierra en metros
        const φ1 = coord1.lat * Math.PI/180;
        const φ2 = coord2.lat * Math.PI/180;
        const Δφ = (coord2.lat-coord1.lat) * Math.PI/180;
        const Δλ = (coord2.lng-coord1.lng) * Math.PI/180;

        const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                Math.cos(φ1) * Math.cos(φ2) *
                Math.sin(Δλ/2) * Math.sin(Δλ/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

        return Math.round(R * c);
    }

    startLocationTracking() {
        // Simular actualizaciones de ubicación del guardia
        setInterval(() => {
            this.updateGuardLocation();
        }, 10000); // Cada 10 segundos
    }

    updateGuardLocation() {
        // Simular movimiento del guardia
        if (this.guardLocation) {
            this.guardLocation.lat += (Math.random() - 0.5) * 0.0001;
            this.guardLocation.lng += (Math.random() - 0.5) * 0.0001;
            this.updateDistance();
        }
    }

    async loadRecentActivity() {
        try {
            const activities = [
                {
                    icon: 'fas fa-shield-alt',
                    text: 'Patrullaje completado',
                    time: 'Hace 15 minutos'
                },
                {
                    icon: 'fas fa-map-marker-alt',
                    text: 'Guardia en perímetro',
                    time: 'Hace 1 hora'
                },
                {
                    icon: 'fas fa-check-circle',
                    text: 'Revisión de seguridad',
                    time: 'Hace 2 horas'
                }
            ];

            const activityList = document.getElementById('activity-list');
            activityList.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="${activity.icon}"></i>
                    </div>
                    <div class="activity-content">
                        <p>${activity.text}</p>
                        <span class="activity-time">${activity.time}</span>
                    </div>
                </div>
            `).join('');
        } catch (error) {
            console.error('Error cargando actividad reciente:', error);
        }
    }
}

// Funciones globales para los botones
function sendAlert(alertType) {
    const alertMessages = {
        'casa_sola': '¿Confirma que va a estar fuera de casa? Esto notificará al guardia que la propiedad estará desocupada.',
        'actitud_sospechosa': '¿Ha observado actividad sospechosa? Esto enviará una alerta de prioridad alta al guardia.',
        'solicitar_ayuda': '¿Necesita ayuda inmediata? Esto enviará una alerta de emergencia al guardia.',
        'llegando_casa': '¿Está llegando a casa? Esto notificará al guardia de su llegada.'
    };

    const modal = document.getElementById('alert-modal');
    const message = document.getElementById('alert-message');

    message.textContent = alertMessages[alertType];
    clienteDashboard.currentAlert = alertType;

    modal.classList.add('show');
}

function closeModal() {
    const modal = document.getElementById('alert-modal');
    modal.classList.remove('show');
    clienteDashboard.currentAlert = null;
}

async function confirmAlert() {
    if (!clienteDashboard.currentAlert) return;

    try {
        // Aquí enviarías la alerta al backend
        console.log('Enviando alerta:', clienteDashboard.currentAlert);

        // Simular envío exitoso
        showNotification('Alerta enviada correctamente al guardia', 'success');

        // Agregar a actividad reciente
        addToRecentActivity(clienteDashboard.currentAlert);

        closeModal();
    } catch (error) {
        console.error('Error enviando alerta:', error);
        showNotification('Error al enviar la alerta', 'error');
    }
}

function addToRecentActivity(alertType) {
    const alertTexts = {
        'casa_sola': 'Notificación: Casa sola enviada',
        'actitud_sospechosa': 'Alerta: Actitud sospechosa reportada',
        'solicitar_ayuda': 'Emergencia: Solicitud de ayuda enviada',
        'llegando_casa': 'Notificación: Llegada a casa'
    };

    const activityList = document.getElementById('activity-list');
    const newActivity = document.createElement('div');
    newActivity.className = 'activity-item';
    newActivity.innerHTML = `
        <div class="activity-icon">
            <i class="fas fa-bell"></i>
        </div>
        <div class="activity-content">
            <p>${alertTexts[alertType]}</p>
            <span class="activity-time">Ahora</span>
        </div>
    `;

    activityList.insertBefore(newActivity, activityList.firstChild);
}

function showNotification(message, type) {
    // Crear notificación temporal
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#48bb78' : '#f56565'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        z-index: 1001;
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function logout() {
    if (confirm('¿Está seguro de que desea cerrar sesión?')) {
        // Limpiar datos de sesión
        if (typeof Storage !== 'undefined') {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            localStorage.clear();
        }

        // Redirigir a la página principal
        window.location.href = 'index.html';
    }
}

// Inicializar dashboard
const clienteDashboard = new ClienteDashboard();

// Agregar estilos para animaciones
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
`;
document.head.appendChild(style);

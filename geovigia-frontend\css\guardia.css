/* ===== GUARDIA DASHBOARD STYLES ===== */

.guardia-dashboard {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    min-height: 100vh;
    font-family: 'Inter', sans-serif;
}

/* Header */
.guardia-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #11998e;
}

.guard-status {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logout-btn {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: #ff5252;
    transform: translateY(-2px);
}

.emergency-btn {
    background: #ff4757;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: pulse-emergency 2s infinite;
}

@keyframes pulse-emergency {
    0%, 100% { box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7); }
    50% { box-shadow: 0 0 0 10px rgba(255, 71, 87, 0); }
}

.emergency-btn:hover {
    background: #ff3742;
    transform: scale(1.05);
}

/* Main Content */
.guardia-main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

/* Route Overview */
.route-overview {
    grid-column: 1 / -1;
}

.route-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.route-stats {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #11998e;
}

.stat-label {
    font-size: 0.875rem;
    color: #718096;
}

.start-route-btn {
    background: linear-gradient(135deg, #11998e, #38ef7d);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.start-route-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(17, 153, 142, 0.3);
}

/* Properties List */
.properties-list {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.property-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.property-item:hover {
    background: #f7fafc;
    transform: translateX(4px);
}

.property-item.alert {
    border-left: 4px solid #ff4757;
    background: #fff5f5;
}

.property-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.property-item.occupied .property-icon {
    background: #c6f6d5;
    color: #22543d;
}

.property-item.empty .property-icon {
    background: #fed7d7;
    color: #742a2a;
}

.property-info {
    flex: 1;
}

.property-info h4 {
    margin: 0 0 0.25rem 0;
    color: #2d3748;
}

.property-info p {
    margin: 0 0 0.5rem 0;
    color: #718096;
    font-size: 0.875rem;
}

.occupancy-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.occupancy-status.occupied {
    background: #c6f6d5;
    color: #22543d;
}

.occupancy-status.empty {
    background: #fed7d7;
    color: #742a2a;
}

.alert-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #ff4757;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.check-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: #11998e;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.check-btn:hover {
    background: #0f7c75;
    transform: scale(1.1);
}

.check-btn.priority {
    background: #ff4757;
    animation: pulse 1s infinite;
}

/* Live Alerts */
.alerts-container {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-item.high-priority {
    border-left-color: #ff4757;
    background: #fff5f5;
}

.alert-item.medium-priority {
    border-left-color: #ffa502;
    background: #fffbf0;
}

.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.high-priority .alert-icon {
    background: #ff4757;
}

.medium-priority .alert-icon {
    background: #ffa502;
}

.alert-content {
    flex: 1;
}

.alert-content h4 {
    margin: 0 0 0.25rem 0;
    color: #2d3748;
}

.alert-content p {
    margin: 0 0 0.25rem 0;
    color: #4a5568;
}

.alert-time {
    font-size: 0.75rem;
    color: #718096;
}

.respond-btn {
    background: #11998e;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.respond-btn:hover {
    background: #0f7c75;
}

/* Map Section */
.map-container {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.route-map {
    height: 300px;
    background: #f7fafc;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.map-placeholder {
    text-align: center;
    color: #718096;
}

.map-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #11998e;
}

.map-controls {
    display: flex;
    gap: 1rem;
}

.map-btn {
    background: #11998e;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.map-btn:hover {
    background: #0f7c75;
}

/* Emergency Modal */
.modal.emergency .modal-content {
    border-top: 4px solid #ff4757;
}

.btn-emergency {
    background: #ff4757;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .guardia-main {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .route-card {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .route-stats {
        justify-content: center;
    }
}

from django.contrib import admin
from .models import Property, PropertyOwnership, PropertyPerimeter


class PropertyOwnershipInline(admin.TabularInline):
    model = PropertyOwnership
    extra = 1
    verbose_name = 'Asignación'
    verbose_name_plural = 'Asignaciones'


class PropertyPerimeterInline(admin.StackedInline):
    model = PropertyPerimeter
    can_delete = False
    verbose_name_plural = 'Perímetro'


@admin.register(Property)
class PropertyAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'property_type', 'city', 'status', 'is_monitored',
        'priority_level', 'created_at'
    ]
    list_filter = [
        'property_type', 'status', 'is_monitored', 'priority_level',
        'city', 'created_at'
    ]
    search_fields = ['name', 'address', 'neighborhood', 'city']
    ordering = ['-created_at']

    fieldsets = (
        ('Información Básica', {
            'fields': ('name', 'property_type', 'description')
        }),
        ('Ubicación', {
            'fields': ('address', 'neighborhood', 'city', 'postal_code',
                      'latitude', 'longitude')
        }),
        ('Detalles', {
            'fields': ('area_size', 'floors')
        }),
        ('Configuración', {
            'fields': ('status', 'is_monitored', 'priority_level')
        }),
        ('Contacto de Emergencia', {
            'fields': ('emergency_contact_name', 'emergency_contact_phone'),
            'classes': ('collapse',)
        }),
        ('Metadatos', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['created_at', 'updated_at']
    inlines = [PropertyOwnershipInline, PropertyPerimeterInline]

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related('ownerships__client')


@admin.register(PropertyOwnership)
class PropertyOwnershipAdmin(admin.ModelAdmin):
    list_display = [
        'client', 'property', 'ownership_type', 'is_primary',
        'can_receive_alerts', 'created_at'
    ]
    list_filter = [
        'ownership_type', 'is_primary', 'can_receive_alerts', 'created_at'
    ]
    search_fields = [
        'client__username', 'client__first_name', 'client__last_name',
        'property__name', 'property__address'
    ]
    ordering = ['-created_at']

    fieldsets = (
        ('Asignación', {
            'fields': ('client', 'property', 'ownership_type')
        }),
        ('Configuración', {
            'fields': ('is_primary', 'can_receive_alerts')
        }),
        ('Notas', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
        ('Metadatos', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['created_at']


@admin.register(PropertyPerimeter)
class PropertyPerimeterAdmin(admin.ModelAdmin):
    list_display = [
        'property', 'perimeter_type', 'is_active', 'alert_on_entry',
        'alert_on_exit', 'created_at'
    ]
    list_filter = [
        'perimeter_type', 'is_active', 'alert_on_entry', 'alert_on_exit',
        'created_at'
    ]
    search_fields = ['property__name', 'description']
    ordering = ['-created_at']

    fieldsets = (
        ('Propiedad', {
            'fields': ('property',)
        }),
        ('Configuración del Perímetro', {
            'fields': ('perimeter_type', 'description')
        }),
        ('Perímetro Circular', {
            'fields': ('radius_meters',),
            'classes': ('collapse',)
        }),
        ('Perímetro Rectangular', {
            'fields': ('width_meters', 'height_meters'),
            'classes': ('collapse',)
        }),
        ('Perímetro Polígono', {
            'fields': ('polygon_coordinates',),
            'classes': ('collapse',)
        }),
        ('Alertas', {
            'fields': ('is_active', 'alert_on_entry', 'alert_on_exit')
        }),
        ('Metadatos', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['created_at', 'updated_at']

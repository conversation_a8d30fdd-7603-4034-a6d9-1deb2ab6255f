from rest_framework import serializers
from .models import Notification, PushSubscription, WebSocketConnection


class NotificationSerializer(serializers.ModelSerializer):
    """Serializer para notificaciones"""
    
    sender_username = serializers.Char<PERSON>ield(source='sender.username', read_only=True)
    recipient_username = serializers.CharField(source='recipient.username', read_only=True)
    
    class Meta:
        model = Notification
        fields = [
            'id', 'title', 'message', 'notification_type', 'priority',
            'is_read', 'is_sent', 'extra_data', 'created_at', 'sent_at',
            'read_at', 'expires_at', 'sender_username', 'recipient_username'
        ]
        read_only_fields = [
            'id', 'created_at', 'sent_at', 'read_at', 'is_sent',
            'sender_username', 'recipient_username'
        ]


class PushSubscriptionSerializer(serializers.ModelSerializer):
    """Serializer para suscripciones push"""
    
    class Meta:
        model = PushSubscription
        fields = [
            'id', 'endpoint', 'p256dh_key', 'auth_key', 'user_agent',
            'is_active', 'created_at', 'last_used'
        ]
        read_only_fields = ['id', 'created_at', 'last_used']


class WebSocketConnectionSerializer(serializers.ModelSerializer):
    """Serializer para conexiones WebSocket"""
    
    username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = WebSocketConnection
        fields = [
            'id', 'username', 'channel_name', 'connected_at',
            'last_activity', 'user_agent', 'ip_address'
        ]
        read_only_fields = ['id', 'connected_at', 'last_activity']


class CreateNotificationSerializer(serializers.Serializer):
    """Serializer para crear notificaciones"""
    
    recipient_id = serializers.IntegerField()
    title = serializers.CharField(max_length=200)
    message = serializers.CharField()
    notification_type = serializers.CharField(max_length=20, required=False)
    priority = serializers.CharField(max_length=10, required=False)
    extra_data = serializers.JSONField(required=False)
    expires_at = serializers.DateTimeField(required=False)


class AlertSerializer(serializers.Serializer):
    """Serializer para alertas de emergencia"""
    
    recipient_id = serializers.IntegerField(required=False)
    title = serializers.CharField(max_length=200, required=False)
    message = serializers.CharField(required=False)
    alert_type = serializers.CharField(max_length=50, required=False)
    location = serializers.JSONField(required=False)


class LocationUpdateSerializer(serializers.Serializer):
    """Serializer para actualizaciones de ubicación"""
    
    latitude = serializers.FloatField()
    longitude = serializers.FloatField()
    accuracy = serializers.FloatField(required=False)
    heading = serializers.FloatField(required=False)
    speed = serializers.FloatField(required=False)
    timestamp = serializers.DateTimeField(required=False)

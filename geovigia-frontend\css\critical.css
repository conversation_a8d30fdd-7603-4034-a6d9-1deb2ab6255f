/* Critical CSS - Estilos esenciales que deben cargar primero */

/* Reset básico crítico */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #0f172a;
    background-color: #f8fafc;
    overflow-x: hidden;
}

/* Login Screen Critical */
.login-screen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 1000 !important;
}

.login-container {
    background: #ffffff !important;
    padding: 2rem !important;
    border-radius: 16px !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1) !important;
    width: 100% !important;
    max-width: 400px !important;
    margin: 1rem !important;
}

.login-header {
    text-align: center !important;
    margin-bottom: 2rem !important;
}

.login-header .logo {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    margin-bottom: 1rem !important;
}

.login-header .logo i {
    font-size: 2rem !important;
    color: #2563eb !important;
}

.login-header h1,
.login-header h2 {
    font-size: 1.75rem !important;
    font-weight: 700 !important;
    color: #0f172a !important;
    margin: 0 0 0.5rem 0 !important;
}

.login-header p {
    color: #475569 !important;
    font-size: 0.875rem !important;
    margin: 0 !important;
}

/* Form Elements Critical */
.login-form {
    margin-bottom: 2rem !important;
}

.form-group {
    margin-bottom: 1.5rem !important;
}

.form-group label {
    display: block !important;
    margin-bottom: 0.5rem !important;
    color: #0f172a !important;
    font-weight: 500 !important;
}

.input-group {
    position: relative !important;
}

.input-group i {
    position: absolute !important;
    left: 1rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    color: #475569 !important;
}

.input-group input {
    width: 100% !important;
    padding: 1rem 1rem 1rem 3rem !important;
    border: 2px solid #e2e8f0 !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    transition: border-color 0.3s !important;
    box-sizing: border-box !important;
}

.input-group input:focus {
    outline: none !important;
    border-color: #2563eb !important;
}

.login-btn {
    width: 100% !important;
    padding: 1rem !important;
    background: #2563eb !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: background-color 0.3s !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
}

.login-btn:hover {
    background: #1d4ed8 !important;
}

.login-btn:disabled {
    background: #94a3b8 !important;
    cursor: not-allowed !important;
}

/* Main App Layout Critical */
.main-app {
    display: flex !important;
    height: 100vh !important;
    overflow: hidden !important;
}

/* Sidebar Critical */
.sidebar {
    width: 280px !important;
    background: #1e293b !important;
    color: #ffffff !important;
    display: flex !important;
    flex-direction: column !important;
    transition: width 0.25s ease-in-out !important;
    position: relative !important;
    z-index: 100 !important;
}

.sidebar.collapsed {
    width: 80px !important;
}

.sidebar-header {
    padding: 1.5rem !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    height: 70px !important;
}

/* Main Content Critical */
.main-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
}

/* Header Critical */
.header {
    height: 70px !important;
    background: #ffffff !important;
    border-bottom: 1px solid #e2e8f0 !important;
    padding: 0 2rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

/* Content Container Critical */
.content-container {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 2rem !important;
}

.page-content {
    display: none !important;
}

.page-content.active {
    display: block !important;
}

/* Utilities Critical */
.hidden {
    display: none !important;
}

/* Loading States */
.loading {
    opacity: 0.6 !important;
    pointer-events: none !important;
}

/* Error States */
.error {
    color: #ef4444 !important;
    background: #fef2f2 !important;
    border: 1px solid #fecaca !important;
    padding: 1rem !important;
    border-radius: 8px !important;
    margin: 1rem 0 !important;
}

/* Success States */
.success {
    color: #10b981 !important;
    background: #f0fdf4 !important;
    border: 1px solid #bbf7d0 !important;
    padding: 1rem !important;
    border-radius: 8px !important;
    margin: 1rem 0 !important;
}

/* Responsive Critical */
@media (max-width: 768px) {
    .sidebar {
        width: 80px !important;
    }
    
    .content-container {
        padding: 1rem !important;
    }
    
    .header {
        padding: 0 1rem !important;
    }
}

/* Animation Critical */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Ensure visibility */
.main-app,
.sidebar,
.main-content,
.header,
.content-container {
    visibility: visible !important;
    opacity: 1 !important;
}

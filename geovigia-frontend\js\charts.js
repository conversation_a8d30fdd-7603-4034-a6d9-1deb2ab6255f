/**
 * Sistema de Gráficos para GeoVigia
 * Maneja la creación y actualización de gráficos usando Chart.js
 */

// Configuración global de Chart.js
Chart.defaults.font.family = "'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
Chart.defaults.font.size = 12;
Chart.defaults.color = '#64748b';

// Colores del tema
const CHART_COLORS = {
    primary: '#3b82f6',
    secondary: '#8b5cf6',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#06b6d4',
    light: '#f8fafc',
    dark: '#1e293b'
};

// Paleta de colores para múltiples series
const COLOR_PALETTE = [
    '#3b82f6', '#8b5cf6', '#10b981', '#f59e0b', '#ef4444',
    '#06b6d4', '#ec4899', '#84cc16', '#f97316', '#6366f1'
];

/**
 * Crear gráfico de líneas
 */
function createLineChart(canvasId, data, options = {}) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) {
        console.error(`Canvas ${canvasId} no encontrado`);
        return null;
    }

    const defaultOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            },
            tooltip: {
                mode: 'index',
                intersect: false,
            }
        },
        scales: {
            x: {
                display: true,
                grid: {
                    color: '#e2e8f0'
                }
            },
            y: {
                display: true,
                grid: {
                    color: '#e2e8f0'
                }
            }
        },
        interaction: {
            mode: 'nearest',
            axis: 'x',
            intersect: false
        }
    };

    const config = {
        type: 'line',
        data: data,
        options: { ...defaultOptions, ...options }
    };

    return new Chart(ctx, config);
}

/**
 * Crear gráfico de barras
 */
function createBarChart(canvasId, data, options = {}) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) {
        console.error(`Canvas ${canvasId} no encontrado`);
        return null;
    }

    const defaultOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            x: {
                grid: {
                    color: '#e2e8f0'
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    color: '#e2e8f0'
                }
            }
        }
    };

    const config = {
        type: 'bar',
        data: data,
        options: { ...defaultOptions, ...options }
    };

    return new Chart(ctx, config);
}

/**
 * Crear gráfico de dona
 */
function createDoughnutChart(canvasId, data, options = {}) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) {
        console.error(`Canvas ${canvasId} no encontrado`);
        return null;
    }

    const defaultOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'right',
            }
        }
    };

    const config = {
        type: 'doughnut',
        data: data,
        options: { ...defaultOptions, ...options }
    };

    return new Chart(ctx, config);
}

/**
 * Crear gráfico de área
 */
function createAreaChart(canvasId, data, options = {}) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) {
        console.error(`Canvas ${canvasId} no encontrado`);
        return null;
    }

    // Configurar datasets para área
    if (data.datasets) {
        data.datasets.forEach(dataset => {
            dataset.fill = true;
            dataset.backgroundColor = dataset.backgroundColor || CHART_COLORS.primary + '20';
        });
    }

    return createLineChart(canvasId, data, options);
}

/**
 * Actualizar datos de un gráfico
 */
function updateChartData(chart, newData) {
    if (!chart) return;

    chart.data.labels = newData.labels;
    chart.data.datasets = newData.datasets;
    chart.update();
}

/**
 * Destruir gráfico
 */
function destroyChart(chart) {
    if (chart) {
        chart.destroy();
    }
}

/**
 * Generar datos de ejemplo para gráficos
 */
function generateSampleData(type = 'line', points = 7) {
    const labels = [];
    const data = [];

    for (let i = 0; i < points; i++) {
        labels.push(`Punto ${i + 1}`);
        data.push(Math.floor(Math.random() * 100));
    }

    switch (type) {
        case 'line':
        case 'area':
            return {
                labels: labels,
                datasets: [{
                    label: 'Datos de ejemplo',
                    data: data,
                    borderColor: CHART_COLORS.primary,
                    backgroundColor: CHART_COLORS.primary + '20',
                    tension: 0.4
                }]
            };

        case 'bar':
            return {
                labels: labels,
                datasets: [{
                    label: 'Datos de ejemplo',
                    data: data,
                    backgroundColor: COLOR_PALETTE.slice(0, points)
                }]
            };

        case 'doughnut':
            return {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: COLOR_PALETTE.slice(0, points)
                }]
            };

        default:
            return { labels: [], datasets: [] };
    }
}

/**
 * Configurar tema oscuro para gráficos
 */
function setDarkTheme() {
    Chart.defaults.color = '#94a3b8';
    Chart.defaults.borderColor = '#334155';
}

/**
 * Configurar tema claro para gráficos
 */
function setLightTheme() {
    Chart.defaults.color = '#64748b';
    Chart.defaults.borderColor = '#e2e8f0';
}

// Exponer funciones globalmente
window.createLineChart = createLineChart;
window.createBarChart = createBarChart;
window.createDoughnutChart = createDoughnutChart;
window.createAreaChart = createAreaChart;
window.updateChartData = updateChartData;
window.destroyChart = destroyChart;
window.generateSampleData = generateSampleData;
window.setDarkTheme = setDarkTheme;
window.setLightTheme = setLightTheme;
window.CHART_COLORS = CHART_COLORS;
window.COLOR_PALETTE = COLOR_PALETTE;

console.log('📊 Sistema de gráficos inicializado');

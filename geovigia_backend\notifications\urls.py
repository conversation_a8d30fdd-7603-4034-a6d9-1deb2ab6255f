from django.urls import path
from . import views

app_name = 'notifications'

urlpatterns = [
    # Notificaciones básicas
    path('', views.get_notifications, name='list'),
    path('create/', views.create_notification, name='create'),
    path('unread-count/', views.get_unread_count, name='unread_count'),
    path('mark-all-read/', views.mark_all_read, name='mark_all_read'),
    path('<uuid:notification_id>/read/', views.mark_notification_read, name='mark_read'),
    
    # Push notifications
    path('push/subscribe/', views.subscribe_push, name='push_subscribe'),
    
    # Alertas
    path('alert/', views.send_alert, name='send_alert'),
]

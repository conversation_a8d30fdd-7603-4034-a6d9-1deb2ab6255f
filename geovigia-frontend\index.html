<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>GeoVigia - Sistema de Vigilancia Inteligente</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">

    <!-- Estilos críticos (cargan primero) -->
    <link rel="stylesheet" href="css/critical.css?v=2.0">

    <!-- Estilos principales -->
    <link rel="stylesheet" href="css/styles.css?v=2.0">
    <link rel="stylesheet" href="css/components.css?v=2.0">
    <link rel="stylesheet" href="css/analytics.css?v=2.0">
    <link rel="stylesheet" href="css/advanced-analytics.css?v=2.0">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <!-- Leaflet Draw CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css"/>

    <!-- Chart.js CSS (opcional) -->
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .heatmap-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
            border-radius: 12px;
            overflow: hidden;
        }

        /* Estilos para pantalla de login */
        .login-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .login-container {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            margin: 1rem;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .login-header h2 {
            color: #333;
            margin: 0 0 0.5rem 0;
            font-size: 1.5rem;
        }

        .login-header p {
            color: #666;
            margin: 0;
        }

        .login-form {
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        .input-group {
            position: relative;
        }

        .input-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .input-group input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }

        .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 1rem;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .login-btn:hover {
            background: #5a6fd8;
        }

        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .login-demo-info {
            border-top: 1px solid #e0e0e0;
            padding-top: 1.5rem;
        }

        .login-demo-info h4 {
            margin: 0 0 1rem 0;
            color: #333;
            text-align: center;
        }

        .demo-credentials {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .credential-item {
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .credential-item:hover {
            background: #e9ecef;
        }

        .credential-item i {
            color: #667eea;
            width: 20px;
        }

        .demo-note {
            text-align: center;
            font-size: 0.875rem;
            color: #666;
            margin: 1rem 0 0 0;
        }

        /* Toast notifications */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
        }

        .toast {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 250px;
            animation: slideInRight 0.3s ease;
        }

        .toast-success {
            border-left: 4px solid #28a745;
        }

        .toast-error {
            border-left: 4px solid #dc3545;
        }

        .toast-info {
            border-left: 4px solid #17a2b8;
        }

        .toast-warning {
            border-left: 4px solid #ffc107;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="login-screen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <span>GeoVigia</span>
                </div>
                <h2>Sistema de Vigilancia Inteligente</h2>
                <p>Ingresa tus credenciales para acceder</p>
            </div>

            <form id="login-form" class="login-form">
                <div class="form-group">
                    <label for="username">Usuario</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" required placeholder="Ingresa tu usuario">
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">Contraseña</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required placeholder="Ingresa tu contraseña">
                    </div>
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    Iniciar Sesión
                </button>
            </form>

            <div class="login-demo-info">
                <h4>Credenciales de Demo</h4>
                <div class="demo-credentials">
                    <div class="credential-item" onclick="fillCredentials('operador', 'operador123')">
                        <i class="fas fa-user-tie"></i>
                        <span><strong>Operador:</strong> operador / operador123</span>
                    </div>
                    <div class="credential-item" onclick="fillCredentials('guardia', 'guardia123')">
                        <i class="fas fa-user-shield"></i>
                        <span><strong>Guardia:</strong> guardia / guardia123</span>
                    </div>
                    <div class="credential-item" onclick="fillCredentials('cliente', 'cliente123')">
                        <i class="fas fa-user"></i>
                        <span><strong>Cliente:</strong> cliente / cliente123</span>
                    </div>
                </div>
                <p class="demo-note">Haz click en cualquier credencial para llenar automáticamente</p>
            </div>
        </div>
    </div>

    <!-- Main Application (hidden initially) -->
    <div id="main-app" class="main-app" style="display: none;">
        <!-- Header -->
    <header class="main-header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-shield-alt"></i>
                <span>GeoVigia</span>
            </div>
            <nav class="main-nav">
                <a href="#dashboard" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
                <a href="#users" class="nav-link" data-section="users">
                    <i class="fas fa-users"></i>
                    Usuarios
                </a>
                <a href="#properties" class="nav-link" data-section="properties">
                    <i class="fas fa-home"></i>
                    Propiedades
                </a>
                <a href="#routes" class="nav-link" data-section="routes">
                    <i class="fas fa-route"></i>
                    Rutas
                </a>
                <a href="#maps" class="nav-link" data-section="maps">
                    <i class="fas fa-map"></i>
                    Mapas
                </a>
                <a href="#monitoring" class="nav-link" data-section="monitoring">
                    <i class="fas fa-eye"></i>
                    Monitoreo
                </a>
                <a href="#notifications" class="nav-link" data-section="notifications">
                    <i class="fas fa-bell"></i>
                    Notificaciones
                </a>
                <a href="#reports" class="nav-link" data-section="reports">
                    <i class="fas fa-chart-bar"></i>
                    Reportes
                </a>
                <a href="#analytics" class="nav-link" data-section="analytics">
                    <i class="fas fa-chart-line"></i>
                    Analytics
                </a>
            </nav>
            <div class="user-menu">
                <div class="user-info">
                    <span id="user-name">Operador</span>
                    <i class="fas fa-user-circle"></i>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Section -->
        <section id="dashboard-section" class="content-section active">
            <div class="section-header">
                <h1><i class="fas fa-tachometer-alt"></i> Dashboard Principal</h1>
                <p>Panel de control del sistema GeoVigia</p>
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="card-content">
                        <h3>Usuarios Activos</h3>
                        <div class="card-number" id="active-users">24</div>
                        <p>Guardias en servicio</p>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="card-content">
                        <h3>Propiedades</h3>
                        <div class="card-number" id="total-properties">156</div>
                        <p>Bajo vigilancia</p>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-icon">
                        <i class="fas fa-route"></i>
                    </div>
                    <div class="card-content">
                        <h3>Rutas Activas</h3>
                        <div class="card-number" id="active-routes">8</div>
                        <p>En ejecución</p>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="card-content">
                        <h3>Alertas Hoy</h3>
                        <div class="card-number" id="today-alerts">3</div>
                        <p>Reportes recibidos</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h2>Acciones Rápidas</h2>
                <div class="actions-grid">
                    <button class="action-btn" onclick="navigateToSection('users')">
                        <i class="fas fa-user-plus"></i>
                        Nuevo Usuario
                    </button>
                    <button class="action-btn" onclick="navigateToSection('properties')">
                        <i class="fas fa-home"></i>
                        Nueva Propiedad
                    </button>
                    <button class="action-btn" onclick="navigateToSection('routes')">
                        <i class="fas fa-route"></i>
                        Nueva Ruta
                    </button>
                    <button class="action-btn" onclick="navigateToSection('maps')">
                        <i class="fas fa-map"></i>
                        Ver Mapas
                    </button>
                </div>
            </div>
        </section>

        <!-- Users Section -->
        <section id="users-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-users"></i> Gestión de Usuarios</h1>
                <button class="btn btn-primary" onclick="showCreateUserModal()">
                    <i class="fas fa-plus"></i> Nuevo Usuario
                </button>
            </div>
            <div id="users-content">
                <!-- Content will be loaded by users.js -->
            </div>
        </section>

        <!-- Properties Section -->
        <section id="properties-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-home"></i> Gestión de Propiedades</h1>
                <button class="btn btn-primary" onclick="showCreatePropertyModal()">
                    <i class="fas fa-plus"></i> Nueva Propiedad
                </button>
            </div>
            <div id="properties-content">
                <!-- Content will be loaded by properties.js -->
            </div>
        </section>

        <!-- Routes Section -->
        <section id="routes-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-route"></i> Gestión de Rutas</h1>
                <button class="btn btn-primary" onclick="showCreateRouteModal()">
                    <i class="fas fa-plus"></i> Nueva Ruta
                </button>
            </div>
            <div id="routes-content">
                <!-- Content will be loaded by routes.js -->
            </div>
        </section>

        <!-- Maps Section -->
        <section id="maps-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-map"></i> Mapas Interactivos</h1>
                <div class="maps-controls">
                    <button class="btn btn-secondary map-mode-btn active" data-mode="view">
                        <i class="fas fa-eye"></i> Ver
                    </button>
                    <button class="btn btn-secondary map-mode-btn" data-mode="perimeter">
                        <i class="fas fa-draw-polygon"></i> Perímetro
                    </button>
                    <button class="btn btn-secondary map-mode-btn" data-mode="route">
                        <i class="fas fa-route"></i> Ruta
                    </button>
                    <button class="btn btn-primary" onclick="geoVigiaMaps?.centerOnProperties()">
                        <i class="fas fa-crosshairs"></i> Centrar
                    </button>
                </div>
            </div>
            <div class="maps-content">
                <div id="map-container" class="map-container">
                    <!-- Leaflet map will be initialized here -->
                </div>
            </div>
        </section>

        <!-- Monitoring Section -->
        <section id="monitoring-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-eye"></i> Monitoreo en Tiempo Real</h1>
            </div>
            <div id="monitoring-content">
                <!-- Content will be loaded by monitoring.js -->
            </div>
        </section>

        <!-- Notifications Section -->
        <section id="notifications-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-bell"></i> Centro de Notificaciones</h1>
                <div class="notifications-controls">
                    <button class="btn btn-secondary" onclick="geoVigiaNotifications?.markAllAsRead()">
                        <i class="fas fa-check-double"></i> Marcar Todas como Leídas
                    </button>
                    <button class="btn btn-primary" onclick="showCreateNotificationModal()">
                        <i class="fas fa-plus"></i> Nueva Notificación
                    </button>
                </div>
            </div>

            <div class="notifications-dashboard">
                <!-- Estadísticas de Notificaciones -->
                <div class="notifications-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Total</h3>
                            <div class="stat-number" id="total-notifications">0</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-envelope-open"></i>
                        </div>
                        <div class="stat-content">
                            <h3>No Leídas</h3>
                            <div class="stat-number" id="unread-notifications">0</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Alertas</h3>
                            <div class="stat-number" id="alert-notifications">0</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Conectados</h3>
                            <div class="stat-number" id="connected-users">0</div>
                        </div>
                    </div>
                </div>

                <!-- Filtros de Notificaciones -->
                <div class="notifications-filters">
                    <div class="filter-group">
                        <label>Tipo:</label>
                        <select id="notification-type-filter">
                            <option value="">Todos</option>
                            <option value="info">Información</option>
                            <option value="alert">Alerta</option>
                            <option value="warning">Advertencia</option>
                            <option value="emergency">Emergencia</option>
                            <option value="route_update">Actualización de Ruta</option>
                            <option value="property_alert">Alerta de Propiedad</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label>Estado:</label>
                        <select id="notification-status-filter">
                            <option value="">Todos</option>
                            <option value="unread">No Leídas</option>
                            <option value="read">Leídas</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label>Prioridad:</label>
                        <select id="notification-priority-filter">
                            <option value="">Todas</option>
                            <option value="low">Baja</option>
                            <option value="normal">Normal</option>
                            <option value="high">Alta</option>
                            <option value="critical">Crítica</option>
                        </select>
                    </div>

                    <button class="btn btn-secondary" onclick="applyNotificationFilters()">
                        <i class="fas fa-filter"></i> Aplicar Filtros
                    </button>
                </div>

                <!-- Lista de Notificaciones -->
                <div class="notifications-list-container">
                    <div id="notifications-list" class="notifications-list">
                        <!-- Las notificaciones se cargarán aquí dinámicamente -->
                    </div>

                    <div class="notifications-pagination">
                        <button class="btn btn-secondary" id="load-more-notifications">
                            <i class="fas fa-chevron-down"></i> Cargar Más
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-chart-bar"></i> Reportes y Estadísticas</h1>
            </div>
            <div id="reports-content">
                <!-- Content will be loaded by reports.js -->
            </div>
        </section>

        <!-- Advanced Analytics Section -->
        <section id="analytics-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-chart-line"></i> Analytics Avanzados</h1>
                <div class="analytics-controls">
                    <button class="btn btn-secondary" onclick="initHeatmaps()">
                        <i class="fas fa-fire"></i> Heatmaps
                    </button>
                    <button class="btn btn-secondary" onclick="initAdvancedCharts()">
                        <i class="fas fa-chart-area"></i> Gráficos Avanzados
                    </button>
                    <button class="btn btn-secondary" onclick="initDashboardWidgets()">
                        <i class="fas fa-th-large"></i> Widgets
                    </button>
                    <button class="btn btn-primary" onclick="loadAllAnalytics()">
                        <i class="fas fa-sync-alt"></i> Cargar Todo
                    </button>
                </div>
            </div>
            <div id="analytics-content">
                <!-- Las funcionalidades avanzadas se cargarán aquí dinámicamente -->
                <div class="analytics-placeholder">
                    <div class="placeholder-content">
                        <i class="fas fa-chart-line fa-3x"></i>
                        <h3>Analytics Avanzados</h3>
                        <p>Selecciona una opción arriba para comenzar el análisis avanzado</p>
                        <div class="feature-list">
                            <div class="feature-item">
                                <i class="fas fa-fire"></i>
                                <span>Mapas de Calor por Zonas y Tiempo</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-chart-area"></i>
                                <span>Gráficos de Tendencias y Predicciones</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-th-large"></i>
                                <span>Widgets Interactivos y Drill-Down</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-download"></i>
                                <span>Exportación de Reportes</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Cargando...</p>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Scripts -->
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Leaflet Draw JS -->
    <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>

    <!-- Leaflet Heatmap Plugin -->
    <script src="https://cdn.jsdelivr.net/npm/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>

    <!-- Application Scripts -->
    <script src="js/config.js?v=2.0"></script>
    <script src="js/components.js?v=2.0"></script>
    <script src="js/auth.js?v=2.0"></script>
    <script src="js/maps.js?v=2.0"></script>
    <script src="js/notifications.js?v=2.0"></script>
    <script src="js/analytics.js?v=2.0"></script>
    <script src="js/charts.js?v=2.0"></script>
    <script src="js/heatmaps.js?v=2.0"></script>
    <script src="js/advanced-charts.js?v=2.0"></script>
    <script src="js/dashboard-widgets.js?v=2.0"></script>
    <script src="js/users.js?v=2.0"></script>
    <script src="js/properties.js?v=2.0"></script>
    <script src="js/routes.js?v=2.0"></script>
    <script src="js/monitoring.js?v=2.0"></script>
    <script src="js/reports.js?v=2.0"></script>

    <!-- Main Application Script -->
    <script>
        // Ensure all styles are loaded before initializing
        window.addEventListener('load', function() {
            console.log('🚀 Iniciando GeoVigia...');

            // Small delay to ensure all CSS is applied
            setTimeout(() => {
                // Check authentication first
                if (typeof checkAuthentication === 'function' && checkAuthentication()) {
                    // Initialize navigation
                    if (typeof initializeNavigation === 'function') {
                        initializeNavigation();
                    }

                    // Initialize components after authentication is confirmed
                    if (typeof GeoVigia !== 'undefined') {
                        console.log('✅ Componentes GeoVigia cargados');
                    }

                    // Initialize analytics dashboard only after authentication and styles
                    setTimeout(() => {
                        if (typeof initAnalytics === 'function') {
                            try {
                                initAnalytics();
                            } catch (error) {
                                console.log('📊 Analytics en modo demo:', error.message);
                            }
                        }
                    }, 1000);

                    // Initialize charts after analytics
                    setTimeout(() => {
                        if (typeof initCharts === 'function') {
                            try {
                                initCharts();
                            } catch (error) {
                                console.log('📊 Charts en modo demo:', error.message);
                            }
                        }
                    }, 2500);

                    console.log('✅ GeoVigia iniciado correctamente');
                } else {
                    console.log('🔐 Esperando autenticación...');
                }
            }, 100);
        });

        // Navigation functionality
        function initializeNavigation() {
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.content-section');

            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const sectionName = link.dataset.section;
                    navigateToSection(sectionName);
                });
            });
        }

        function navigateToSection(sectionName) {
            // Update active nav link
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

            // Show corresponding section
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById(`${sectionName}-section`).classList.add('active');

            // Initialize section-specific functionality
            initializeSection(sectionName);
        }

        function initializeSection(sectionName) {
            switch(sectionName) {
                case 'dashboard':
                    // Initialize analytics dashboard
                    if (typeof initAnalytics === 'function') {
                        initAnalytics();
                    }
                    // Initialize charts
                    if (typeof initCharts === 'function') {
                        setTimeout(() => initCharts(), 1000); // Delay para que analytics se cargue primero
                    }
                    break;
                case 'maps':
                    // Initialize maps when navigating to maps section
                    if (typeof GeoVigia !== 'undefined' && GeoVigia.Maps) {
                        GeoVigia.Maps.initialize();
                    }
                    break;
                case 'notifications':
                    // Initialize notifications section
                    initializeNotificationsSection();
                    break;
                case 'analytics':
                    // Initialize advanced analytics section
                    initializeAnalyticsSection();
                    break;
                case 'users':
                    // Initialize users module
                    break;
                case 'properties':
                    // Initialize properties module
                    break;
                case 'routes':
                    // Initialize routes module
                    break;
                case 'monitoring':
                    // Initialize monitoring module
                    break;
                case 'reports':
                    // Initialize reports module
                    break;
            }
        }

        function logout() {
            if (confirm('¿Está seguro de que desea cerrar sesión?')) {
                // Clear any stored data
                localStorage.clear();
                // Redirect to login or reload
                window.location.reload();
            }
        }

        // Funciones de Notificaciones
        function initializeNotificationsSection() {
            if (window.geoVigiaNotifications) {
                updateNotificationsStats();
                loadNotificationsList();
            }
        }

        function updateNotificationsStats() {
            if (!window.geoVigiaNotifications) return;

            const notifications = window.geoVigiaNotifications.notifications;
            const unreadCount = window.geoVigiaNotifications.unreadCount;
            const alertCount = notifications.filter(n => n.type === 'alert' || n.type === 'emergency').length;

            document.getElementById('total-notifications').textContent = notifications.length;
            document.getElementById('unread-notifications').textContent = unreadCount;
            document.getElementById('alert-notifications').textContent = alertCount;
            // TODO: Obtener usuarios conectados del WebSocket
            document.getElementById('connected-users').textContent = '0';
        }

        function loadNotificationsList() {
            if (window.geoVigiaNotifications) {
                window.geoVigiaNotifications.updateNotificationsList();
            }
        }

        function applyNotificationFilters() {
            const typeFilter = document.getElementById('notification-type-filter').value;
            const statusFilter = document.getElementById('notification-status-filter').value;
            const priorityFilter = document.getElementById('notification-priority-filter').value;

            // TODO: Implementar filtrado de notificaciones
            console.log('Aplicando filtros:', { typeFilter, statusFilter, priorityFilter });
        }

        function showCreateNotificationModal() {
            // TODO: Implementar modal para crear notificaciones
            console.log('Mostrar modal de crear notificación');
        }

        // Funciones de Analytics Avanzados
        function initializeAnalyticsSection() {
            console.log('🔬 Inicializando sección de Analytics Avanzados');
            // La sección se inicializa cuando el usuario selecciona una opción
        }

        function loadAllAnalytics() {
            console.log('📊 Cargando todos los analytics avanzados...');

            // Ocultar placeholder
            const placeholder = document.querySelector('.analytics-placeholder');
            if (placeholder) {
                placeholder.style.display = 'none';
            }

            // Inicializar todos los módulos
            if (typeof initHeatmaps === 'function') {
                initHeatmaps();
            }

            if (typeof initAdvancedCharts === 'function') {
                setTimeout(() => initAdvancedCharts(), 1000);
            }

            if (typeof initDashboardWidgets === 'function') {
                setTimeout(() => initDashboardWidgets(), 2000);
            }

            // Mostrar mensaje de éxito
            if (typeof showToast === 'function') {
                showToast('Analytics avanzados cargados correctamente', 'success');
            }
        }

        // Función auxiliar para mostrar toast (si no existe)
        function showToast(message, type = 'info') {
            console.log(`${type.toUpperCase()}: ${message}`);

            // Crear toast simple si no existe el sistema
            const toastContainer = document.getElementById('toast-container');
            if (toastContainer) {
                const toast = document.createElement('div');
                toast.className = `toast toast-${type}`;
                toast.innerHTML = `
                    <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle"></i>
                    <span>${message}</span>
                `;

                toastContainer.appendChild(toast);

                // Auto-remove after 3 seconds
                setTimeout(() => {
                    toast.remove();
                }, 3000);
            }
        }
    </script>
    </div> <!-- End of main-app -->
</body>
</html>

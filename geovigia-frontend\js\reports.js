/**
 * Módulo de Reportes y Analytics para GeoVigia Frontend
 */

class ReportsModule {
    constructor() {
        this.reportData = {};
        this.dateRange = {
            start: null,
            end: null
        };
        this.currentReport = 'summary';
    }

    /**
     * Carga el módulo de reportes
     */
    async load() {
        try {
            this.setDefaultDateRange();
            await this.loadReportData();
            this.render();
            this.setupEventListeners();
        } catch (error) {
            console.error('Error cargando reportes:', error);
            GeoVigia.Components.Toast.show(
                'error',
                'Error',
                'Error al cargar los reportes'
            );
        }
    }

    /**
     * Establece el rango de fechas por defecto (último mes)
     */
    setDefaultDateRange() {
        const end = new Date();
        const start = new Date();
        start.setMonth(start.getMonth() - 1);
        
        this.dateRange.start = start.toISOString().split('T')[0];
        this.dateRange.end = end.toISOString().split('T')[0];
    }

    /**
     * Carga los datos de reportes
     */
    async loadReportData() {
        try {
            // Cargar estadísticas generales
            const [userStats, propertyStats, routeExecutions] = await Promise.all([
                GeoVigia.API.Users.getUserStats(),
                GeoVigia.API.Properties.getPropertyStats(),
                GeoVigia.API.Routes.getExecutions({
                    start_date: this.dateRange.start,
                    end_date: this.dateRange.end
                })
            ]);

            this.reportData = {
                userStats,
                propertyStats,
                executions: Array.isArray(routeExecutions) ? routeExecutions : (routeExecutions.results || [])
            };
        } catch (error) {
            console.error('Error cargando datos de reportes:', error);
            this.reportData = {
                userStats: {},
                propertyStats: {},
                executions: []
            };
        }
    }

    /**
     * Renderiza el módulo de reportes
     */
    render() {
        const container = document.getElementById('reports-page');
        if (!container) return;

        container.innerHTML = `
            <div class="reports-header">
                <div class="reports-controls">
                    <div class="date-range-selector">
                        <label>Desde:</label>
                        <input type="date" id="start-date" value="${this.dateRange.start}">
                        <label>Hasta:</label>
                        <input type="date" id="end-date" value="${this.dateRange.end}">
                        <button class="btn btn-primary" id="apply-date-range">
                            <i class="fas fa-search"></i>
                            Aplicar
                        </button>
                    </div>
                    
                    <div class="report-actions">
                        <button class="btn btn-ghost" id="export-report-btn">
                            <i class="fas fa-download"></i>
                            Exportar
                        </button>
                        <button class="btn btn-ghost" id="refresh-reports-btn">
                            <i class="fas fa-sync-alt"></i>
                            Actualizar
                        </button>
                    </div>
                </div>
                
                <div class="report-tabs">
                    <button class="tab-btn active" data-report="summary">Resumen</button>
                    <button class="tab-btn" data-report="routes">Rutas</button>
                    <button class="tab-btn" data-report="guards">Guardias</button>
                    <button class="tab-btn" data-report="properties">Propiedades</button>
                </div>
            </div>

            <div class="reports-content">
                <div id="summary-report" class="report-section active">
                    ${this.renderSummaryReport()}
                </div>
                
                <div id="routes-report" class="report-section">
                    ${this.renderRoutesReport()}
                </div>
                
                <div id="guards-report" class="report-section">
                    ${this.renderGuardsReport()}
                </div>
                
                <div id="properties-report" class="report-section">
                    ${this.renderPropertiesReport()}
                </div>
            </div>
        `;

        this.setupEventListeners();
    }

    /**
     * Renderiza el reporte de resumen
     */
    renderSummaryReport() {
        const executions = this.reportData.executions || [];
        const completedExecutions = executions.filter(e => e.status === 'completada');
        const delayedExecutions = executions.filter(e => e.is_delayed);
        
        const avgCompletionRate = executions.length > 0 
            ? (completedExecutions.length / executions.length * 100).toFixed(1)
            : 0;

        return `
            <div class="summary-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-route"></i>
                    </div>
                    <div class="stat-content">
                        <h3>${executions.length}</h3>
                        <p>Ejecuciones Totales</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>${completedExecutions.length}</h3>
                        <p>Completadas</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3>${delayedExecutions.length}</h3>
                        <p>Retrasadas</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-content">
                        <h3>${avgCompletionRate}%</h3>
                        <p>Tasa de Completitud</p>
                    </div>
                </div>
            </div>
            
            <div class="summary-charts">
                <div class="chart-container">
                    <h4>Distribución de Estados</h4>
                    <div class="chart-placeholder">
                        <p>Gráfico de distribución de estados de ejecuciones</p>
                        <small>Funcionalidad de gráficos en desarrollo</small>
                    </div>
                </div>
                
                <div class="chart-container">
                    <h4>Tendencia de Ejecuciones</h4>
                    <div class="chart-placeholder">
                        <p>Gráfico de tendencia temporal de ejecuciones</p>
                        <small>Funcionalidad de gráficos en desarrollo</small>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Renderiza el reporte de rutas
     */
    renderRoutesReport() {
        const executions = this.reportData.executions || [];
        const routeStats = this.calculateRouteStats(executions);

        return `
            <div class="routes-report-content">
                <h4>Estadísticas por Ruta</h4>
                <div class="routes-stats-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Ruta</th>
                                <th>Ejecuciones</th>
                                <th>Completadas</th>
                                <th>Tasa de Éxito</th>
                                <th>Tiempo Promedio</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${routeStats.map(stat => `
                                <tr>
                                    <td>${stat.routeName}</td>
                                    <td>${stat.totalExecutions}</td>
                                    <td>${stat.completedExecutions}</td>
                                    <td>${stat.successRate.toFixed(1)}%</td>
                                    <td>${stat.avgDuration} min</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * Renderiza el reporte de guardias
     */
    renderGuardsReport() {
        const executions = this.reportData.executions || [];
        const guardStats = this.calculateGuardStats(executions);

        return `
            <div class="guards-report-content">
                <h4>Rendimiento por Guardia</h4>
                <div class="guards-stats-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Guardia</th>
                                <th>Rutas Ejecutadas</th>
                                <th>Completadas</th>
                                <th>Tasa de Éxito</th>
                                <th>Puntualidad</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${guardStats.map(stat => `
                                <tr>
                                    <td>${stat.guardName}</td>
                                    <td>${stat.totalExecutions}</td>
                                    <td>${stat.completedExecutions}</td>
                                    <td>${stat.successRate.toFixed(1)}%</td>
                                    <td>${stat.punctualityRate.toFixed(1)}%</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * Renderiza el reporte de propiedades
     */
    renderPropertiesReport() {
        const propertyStats = this.reportData.propertyStats || {};

        return `
            <div class="properties-report-content">
                <h4>Estadísticas de Propiedades</h4>
                <div class="properties-summary">
                    <div class="property-stat">
                        <strong>Total de Propiedades:</strong> ${propertyStats.total || 0}
                    </div>
                    <div class="property-stat">
                        <strong>Propiedades Activas:</strong> ${propertyStats.active || 0}
                    </div>
                    <div class="property-stat">
                        <strong>Por Tipo:</strong>
                        <ul>
                            <li>Residencial: ${propertyStats.residential || 0}</li>
                            <li>Comercial: ${propertyStats.commercial || 0}</li>
                            <li>Industrial: ${propertyStats.industrial || 0}</li>
                            <li>Mixto: ${propertyStats.mixed || 0}</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Calcula estadísticas por ruta
     */
    calculateRouteStats(executions) {
        const routeMap = new Map();

        executions.forEach(execution => {
            const routeName = execution.route_name;
            if (!routeMap.has(routeName)) {
                routeMap.set(routeName, {
                    routeName,
                    totalExecutions: 0,
                    completedExecutions: 0,
                    totalDuration: 0,
                    durationCount: 0
                });
            }

            const stat = routeMap.get(routeName);
            stat.totalExecutions++;
            
            if (execution.status === 'completada') {
                stat.completedExecutions++;
            }
            
            if (execution.duration_minutes) {
                stat.totalDuration += execution.duration_minutes;
                stat.durationCount++;
            }
        });

        return Array.from(routeMap.values()).map(stat => ({
            ...stat,
            successRate: stat.totalExecutions > 0 ? (stat.completedExecutions / stat.totalExecutions * 100) : 0,
            avgDuration: stat.durationCount > 0 ? Math.round(stat.totalDuration / stat.durationCount) : 0
        }));
    }

    /**
     * Calcula estadísticas por guardia
     */
    calculateGuardStats(executions) {
        const guardMap = new Map();

        executions.forEach(execution => {
            const guardName = execution.guard_username;
            if (!guardMap.has(guardName)) {
                guardMap.set(guardName, {
                    guardName,
                    totalExecutions: 0,
                    completedExecutions: 0,
                    onTimeExecutions: 0
                });
            }

            const stat = guardMap.get(guardName);
            stat.totalExecutions++;
            
            if (execution.status === 'completada') {
                stat.completedExecutions++;
            }
            
            if (execution.is_on_time) {
                stat.onTimeExecutions++;
            }
        });

        return Array.from(guardMap.values()).map(stat => ({
            ...stat,
            successRate: stat.totalExecutions > 0 ? (stat.completedExecutions / stat.totalExecutions * 100) : 0,
            punctualityRate: stat.totalExecutions > 0 ? (stat.onTimeExecutions / stat.totalExecutions * 100) : 0
        }));
    }

    /**
     * Configura los event listeners
     */
    setupEventListeners() {
        // Aplicar rango de fechas
        const applyDateBtn = document.getElementById('apply-date-range');
        if (applyDateBtn) {
            applyDateBtn.addEventListener('click', () => this.applyDateRange());
        }

        // Exportar reporte
        const exportBtn = document.getElementById('export-report-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportReport());
        }

        // Actualizar reportes
        const refreshBtn = document.getElementById('refresh-reports-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refresh());
        }

        // Tabs de reportes
        const tabBtns = document.querySelectorAll('.tab-btn');
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const reportType = btn.dataset.report;
                this.switchReport(reportType);
            });
        });
    }

    /**
     * Aplica el rango de fechas seleccionado
     */
    async applyDateRange() {
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;

        if (!startDate || !endDate) {
            GeoVigia.Components.Toast.show('warning', 'Fechas Requeridas', 'Por favor seleccione ambas fechas');
            return;
        }

        if (new Date(startDate) > new Date(endDate)) {
            GeoVigia.Components.Toast.show('error', 'Fechas Inválidas', 'La fecha de inicio debe ser anterior a la fecha de fin');
            return;
        }

        this.dateRange.start = startDate;
        this.dateRange.end = endDate;

        await this.refresh();
    }

    /**
     * Cambia entre diferentes tipos de reporte
     */
    switchReport(reportType) {
        // Actualizar tabs activos
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.report === reportType) {
                btn.classList.add('active');
            }
        });

        // Mostrar sección correspondiente
        document.querySelectorAll('.report-section').forEach(section => {
            section.classList.remove('active');
        });

        const targetSection = document.getElementById(`${reportType}-report`);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        this.currentReport = reportType;
    }

    /**
     * Exporta el reporte actual
     */
    exportReport() {
        const reportData = {
            type: this.currentReport,
            dateRange: this.dateRange,
            timestamp: new Date().toISOString(),
            data: this.reportData
        };

        const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `geovigia-reporte-${this.currentReport}-${GeoVigia.Utils.Date.getCurrentDate()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        
        GeoVigia.Components.Toast.show(
            'success',
            'Exportado',
            'Reporte exportado correctamente'
        );
    }

    /**
     * Refresca los datos de reportes
     */
    async refresh() {
        await this.loadReportData();
        this.render();
    }
}

// Crear instancia global del módulo de reportes
window.GeoVigia.Reports = new ReportsModule();

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.shortcuts import get_object_or_404
from django.utils import timezone
import json
import logging

from .models import Notification, PushSubscription, NotificationType, NotificationPriority
from .serializers import NotificationSerializer, PushSubscriptionSerializer
from .utils import send_push_notification

logger = logging.getLogger(__name__)

# Importar channel_layer de manera segura
def get_channel_layer_safe():
    try:
        from channels.layers import get_channel_layer
        return get_channel_layer()
    except ImportError:
        logger.warning("Channels no está disponible")
        return None


class NotificationPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_notifications(request):
    """Obtiene las notificaciones del usuario autenticado"""
    try:
        notifications = Notification.objects.filter(
            recipient=request.user
        ).order_by('-created_at')

        # Filtros opcionales
        notification_type = request.GET.get('type')
        is_read = request.GET.get('is_read')

        if notification_type:
            notifications = notifications.filter(notification_type=notification_type)

        if is_read is not None:
            notifications = notifications.filter(is_read=is_read.lower() == 'true')

        # Paginación
        paginator = NotificationPagination()
        page = paginator.paginate_queryset(notifications, request)

        if page is not None:
            serializer = NotificationSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)

        serializer = NotificationSerializer(notifications, many=True)
        return Response(serializer.data)

    except Exception as e:
        logger.error(f"Error obteniendo notificaciones: {e}")
        return Response(
            {'error': 'Error interno del servidor'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_notification(request):
    """Crea una nueva notificación"""
    try:
        data = request.data

        # Validar datos requeridos
        required_fields = ['recipient_id', 'title', 'message']
        for field in required_fields:
            if field not in data:
                return Response(
                    {'error': f'Campo requerido: {field}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Verificar permisos (solo operadores pueden enviar notificaciones)
        if request.user.user_type != 'operador':
            return Response(
                {'error': 'No tienes permisos para enviar notificaciones'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Crear notificación
        notification = Notification.objects.create(
            recipient_id=data['recipient_id'],
            sender=request.user,
            title=data['title'],
            message=data['message'],
            notification_type=data.get('type', NotificationType.INFO),
            priority=data.get('priority', NotificationPriority.NORMAL),
            extra_data=data.get('extra_data', {})
        )

        # Enviar notificación en tiempo real
        send_realtime_notification(notification)

        # Enviar push notification si está habilitado
        send_push_notification(notification)

        serializer = NotificationSerializer(notification)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Error creando notificación: {e}")
        return Response(
            {'error': 'Error interno del servidor'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_notification_read(request, notification_id):
    """Marca una notificación como leída"""
    try:
        notification = get_object_or_404(
            Notification,
            id=notification_id,
            recipient=request.user
        )

        notification.mark_as_read()

        return Response({'message': 'Notificación marcada como leída'})

    except Exception as e:
        logger.error(f"Error marcando notificación como leída: {e}")
        return Response(
            {'error': 'Error interno del servidor'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_all_read(request):
    """Marca todas las notificaciones del usuario como leídas"""
    try:
        updated = Notification.objects.filter(
            recipient=request.user,
            is_read=False
        ).update(
            is_read=True,
            read_at=timezone.now()
        )

        return Response({
            'message': f'{updated} notificaciones marcadas como leídas'
        })

    except Exception as e:
        logger.error(f"Error marcando todas las notificaciones como leídas: {e}")
        return Response(
            {'error': 'Error interno del servidor'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_unread_count(request):
    """Obtiene el número de notificaciones no leídas"""
    try:
        count = Notification.objects.filter(
            recipient=request.user,
            is_read=False
        ).count()

        return Response({'unread_count': count})

    except Exception as e:
        logger.error(f"Error obteniendo contador de no leídas: {e}")
        return Response(
            {'error': 'Error interno del servidor'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def subscribe_push(request):
    """Registra una suscripción push"""
    try:
        data = request.data

        # Validar datos requeridos
        required_fields = ['endpoint', 'p256dh', 'auth']
        for field in required_fields:
            if field not in data:
                return Response(
                    {'error': f'Campo requerido: {field}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Crear o actualizar suscripción
        subscription, created = PushSubscription.objects.update_or_create(
            user=request.user,
            endpoint=data['endpoint'],
            defaults={
                'p256dh_key': data['p256dh'],
                'auth_key': data['auth'],
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'is_active': True
            }
        )

        serializer = PushSubscriptionSerializer(subscription)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Error registrando suscripción push: {e}")
        return Response(
            {'error': 'Error interno del servidor'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def send_alert(request):
    """Envía una alerta de emergencia"""
    try:
        data = request.data

        # Crear notificación de emergencia
        notification = Notification.objects.create(
            recipient_id=data.get('recipient_id'),
            sender=request.user,
            title=data.get('title', 'Alerta de Emergencia'),
            message=data.get('message', 'Se ha activado una alerta de emergencia'),
            notification_type=NotificationType.EMERGENCY,
            priority=NotificationPriority.CRITICAL,
            extra_data={
                'alert_type': data.get('alert_type', 'emergency'),
                'location': data.get('location', {}),
                'timestamp': timezone.now().isoformat()
            }
        )

        # Enviar alerta en tiempo real
        send_realtime_alert(notification, data.get('alert_type', 'emergency'))

        # Enviar push notification crítica
        send_push_notification(notification)

        return Response({
            'message': 'Alerta enviada exitosamente',
            'notification_id': str(notification.id)
        })

    except Exception as e:
        logger.error(f"Error enviando alerta: {e}")
        return Response(
            {'error': 'Error interno del servidor'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def send_realtime_notification(notification):
    """Envía notificación en tiempo real via WebSocket"""
    try:
        from asgiref.sync import async_to_sync
        channel_layer = get_channel_layer_safe()

        if not channel_layer:
            logger.warning("Channel layer no disponible")
            return

        # Enviar a usuario específico
        user_group = f"user_{notification.recipient.id}"
        async_to_sync(channel_layer.group_send)(
            user_group,
            {
                'type': 'notification_message',
                'notification': notification.to_dict()
            }
        )

        # Marcar como enviada
        notification.mark_as_sent()

    except Exception as e:
        logger.error(f"Error enviando notificación en tiempo real: {e}")


def send_realtime_alert(notification, alert_type):
    """Envía alerta en tiempo real via WebSocket"""
    try:
        from asgiref.sync import async_to_sync
        channel_layer = get_channel_layer_safe()

        if not channel_layer:
            logger.warning("Channel layer no disponible")
            return

        alert_data = {
            'id': str(notification.id),
            'type': alert_type,
            'title': notification.title,
            'message': notification.message,
            'priority': notification.priority,
            'sender': notification.sender.username if notification.sender else 'Sistema',
            'timestamp': notification.created_at.isoformat(),
            'extra_data': notification.extra_data
        }

        # Enviar a todos los operadores
        async_to_sync(channel_layer.group_send)(
            "role_operador",
            {
                'type': 'alert_message',
                'alert': alert_data
            }
        )

        # Si es una alerta de cliente, enviar también a guardias
        if notification.sender and notification.sender.user_type == 'cliente':
            async_to_sync(channel_layer.group_send)(
                "role_guardia",
                {
                    'type': 'alert_message',
                    'alert': alert_data
                }
            )

    except Exception as e:
        logger.error(f"Error enviando alerta en tiempo real: {e}")

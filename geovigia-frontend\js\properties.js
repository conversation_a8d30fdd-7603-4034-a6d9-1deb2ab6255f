/**
 * Módulo de Gestión de Propiedades para GeoVigia Frontend
 */

class PropertiesModule {
    constructor() {
        this.properties = [];
        this.table = null;
        this.filters = {
            search: '',
            propertyType: '',
            status: ''
        };
    }

    /**
     * Carga el módulo de propiedades
     */
    async load() {
        try {
            await this.loadProperties();
            this.render();
            this.setupEventListeners();
        } catch (error) {
            console.error('Error cargando propiedades:', error);
            GeoVigia.Components.Toast.show(
                'error',
                'Error',
                'Error al cargar las propiedades'
            );
        }
    }

    /**
     * Carga la lista de propiedades
     */
    async loadProperties() {
        try {
            const response = await GeoVigia.API.Properties.getProperties();
            this.properties = Array.isArray(response) ? response : (response.results || []);
        } catch (error) {
            console.error('Error cargando propiedades:', error);
            this.properties = [];
        }
    }

    /**
     * Renderiza el módulo de propiedades
     */
    render() {
        const container = document.getElementById('properties-page');
        if (!container) return;

        container.innerHTML = `
            <div class="properties-header">
                <div class="properties-actions">
                    <button class="btn btn-primary" id="add-property-btn">
                        <i class="fas fa-plus"></i>
                        Nueva Propiedad
                    </button>
                    <button class="btn btn-ghost" id="refresh-properties-btn">
                        <i class="fas fa-sync-alt"></i>
                        Actualizar
                    </button>
                </div>
                
                <div class="properties-filters">
                    <div class="filter-group">
                        <select id="property-type-filter">
                            <option value="">Todos los tipos</option>
                            <option value="residencial">Residencial</option>
                            <option value="comercial">Comercial</option>
                            <option value="industrial">Industrial</option>
                            <option value="mixto">Mixto</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <select id="status-filter">
                            <option value="">Todos los estados</option>
                            <option value="activa">Activas</option>
                            <option value="inactiva">Inactivas</option>
                            <option value="mantenimiento">En Mantenimiento</option>
                        </select>
                    </div>
                </div>
            </div>

            <div id="properties-table-container"></div>
        `;

        this.renderTable();
    }

    /**
     * Renderiza la tabla de propiedades
     */
    renderTable() {
        const container = document.getElementById('properties-table-container');
        if (!container) return;

        const columns = [
            {
                key: 'name',
                title: 'Nombre',
                width: '200px'
            },
            {
                key: 'full_address',
                title: 'Dirección',
                render: (value, row) => {
                    return value || `${row.address}, ${row.city}`;
                }
            },
            {
                key: 'property_type',
                title: 'Tipo',
                width: '120px',
                render: (value) => {
                    return `<span class="status-badge ${value}">${GeoVigia.Utils.Format.capitalize(value)}</span>`;
                }
            },
            {
                key: 'status',
                title: 'Estado',
                width: '120px',
                render: (value) => {
                    return `<span class="status-badge ${value}">${GeoVigia.Utils.Format.capitalize(value)}</span>`;
                }
            },
            {
                key: 'owner_name',
                title: 'Propietario',
                width: '150px',
                render: (value, row) => {
                    return row.owner ? (row.owner.first_name && row.owner.last_name 
                        ? `${row.owner.first_name} ${row.owner.last_name}`
                        : row.owner.username) : 'N/A';
                }
            },
            {
                key: 'created_at',
                title: 'Fecha de Registro',
                width: '150px',
                render: (value) => GeoVigia.Utils.Date.formatDate(value)
            },
            {
                key: 'actions',
                title: 'Acciones',
                width: '150px',
                render: (value, row) => `
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-ghost" onclick="GeoVigia.Properties.editProperty(${row.id})" title="Editar">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-ghost" onclick="GeoVigia.Properties.viewProperty(${row.id})" title="Ver">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-ghost" onclick="GeoVigia.Properties.managePerimeters(${row.id})" title="Perímetros">
                            <i class="fas fa-map-marked-alt"></i>
                        </button>
                    </div>
                `
            }
        ];

        this.table = new GeoVigia.Components.Table(container, {
            columns: columns,
            data: this.properties,
            searchPlaceholder: 'Buscar propiedades...',
            emptyMessage: 'No se encontraron propiedades'
        });
    }

    /**
     * Configura los event listeners
     */
    setupEventListeners() {
        // Botón de nueva propiedad
        const addPropertyBtn = document.getElementById('add-property-btn');
        if (addPropertyBtn) {
            addPropertyBtn.addEventListener('click', () => this.showPropertyForm());
        }

        // Botón de actualizar
        const refreshBtn = document.getElementById('refresh-properties-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refresh());
        }

        // Filtros
        const propertyTypeFilter = document.getElementById('property-type-filter');
        const statusFilter = document.getElementById('status-filter');

        if (propertyTypeFilter) {
            propertyTypeFilter.addEventListener('change', (e) => {
                this.filters.propertyType = e.target.value;
                this.applyFilters();
            });
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFilters();
            });
        }
    }

    /**
     * Aplica los filtros a la tabla
     */
    applyFilters() {
        let filteredProperties = [...this.properties];

        // Filtrar por tipo de propiedad
        if (this.filters.propertyType) {
            filteredProperties = filteredProperties.filter(property => 
                property.property_type === this.filters.propertyType);
        }

        // Filtrar por estado
        if (this.filters.status) {
            filteredProperties = filteredProperties.filter(property => 
                property.status === this.filters.status);
        }

        // Actualizar tabla
        if (this.table) {
            this.table.setData(filteredProperties);
        }
    }

    /**
     * Muestra el formulario de propiedad
     */
    async showPropertyForm(propertyId = null) {
        const isEdit = !!propertyId;
        let propertyData = {};

        if (isEdit) {
            try {
                propertyData = await GeoVigia.API.Properties.getProperty(propertyId);
            } catch (error) {
                GeoVigia.Components.Toast.show('error', 'Error', 'Error al cargar los datos de la propiedad');
                return;
            }
        }

        const formContent = `
            <form id="property-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Nombre *</label>
                        <input type="text" id="name" name="name" value="${propertyData.name || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="property_type">Tipo de Propiedad *</label>
                        <select id="property_type" name="property_type" required>
                            <option value="">Seleccionar tipo</option>
                            <option value="residencial" ${propertyData.property_type === 'residencial' ? 'selected' : ''}>Residencial</option>
                            <option value="comercial" ${propertyData.property_type === 'comercial' ? 'selected' : ''}>Comercial</option>
                            <option value="industrial" ${propertyData.property_type === 'industrial' ? 'selected' : ''}>Industrial</option>
                            <option value="mixto" ${propertyData.property_type === 'mixto' ? 'selected' : ''}>Mixto</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address">Dirección *</label>
                    <input type="text" id="address" name="address" value="${propertyData.address || ''}" required>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="city">Ciudad *</label>
                        <input type="text" id="city" name="city" value="${propertyData.city || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="state">Estado *</label>
                        <input type="text" id="state" name="state" value="${propertyData.state || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="postal_code">Código Postal</label>
                        <input type="text" id="postal_code" name="postal_code" value="${propertyData.postal_code || ''}">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="latitude">Latitud *</label>
                        <input type="number" step="any" id="latitude" name="latitude" value="${propertyData.latitude || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="longitude">Longitud *</label>
                        <input type="number" step="any" id="longitude" name="longitude" value="${propertyData.longitude || ''}" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">Descripción</label>
                    <textarea id="description" name="description" rows="3">${propertyData.description || ''}</textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="status">Estado</label>
                        <select id="status" name="status">
                            <option value="activa" ${propertyData.status === 'activa' ? 'selected' : ''}>Activa</option>
                            <option value="inactiva" ${propertyData.status === 'inactiva' ? 'selected' : ''}>Inactiva</option>
                            <option value="mantenimiento" ${propertyData.status === 'mantenimiento' ? 'selected' : ''}>En Mantenimiento</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="priority_level">Nivel de Prioridad</label>
                        <select id="priority_level" name="priority_level">
                            <option value="1" ${propertyData.priority_level === 1 ? 'selected' : ''}>1 - Baja</option>
                            <option value="2" ${propertyData.priority_level === 2 ? 'selected' : ''}>2 - Normal</option>
                            <option value="3" ${propertyData.priority_level === 3 ? 'selected' : ''}>3 - Media</option>
                            <option value="4" ${propertyData.priority_level === 4 ? 'selected' : ''}>4 - Alta</option>
                            <option value="5" ${propertyData.priority_level === 5 ? 'selected' : ''}>5 - Crítica</option>
                        </select>
                    </div>
                </div>
            </form>
        `;

        const confirmed = await GeoVigia.Components.Modal.show(
            isEdit ? 'Editar Propiedad' : 'Nueva Propiedad',
            formContent,
            {
                confirmText: isEdit ? 'Actualizar' : 'Crear',
                cancelText: 'Cancelar',
                width: '600px'
            }
        );

        if (confirmed) {
            await this.saveProperty(propertyId);
        }
    }

    /**
     * Guarda una propiedad
     */
    async saveProperty(propertyId = null) {
        const form = document.getElementById('property-form');
        if (!form) return;

        const formData = GeoVigia.Utils.DOM.getFormData(form);
        const isEdit = !!propertyId;

        // Validar coordenadas
        const lat = parseFloat(formData.latitude);
        const lng = parseFloat(formData.longitude);
        
        if (!GeoVigia.Utils.Validation.isValidCoordinates(lat, lng)) {
            GeoVigia.Components.Toast.show('error', 'Error', 'Las coordenadas no son válidas');
            return;
        }

        try {
            if (isEdit) {
                await GeoVigia.API.Properties.updateProperty(propertyId, formData);
                GeoVigia.Components.Toast.show('success', 'Actualizada', 'Propiedad actualizada correctamente');
            } else {
                await GeoVigia.API.Properties.createProperty(formData);
                GeoVigia.Components.Toast.show('success', 'Creada', 'Propiedad creada correctamente');
            }

            await this.refresh();
        } catch (error) {
            console.error('Error guardando propiedad:', error);
            GeoVigia.Components.Toast.show('error', 'Error', 'Error al guardar la propiedad');
        }
    }

    /**
     * Ve los detalles de una propiedad
     */
    async viewProperty(propertyId) {
        try {
            const property = await GeoVigia.API.Properties.getProperty(propertyId);
            
            const content = `
                <div class="property-details">
                    <div class="detail-row">
                        <strong>Nombre:</strong> ${property.name}
                    </div>
                    <div class="detail-row">
                        <strong>Tipo:</strong> ${GeoVigia.Utils.Format.capitalize(property.property_type)}
                    </div>
                    <div class="detail-row">
                        <strong>Dirección:</strong> ${property.full_address || `${property.address}, ${property.city}`}
                    </div>
                    <div class="detail-row">
                        <strong>Coordenadas:</strong> ${property.latitude}, ${property.longitude}
                    </div>
                    <div class="detail-row">
                        <strong>Estado:</strong> ${GeoVigia.Utils.Format.capitalize(property.status)}
                    </div>
                    <div class="detail-row">
                        <strong>Prioridad:</strong> ${property.priority_level}
                    </div>
                    <div class="detail-row">
                        <strong>Fecha de registro:</strong> ${GeoVigia.Utils.Date.formatDateTime(property.created_at)}
                    </div>
                    ${property.description ? `<div class="detail-row"><strong>Descripción:</strong> ${property.description}</div>` : ''}
                </div>
            `;

            await GeoVigia.Components.Modal.show('Detalles de la Propiedad', content, {
                showCancel: false,
                confirmText: 'Cerrar'
            });
        } catch (error) {
            GeoVigia.Components.Toast.show('error', 'Error', 'Error al cargar los detalles de la propiedad');
        }
    }

    /**
     * Edita una propiedad
     */
    async editProperty(propertyId) {
        await this.showPropertyForm(propertyId);
    }

    /**
     * Gestiona los perímetros de una propiedad
     */
    async managePerimeters(propertyId) {
        GeoVigia.Components.Toast.show(
            'info',
            'Perímetros',
            'Gestión de perímetros en desarrollo'
        );
    }

    /**
     * Refresca la lista de propiedades
     */
    async refresh() {
        await this.loadProperties();
        this.applyFilters();
    }
}

// Crear instancia global del módulo de propiedades
window.GeoVigia.Properties = new PropertiesModule();

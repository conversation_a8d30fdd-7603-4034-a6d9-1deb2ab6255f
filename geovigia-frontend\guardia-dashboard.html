<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GeoVigia Guardia - Mi Recorrido</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/guardia.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <!-- Leaflet Draw CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css"/>
</head>
<body class="guardia-dashboard">
    <!-- Header -->
    <header class="guardia-header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-route"></i>
                <span>GeoVigia Guardia</span>
            </div>
            <div class="guard-status">
                <div class="status-indicator">
                    <span class="status-dot active"></span>
                    <span>En Servicio</span>
                </div>
                <div class="header-actions">
                    <button class="emergency-btn" onclick="emergencyCall()">
                        <i class="fas fa-phone"></i>
                        EMERGENCIA
                    </button>
                    <button class="logout-btn" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="guardia-main">
        <!-- Route Overview -->
        <section class="route-overview">
            <h2><i class="fas fa-map-marked-alt"></i> Mi Recorrido</h2>
            <div class="route-card">
                <div class="route-info">
                    <h3 id="route-name">Ruta Nocturna Sector A</h3>
                    <div class="route-stats">
                        <div class="stat">
                            <span class="stat-number" id="total-properties">8</span>
                            <span class="stat-label">Propiedades</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number" id="completed-rounds">2</span>
                            <span class="stat-label">Rondas completadas</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number" id="estimated-time">45 min</span>
                            <span class="stat-label">Tiempo estimado</span>
                        </div>
                    </div>
                </div>
                <button class="start-route-btn" onclick="startRoute()">
                    <i class="fas fa-play"></i>
                    Iniciar Recorrido
                </button>
            </div>
        </section>

        <!-- Properties List -->
        <section class="properties-section">
            <h2><i class="fas fa-home"></i> Propiedades en Recorrido</h2>
            <div class="properties-list" id="properties-list">
                <div class="property-item occupied">
                    <div class="property-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="property-info">
                        <h4>Casa Residencial A</h4>
                        <p>Av. Reforma 123</p>
                        <span class="occupancy-status occupied">Ocupada</span>
                    </div>
                    <div class="property-actions">
                        <button class="check-btn" onclick="checkProperty(1)">
                            <i class="fas fa-check"></i>
                        </button>
                    </div>
                </div>

                <div class="property-item empty">
                    <div class="property-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="property-info">
                        <h4>Casa Residencial B</h4>
                        <p>Av. Reforma 125</p>
                        <span class="occupancy-status empty">Casa Sola</span>
                    </div>
                    <div class="property-actions">
                        <button class="check-btn" onclick="checkProperty(2)">
                            <i class="fas fa-check"></i>
                        </button>
                    </div>
                </div>

                <div class="property-item occupied alert">
                    <div class="property-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="property-info">
                        <h4>Casa Residencial C</h4>
                        <p>Av. Reforma 127</p>
                        <span class="occupancy-status occupied">Ocupada</span>
                        <div class="alert-indicator">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Actitud sospechosa reportada</span>
                        </div>
                    </div>
                    <div class="property-actions">
                        <button class="check-btn priority" onclick="checkProperty(3)">
                            <i class="fas fa-exclamation"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Live Alerts -->
        <section class="live-alerts">
            <h2><i class="fas fa-bell"></i> Alertas en Tiempo Real</h2>
            <div class="alerts-container" id="alerts-container">
                <div class="alert-item high-priority">
                    <div class="alert-icon">
                        <i class="fas fa-sos"></i>
                    </div>
                    <div class="alert-content">
                        <h4>Solicitud de Ayuda</h4>
                        <p>Casa Residencial C - Ana Propietaria</p>
                        <span class="alert-time">Hace 2 minutos</span>
                    </div>
                    <button class="respond-btn" onclick="respondToAlert(1)">
                        Responder
                    </button>
                </div>

                <div class="alert-item medium-priority">
                    <div class="alert-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="alert-content">
                        <h4>Propietario Llegando</h4>
                        <p>Casa Residencial A - Juan Pérez</p>
                        <span class="alert-time">Hace 5 minutos</span>
                    </div>
                    <button class="respond-btn" onclick="respondToAlert(2)">
                        Confirmar
                    </button>
                </div>
            </div>
        </section>

        <!-- Map Section -->
        <section class="map-section">
            <h2><i class="fas fa-map"></i> Mapa de Recorrido</h2>
            <div class="map-container">
                <div id="route-map" class="route-map" style="height: 400px; width: 100%;">
                    <!-- Leaflet map will be initialized here -->
                </div>
                <div class="map-controls">
                    <button class="map-btn" onclick="centerMap()">
                        <i class="fas fa-crosshairs"></i>
                        Centrar
                    </button>
                    <button class="map-btn" onclick="toggleSatellite()">
                        <i class="fas fa-satellite"></i>
                        Satélite
                    </button>
                    <button class="map-btn" onclick="initializeGuardMap()">
                        <i class="fas fa-sync"></i>
                        Actualizar
                    </button>
                </div>
            </div>
        </section>
    </main>

    <!-- Emergency Modal -->
    <div id="emergency-modal" class="modal emergency">
        <div class="modal-content">
            <h3><i class="fas fa-exclamation-triangle"></i> Llamada de Emergencia</h3>
            <p>¿Confirma que desea contactar al operador de emergencia?</p>
            <div class="modal-actions">
                <button class="btn-cancel" onclick="closeEmergencyModal()">Cancelar</button>
                <button class="btn-emergency" onclick="confirmEmergency()">
                    <i class="fas fa-phone"></i>
                    Llamar Ahora
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Leaflet Draw JS -->
    <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>

    <!-- Application Scripts -->
    <script src="js/config.js"></script>
    <script src="js/components.js"></script>
    <script src="js/maps.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/guardia-dashboard.js"></script>

    <script>
        // Initialize guard map when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeGuardMap();
        });

        function initializeGuardMap() {
            if (typeof GeoVigia !== 'undefined' && GeoVigia.Maps) {
                // Initialize map in the route-map container
                if (!window.guardMap && document.getElementById('route-map')) {
                    window.guardMap = new GeoVigiaMaps();
                    window.guardMap.init('route-map');
                    console.log('✅ Mapa de guardia inicializado');
                }
            } else {
                console.warn('⚠️ GeoVigia Maps no disponible');
            }
        }

        function centerMap() {
            if (window.guardMap) {
                window.guardMap.centerOnProperties();
            }
        }

        function toggleSatellite() {
            // TODO: Implement satellite view toggle
            console.log('🛰️ Toggle satellite view');
        }
    </script>
</body>
</html>

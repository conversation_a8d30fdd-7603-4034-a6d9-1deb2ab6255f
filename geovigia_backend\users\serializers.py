from rest_framework import serializers
from django.contrib.auth import authenticate
from .models import CustomUser, ClienteProfile, GuardiaProfile, OperadorProfile


class CustomUserSerializer(serializers.ModelSerializer):
    """
    Serializer para el modelo CustomUser
    """
    password = serializers.CharField(write_only=True)
    
    class Meta:
        model = CustomUser
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'user_type', 'phone_number', 'address', 'is_active_service',
            'password', 'created_at', 'updated_at'
        ]
        extra_kwargs = {
            'password': {'write_only': True},
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True},
        }
    
    def create(self, validated_data):
        password = validated_data.pop('password')
        user = CustomUser.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user
    
    def update(self, instance, validated_data):
        password = validated_data.pop('password', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        if password:
            instance.set_password(password)
        
        instance.save()
        return instance


class ClienteProfileSerializer(serializers.ModelSerializer):
    """
    Serializer para el perfil de Cliente
    """
    user = CustomUserSerializer(read_only=True)
    
    class Meta:
        model = ClienteProfile
        fields = [
            'user', 'emergency_contact_name', 'emergency_contact_phone',
            'home_alone_frequency'
        ]


class GuardiaProfileSerializer(serializers.ModelSerializer):
    """
    Serializer para el perfil de Guardia
    """
    user = CustomUserSerializer(read_only=True)
    
    class Meta:
        model = GuardiaProfile
        fields = [
            'user', 'employee_id', 'shift_start', 'shift_end',
            'is_on_duty', 'current_latitude', 'current_longitude',
            'last_location_update'
        ]


class OperadorProfileSerializer(serializers.ModelSerializer):
    """
    Serializer para el perfil de Operador
    """
    user = CustomUserSerializer(read_only=True)
    
    class Meta:
        model = OperadorProfile
        fields = [
            'user', 'department', 'can_manage_users',
            'can_manage_routes', 'can_view_reports'
        ]


class LoginSerializer(serializers.Serializer):
    """
    Serializer para el login de usuarios
    """
    username = serializers.CharField()
    password = serializers.CharField()
    
    def validate(self, data):
        username = data.get('username')
        password = data.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if user:
                if user.is_active:
                    data['user'] = user
                else:
                    raise serializers.ValidationError('La cuenta está desactivada.')
            else:
                raise serializers.ValidationError('Credenciales inválidas.')
        else:
            raise serializers.ValidationError('Debe proporcionar username y password.')
        
        return data


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer para registro de nuevos usuarios
    """
    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = CustomUser
        fields = [
            'username', 'email', 'first_name', 'last_name',
            'user_type', 'phone_number', 'address',
            'password', 'password_confirm'
        ]
    
    def validate(self, data):
        if data['password'] != data['password_confirm']:
            raise serializers.ValidationError("Las contraseñas no coinciden.")
        return data
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        
        user = CustomUser.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        
        # Crear perfil específico según el tipo de usuario
        if user.user_type == 'cliente':
            ClienteProfile.objects.create(user=user)
        elif user.user_type == 'guardia':
            GuardiaProfile.objects.create(
                user=user,
                employee_id=f"G{user.id:04d}",  # ID automático
                shift_start="08:00",
                shift_end="20:00"
            )
        elif user.user_type == 'operador':
            OperadorProfile.objects.create(user=user)
        
        return user


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer completo que incluye el perfil específico del usuario
    """
    cliente_profile = ClienteProfileSerializer(read_only=True)
    guardia_profile = GuardiaProfileSerializer(read_only=True)
    operador_profile = OperadorProfileSerializer(read_only=True)
    
    class Meta:
        model = CustomUser
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'user_type', 'phone_number', 'address', 'is_active_service',
            'created_at', 'updated_at', 'cliente_profile',
            'guardia_profile', 'operador_profile'
        ]
        read_only_fields = ['created_at', 'updated_at']


class GuardiaLocationUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer para actualizar la ubicación del guardia
    """
    class Meta:
        model = GuardiaProfile
        fields = ['current_latitude', 'current_longitude']
    
    def update(self, instance, validated_data):
        from django.utils import timezone
        instance.current_latitude = validated_data.get('current_latitude', instance.current_latitude)
        instance.current_longitude = validated_data.get('current_longitude', instance.current_longitude)
        instance.last_location_update = timezone.now()
        instance.save()
        return instance

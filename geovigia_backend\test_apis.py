#!/usr/bin/env python
"""
Script de prueba para las APIs del sistema GeoVigia
Ejecutar con: python test_apis.py
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_user_registration():
    """Prueba el registro de usuarios"""
    print("=== Probando Registro de Usuarios ===")
    
    # Datos de prueba para diferentes tipos de usuarios
    test_users = [
        {
            "username": "cliente_test",
            "email": "<EMAIL>",
            "first_name": "<PERSON>",
            "last_name": "<PERSON>",
            "user_type": "cliente",
            "phone_number": "+1234567890",
            "address": "Calle 123, Ciudad",
            "password": "testpass123",
            "password_confirm": "testpass123"
        },
        {
            "username": "guardia_test",
            "email": "<EMAIL>",
            "first_name": "<PERSON>",
            "last_name": "<PERSON>",
            "user_type": "guardia",
            "phone_number": "+1234567891",
            "address": "Avenida 456, Ciudad",
            "password": "testpass123",
            "password_confirm": "testpass123"
        },
        {
            "username": "operador_test",
            "email": "<EMAIL>",
            "first_name": "Ana",
            "last_name": "López",
            "user_type": "operador",
            "phone_number": "+1234567892",
            "address": "Plaza 789, Ciudad",
            "password": "testpass123",
            "password_confirm": "testpass123"
        }
    ]
    
    for user_data in test_users:
        try:
            response = requests.post(f"{BASE_URL}/users/register/", json=user_data)
            if response.status_code == 201:
                print(f"✅ Usuario {user_data['user_type']} registrado exitosamente")
                data = response.json()
                print(f"   Token: {data['token'][:20]}...")
                print(f"   ID: {data['user']['id']}")
            else:
                print(f"❌ Error registrando {user_data['user_type']}: {response.status_code}")
                print(f"   {response.text}")
        except Exception as e:
            print(f"❌ Excepción registrando {user_data['user_type']}: {e}")
    
    print()

def test_user_login():
    """Prueba el login de usuarios"""
    print("=== Probando Login de Usuarios ===")
    
    # Probar login con diferentes usuarios
    login_tests = [
        {"username": "admin", "password": "admin123"},
        {"username": "cliente_test", "password": "testpass123"},
        {"username": "guardia_test", "password": "testpass123"},
        {"username": "operador_test", "password": "testpass123"}
    ]
    
    tokens = {}
    
    for login_data in login_tests:
        try:
            response = requests.post(f"{BASE_URL}/users/login/", json=login_data)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Login exitoso para {login_data['username']}")
                print(f"   Tipo: {data['user']['user_type']}")
                print(f"   Token: {data['token'][:20]}...")
                tokens[login_data['username']] = data['token']
            else:
                print(f"❌ Error en login para {login_data['username']}: {response.status_code}")
                print(f"   {response.text}")
        except Exception as e:
            print(f"❌ Excepción en login para {login_data['username']}: {e}")
    
    print()
    return tokens

def test_user_profile(tokens):
    """Prueba obtener perfil de usuario"""
    print("=== Probando Obtener Perfil de Usuario ===")
    
    for username, token in tokens.items():
        try:
            headers = {"Authorization": f"Token {token}"}
            response = requests.get(f"{BASE_URL}/users/profile/", headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Perfil obtenido para {username}")
                print(f"   Tipo: {data['user_type']}")
                print(f"   Email: {data['email']}")
                if data.get('cliente_profile'):
                    print(f"   Perfil Cliente: ✅")
                if data.get('guardia_profile'):
                    print(f"   Perfil Guardia: ✅")
                if data.get('operador_profile'):
                    print(f"   Perfil Operador: ✅")
            else:
                print(f"❌ Error obteniendo perfil para {username}: {response.status_code}")
        except Exception as e:
            print(f"❌ Excepción obteniendo perfil para {username}: {e}")
    
    print()

def test_guardia_location_update(tokens):
    """Prueba actualización de ubicación del guardia"""
    print("=== Probando Actualización de Ubicación del Guardia ===")
    
    if 'guardia_test' in tokens:
        try:
            headers = {"Authorization": f"Token {tokens['guardia_test']}"}
            location_data = {
                "current_latitude": "19.4326",
                "current_longitude": "-99.1332"
            }
            
            response = requests.post(
                f"{BASE_URL}/users/guardia/location/", 
                json=location_data, 
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Ubicación del guardia actualizada exitosamente")
                print(f"   Latitud: {data['location']['latitude']}")
                print(f"   Longitud: {data['location']['longitude']}")
            else:
                print(f"❌ Error actualizando ubicación: {response.status_code}")
                print(f"   {response.text}")
        except Exception as e:
            print(f"❌ Excepción actualizando ubicación: {e}")
    else:
        print("❌ No hay token de guardia disponible")
    
    print()

def test_duty_status_toggle(tokens):
    """Prueba cambio de estado de servicio del guardia"""
    print("=== Probando Cambio de Estado de Servicio ===")
    
    if 'guardia_test' in tokens:
        try:
            headers = {"Authorization": f"Token {tokens['guardia_test']}"}
            
            response = requests.post(
                f"{BASE_URL}/users/guardia/duty/", 
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Estado de servicio cambiado exitosamente")
                print(f"   Estado: {'En servicio' if data['is_on_duty'] else 'Fuera de servicio'}")
            else:
                print(f"❌ Error cambiando estado: {response.status_code}")
                print(f"   {response.text}")
        except Exception as e:
            print(f"❌ Excepción cambiando estado: {e}")
    else:
        print("❌ No hay token de guardia disponible")
    
    print()

def main():
    """Función principal que ejecuta todas las pruebas"""
    print("🚀 Iniciando pruebas de APIs del Sistema GeoVigia")
    print("=" * 50)
    
    # Ejecutar pruebas en orden
    test_user_registration()
    tokens = test_user_login()
    test_user_profile(tokens)
    test_guardia_location_update(tokens)
    test_duty_status_toggle(tokens)
    
    print("✅ Pruebas completadas")
    print("=" * 50)

if __name__ == "__main__":
    main()

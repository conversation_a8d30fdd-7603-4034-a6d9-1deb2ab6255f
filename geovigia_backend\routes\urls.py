from django.urls import path
from . import views

app_name = 'routes'

urlpatterns = [
    # Gestión de rutas
    path('', views.RouteListCreateView.as_view(), name='route_list_create'),
    path('<int:pk>/', views.RouteDetailView.as_view(), name='route_detail'),
    path('my-routes/', views.my_routes, name='my_routes'),
    path('active/', views.active_routes, name='active_routes'),
    path('today/', views.routes_today, name='routes_today'),
    
    # Asignaciones de guardias
    path('assignments/', views.GuardRouteListCreateView.as_view(), name='guard_assignments'),
    path('assignments/<int:pk>/', views.GuardRouteDetailView.as_view(), name='guard_assignment_detail'),
    path('<int:route_id>/assign/', views.assign_guard_to_route, name='assign_guard'),
    
    # Puntos de control
    path('checkpoints/', views.RouteCheckpointListCreateView.as_view(), name='route_checkpoints_list'),
    path('checkpoints/<int:pk>/', views.RouteCheckpointDetailView.as_view(), name='route_checkpoint_detail'),
    path('<int:route_id>/checkpoints/', views.route_checkpoints, name='route_checkpoints'),
    
    # Ejecución de rutas
    path('executions/', views.RouteExecutionListCreateView.as_view(), name='route_executions'),
    path('<int:route_id>/start/', views.start_route_execution, name='start_route_execution'),
    path('executions/<int:execution_id>/complete/', views.complete_route_execution, name='complete_route_execution'),
]

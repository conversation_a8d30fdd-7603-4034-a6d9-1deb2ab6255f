# Generated by Django 5.2.1 on 2025-05-30 03:11

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WebSocketConnection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('channel_name', models.CharField(max_length=255, unique=True, verbose_name='Nombre del Canal')),
                ('connected_at', models.DateTimeField(auto_now_add=True, verbose_name='Conectado en')),
                ('last_activity', models.DateTimeField(auto_now=True, verbose_name='Última Actividad')),
                ('user_agent', models.TextField(blank=True, verbose_name='User Agent')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='Dirección IP')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='websocket_connections', to=settings.AUTH_USER_MODEL, verbose_name='Usuario')),
            ],
            options={
                'verbose_name': 'Conexión WebSocket',
                'verbose_name_plural': 'Conexiones WebSocket',
                'ordering': ['-connected_at'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='Título')),
                ('message', models.TextField(verbose_name='Mensaje')),
                ('notification_type', models.CharField(choices=[('alert', 'Alerta'), ('info', 'Información'), ('warning', 'Advertencia'), ('success', 'Éxito'), ('emergency', 'Emergencia'), ('route_update', 'Actualización de Ruta'), ('property_alert', 'Alerta de Propiedad'), ('guard_status', 'Estado de Guardia')], default='info', max_length=20, verbose_name='Tipo')),
                ('priority', models.CharField(choices=[('low', 'Baja'), ('normal', 'Normal'), ('high', 'Alta'), ('critical', 'Crítica')], default='normal', max_length=10, verbose_name='Prioridad')),
                ('is_read', models.BooleanField(default=False, verbose_name='Leída')),
                ('is_sent', models.BooleanField(default=False, verbose_name='Enviada')),
                ('extra_data', models.JSONField(blank=True, default=dict, verbose_name='Datos Adicionales')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Creada')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='Enviada en')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='Leída en')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='Expira en')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL, verbose_name='Destinatario')),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_notifications', to=settings.AUTH_USER_MODEL, verbose_name='Remitente')),
            ],
            options={
                'verbose_name': 'Notificación',
                'verbose_name_plural': 'Notificaciones',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['recipient', '-created_at'], name='notificatio_recipie_a972ce_idx'), models.Index(fields=['notification_type', '-created_at'], name='notificatio_notific_d8746a_idx'), models.Index(fields=['priority', '-created_at'], name='notificatio_priorit_3fc670_idx'), models.Index(fields=['is_read', '-created_at'], name='notificatio_is_read_1cb71a_idx')],
            },
        ),
        migrations.CreateModel(
            name='PushSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('endpoint', models.URLField(verbose_name='Endpoint')),
                ('p256dh_key', models.TextField(verbose_name='Clave P256DH')),
                ('auth_key', models.TextField(verbose_name='Clave Auth')),
                ('user_agent', models.TextField(blank=True, verbose_name='User Agent')),
                ('is_active', models.BooleanField(default=True, verbose_name='Activa')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Creada')),
                ('last_used', models.DateTimeField(auto_now=True, verbose_name='Último Uso')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='push_subscriptions', to=settings.AUTH_USER_MODEL, verbose_name='Usuario')),
            ],
            options={
                'verbose_name': 'Suscripción Push',
                'verbose_name_plural': 'Suscripciones Push',
                'unique_together': {('user', 'endpoint')},
            },
        ),
    ]

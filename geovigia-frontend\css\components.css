/* Componentes Reutilizables */

/* Botones */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.5;
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    text-decoration: none;
    transition: all var(--transition-fast);
    white-space: nowrap;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Variantes de botones */
.btn-primary {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-white);
    border-color: var(--secondary-color);
}

.btn-secondary:hover:not(:disabled) {
    background: #475569;
    border-color: #475569;
}

.btn-success {
    background: var(--success-color);
    color: var(--text-white);
    border-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
    background: #059669;
    border-color: #059669;
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-white);
    border-color: var(--warning-color);
}

.btn-warning:hover:not(:disabled) {
    background: #d97706;
    border-color: #d97706;
}

.btn-error {
    background: var(--error-color);
    color: var(--text-white);
    border-color: var(--error-color);
}

.btn-error:hover:not(:disabled) {
    background: #dc2626;
    border-color: #dc2626;
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border-color: var(--border-color);
}

.btn-ghost:hover:not(:disabled) {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: var(--radius-lg);
    position: relative;
}

/* Tamaños de botones */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
}

.btn-full {
    width: 100%;
}

/* Badge para notificaciones */
.badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--error-color);
    color: var(--text-white);
    font-size: 0.625rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Formularios */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    left: var(--spacing-md);
    color: var(--text-muted);
    z-index: 1;
}

.input-group input,
.input-group select,
.input-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    padding-left: 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    background: var(--bg-primary);
}

.input-group input:focus,
.input-group select:focus,
.input-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-group input::placeholder {
    color: var(--text-muted);
}

/* Input sin icono */
input:not(.input-group input),
select:not(.input-group select),
textarea:not(.input-group textarea) {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    background: var(--bg-primary);
}

input:not(.input-group input):focus,
select:not(.input-group select):focus,
textarea:not(.input-group textarea):focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Mensajes de error */
.error-message {
    background: #fef2f2;
    color: var(--error-color);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid #fecaca;
    font-size: 0.875rem;
    margin-top: var(--spacing-md);
}

.success-message {
    background: #f0fdf4;
    color: var(--success-color);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid #bbf7d0;
    font-size: 0.875rem;
    margin-top: var(--spacing-md);
}

/* Tablas */
.table-container {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.table td {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.table tbody tr:hover {
    background: var(--bg-secondary);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Modales */
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-container.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal-container.active .modal {
    transform: scale(1);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    padding: var(--spacing-md);
    min-width: 300px;
    max-width: 400px;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.toast.show {
    transform: translateX(0);
}

.toast-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: var(--text-white);
}

.toast-success .toast-icon {
    background: var(--success-color);
}

.toast-error .toast-icon {
    background: var(--error-color);
}

.toast-warning .toast-icon {
    background: var(--warning-color);
}

.toast-info .toast-icon {
    background: var(--info-color);
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.toast-message {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.toast-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

.status-badge.inactive {
    background: #fee2e2;
    color: #991b1b;
}

.status-badge.pending {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.completed {
    background: #dbeafe;
    color: #1e40af;
}

/* Dropdown */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    border-bottom: 1px solid var(--border-light);
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.pagination-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.pagination-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.pagination-btn.active {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Search Box */
.search-box {
    position: relative;
    max-width: 400px;
}

.search-box input {
    padding-right: 3rem;
}

.search-box .search-icon {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    font-size: 0.875rem;
}

/* Estilos adicionales para el frontend */

/* Form rows */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
}

/* Route items */
.route-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-fast);
}

.route-item:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

.route-info {
    flex: 1;
}

.route-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.route-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.route-details {
    display: flex;
    gap: var(--spacing-lg);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.route-time,
.route-priority,
.route-emergency {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.route-emergency {
    color: var(--warning-color);
    font-weight: 500;
}

/* Activity items */
.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-tertiary);
    color: var(--primary-color);
    font-size: 1.125rem;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.activity-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Execution cards */
.executions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.execution-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.execution-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.execution-card.delayed {
    border-color: var(--warning-color);
    background: #fefbf3;
}

.execution-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.execution-header h4 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.execution-info {
    margin-bottom: var(--spacing-md);
}

.info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.info-item i {
    width: 16px;
    color: var(--text-muted);
}

.execution-progress {
    margin-bottom: var(--spacing-md);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transition: width var(--transition-normal);
}

.progress-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.execution-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Detail rows */
.detail-row {
    display: flex;
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
}

.detail-row strong {
    min-width: 150px;
    color: var(--text-primary);
    font-weight: 600;
}

/* Chart placeholders */
.chart-container {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.chart-container h4 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-placeholder {
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-muted);
    text-align: center;
}

.chart-placeholder p {
    margin: 0 0 var(--spacing-sm) 0;
    font-weight: 500;
}

.chart-placeholder small {
    font-size: 0.75rem;
    opacity: 0.7;
}

/* Summary stats */
.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.summary-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

/* Report sections */
.report-section {
    display: none;
}

.report-section.active {
    display: block;
}

/* Tab buttons */
.report-tabs {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.tab-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.875rem;
    font-weight: 500;
}

.tab-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

/* Date range selector */
.date-range-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
}

.date-range-selector label {
    font-weight: 500;
    color: var(--text-primary);
}

.date-range-selector input {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
}

/* Auto refresh indicator */
.auto-refresh-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.auto-refresh-indicator .text-success {
    color: var(--success-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .route-details {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .executions-grid {
        grid-template-columns: 1fr;
    }

    .summary-charts {
        grid-template-columns: 1fr;
    }

    .date-range-selector {
        flex-direction: column;
        align-items: stretch;
    }

    .report-tabs {
        flex-wrap: wrap;
    }
}

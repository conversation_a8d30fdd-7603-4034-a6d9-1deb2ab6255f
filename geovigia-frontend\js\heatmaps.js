/**
 * Heatmaps Manager - Mapas de Calor para Analytics
 * Maneja la creación y visualización de heatmaps con Leaflet
 */

class HeatmapsManager {
    constructor() {
        this.apiBaseUrl = 'http://127.0.0.1:8000/api';
        this.map = null;
        this.heatmapLayers = new Map(); // Almacenar capas de heatmap
        this.updateInterval = 120000; // 2 minutos para heatmaps
        this.intervalId = null;
        this.currentFilters = {
            days: 7,
            type: 'all'
        };
    }

    /**
     * Inicializar el gestor de heatmaps
     */
    init() {
        console.log('🗺️ Inicializando Heatmaps Manager...');
        this.createHeatmapsSection();
        this.initializeMap();
        this.loadHeatmapData();
        this.startAutoUpdate();
    }

    /**
     * Crear la sección de heatmaps en el dashboard
     */
    createHeatmapsSection() {
        const chartsSection = document.getElementById('charts-section');
        if (!chartsSection) {
            console.error('No se encontró la sección de gráficos');
            return;
        }

        // Buscar si ya existe la sección de heatmaps
        let heatmapsSection = document.getElementById('heatmaps-section');

        if (!heatmapsSection) {
            heatmapsSection = document.createElement('div');
            heatmapsSection.id = 'heatmaps-section';
            heatmapsSection.className = 'heatmaps-section';
            heatmapsSection.innerHTML = this.getHeatmapsHTML();

            // Insertar después de la sección de gráficos
            chartsSection.parentNode.insertBefore(heatmapsSection, chartsSection.nextSibling);
        }
    }

    /**
     * HTML para la sección de heatmaps
     */
    getHeatmapsHTML() {
        return `
            <div class="heatmaps-header">
                <h2>🗺️ Mapas de Calor</h2>
                <div class="heatmaps-controls">
                    <div class="filter-controls">
                        <select id="heatmap-days-filter" class="filter-select">
                            <option value="1">Último día</option>
                            <option value="3">Últimos 3 días</option>
                            <option value="7" selected>Últimos 7 días</option>
                            <option value="14">Últimos 14 días</option>
                            <option value="30">Último mes</option>
                        </select>

                        <select id="heatmap-type-filter" class="filter-select">
                            <option value="all">Todos los tipos</option>
                            <option value="alert">Alertas</option>
                            <option value="emergency">Emergencias</option>
                            <option value="warning">Advertencias</option>
                            <option value="info">Información</option>
                        </select>
                    </div>

                    <div class="heatmap-update-info">
                        <span class="heatmap-update">Actualizado: <span id="heatmap-update-time">--:--:--</span></span>
                        <button id="refresh-heatmap" class="btn-refresh" title="Actualizar heatmaps">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="heatmaps-content">
                <!-- Mapa principal -->
                <div class="heatmap-main">
                    <div class="heatmap-container">
                        <div id="heatmap-map" class="heatmap-map"></div>

                        <!-- Controles de capas -->
                        <div class="heatmap-layers-control">
                            <h4>📊 Capas Disponibles</h4>
                            <div class="layer-toggles">
                                <label class="layer-toggle">
                                    <input type="checkbox" id="layer-incidents" checked>
                                    <span class="layer-color incidents"></span>
                                    <span class="layer-name">Incidentes</span>
                                    <span class="layer-count" id="incidents-count">0</span>
                                </label>

                                <label class="layer-toggle">
                                    <input type="checkbox" id="layer-guards">
                                    <span class="layer-color guards"></span>
                                    <span class="layer-name">Guardias</span>
                                    <span class="layer-count" id="guards-count">0</span>
                                </label>

                                <label class="layer-toggle">
                                    <input type="checkbox" id="layer-properties">
                                    <span class="layer-color properties"></span>
                                    <span class="layer-name">Propiedades</span>
                                    <span class="layer-count" id="properties-count">0</span>
                                </label>

                                <label class="layer-toggle">
                                    <input type="checkbox" id="layer-routes">
                                    <span class="layer-color routes"></span>
                                    <span class="layer-name">Rutas</span>
                                    <span class="layer-count" id="routes-count">0</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Panel de estadísticas -->
                <div class="heatmap-stats">
                    <div class="stat-card">
                        <div class="stat-icon">🔥</div>
                        <div class="stat-info">
                            <div class="stat-label">Zona Caliente</div>
                            <div class="stat-value" id="hotspot-info">Calculando...</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <div class="stat-info">
                            <div class="stat-label">Total Puntos</div>
                            <div class="stat-value" id="total-points">0</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">⏱️</div>
                        <div class="stat-info">
                            <div class="stat-label">Período</div>
                            <div class="stat-value" id="period-info">7 días</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="heatmaps-status">
                <div id="heatmaps-loading" class="loading-indicator" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> Cargando mapas de calor...
                </div>
                <div id="heatmaps-error" class="error-indicator" style="display: none;">
                    <i class="fas fa-exclamation-circle"></i> Error al cargar heatmaps
                </div>
            </div>
        `;
    }

    /**
     * Inicializar el mapa de Leaflet
     */
    initializeMap() {
        // Verificar que Leaflet esté disponible
        if (typeof L === 'undefined') {
            console.error('Leaflet no está disponible');
            return;
        }

        // Crear el mapa centrado en Ciudad de México
        this.map = L.map('heatmap-map').setView([19.4326, -99.1332], 11);

        // Agregar capa base
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 18
        }).addTo(this.map);

        // Configurar eventos de filtros
        this.setupFilterEvents();

        console.log('🗺️ Mapa inicializado correctamente');
    }

    /**
     * Configurar eventos de filtros
     */
    setupFilterEvents() {
        // Filtro de días
        const daysFilter = document.getElementById('heatmap-days-filter');
        if (daysFilter) {
            daysFilter.addEventListener('change', (e) => {
                this.currentFilters.days = parseInt(e.target.value);
                this.loadHeatmapData();
            });
        }

        // Filtro de tipo
        const typeFilter = document.getElementById('heatmap-type-filter');
        if (typeFilter) {
            typeFilter.addEventListener('change', (e) => {
                this.currentFilters.type = e.target.value;
                this.loadHeatmapData();
            });
        }

        // Botón de actualización
        const refreshButton = document.getElementById('refresh-heatmap');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this.loadHeatmapData();
            });
        }

        // Toggles de capas
        const layerToggles = document.querySelectorAll('.layer-toggle input[type="checkbox"]');
        layerToggles.forEach(toggle => {
            toggle.addEventListener('change', (e) => {
                const layerName = e.target.id.replace('layer-', '');
                this.toggleHeatmapLayer(layerName, e.target.checked);
            });
        });
    }

    /**
     * Cargar datos de heatmap desde la API
     */
    async loadHeatmapData() {
        if (!window.authManager || !window.authManager.isAuthenticated()) {
            console.warn('Usuario no autenticado para cargar heatmaps');
            return;
        }

        this.showLoading(true);
        this.hideError();

        try {
            const params = new URLSearchParams({
                days: this.currentFilters.days,
                type: this.currentFilters.type
            });

            const response = await fetch(`${this.apiBaseUrl}/analytics/heatmap/?${params}`, {
                method: 'GET',
                headers: window.authManager.getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error(`Error HTTP: ${response.status}`);
            }

            const data = await response.json();
            this.createHeatmapLayers(data.heatmaps);
            this.updateStatistics(data.statistics);
            this.updateHeatmapTime();

            console.log('✅ Heatmaps actualizados correctamente');

        } catch (error) {
            console.error('❌ Error al cargar heatmaps:', error);
            this.showError(`Error: ${error.message}`);
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Crear capas de heatmap
     */
    createHeatmapLayers(heatmapsData) {
        // Limpiar capas existentes
        this.clearHeatmapLayers();

        // Crear cada capa de heatmap
        Object.keys(heatmapsData).forEach(layerKey => {
            const layerData = heatmapsData[layerKey];
            this.createHeatmapLayer(layerKey, layerData);
        });

        // Actualizar contadores
        this.updateLayerCounts(heatmapsData);
    }

    /**
     * Crear una capa de heatmap individual
     */
    createHeatmapLayer(layerKey, layerData) {
        if (!layerData.data || layerData.data.length === 0) {
            console.warn(`No hay datos para la capa: ${layerKey}`);
            return;
        }

        // Verificar que el plugin de heatmap esté disponible
        if (!L.heatLayer) {
            console.error('Plugin de heatmap no disponible');
            return;
        }

        // Crear capa de heatmap
        const heatmapLayer = L.heatLayer(layerData.data, layerData.options);

        // Almacenar la capa
        this.heatmapLayers.set(layerKey, heatmapLayer);

        // Agregar al mapa si está habilitada
        const toggle = document.getElementById(`layer-${layerKey}`);
        if (toggle && toggle.checked) {
            heatmapLayer.addTo(this.map);
        }

        console.log(`🗺️ Capa de heatmap creada: ${layerData.name} (${layerData.data.length} puntos)`);
    }

    /**
     * Toggle de capa de heatmap
     */
    toggleHeatmapLayer(layerName, enabled) {
        const layer = this.heatmapLayers.get(layerName);
        if (!layer) return;

        if (enabled) {
            layer.addTo(this.map);
        } else {
            this.map.removeLayer(layer);
        }
    }

    /**
     * Limpiar todas las capas de heatmap
     */
    clearHeatmapLayers() {
        this.heatmapLayers.forEach((layer, key) => {
            this.map.removeLayer(layer);
        });
        this.heatmapLayers.clear();
    }

    /**
     * Actualizar contadores de capas
     */
    updateLayerCounts(heatmapsData) {
        Object.keys(heatmapsData).forEach(layerKey => {
            const count = heatmapsData[layerKey].data.length;
            const countElement = document.getElementById(`${layerKey}-count`);
            if (countElement) {
                countElement.textContent = count;
            }
        });
    }

    /**
     * Actualizar estadísticas
     */
    updateStatistics(stats) {
        // Zona caliente
        const hotspotElement = document.getElementById('hotspot-info');
        if (hotspotElement && stats.hotspot) {
            const lat = stats.hotspot.lat.toFixed(4);
            const lng = stats.hotspot.lng.toFixed(4);
            hotspotElement.textContent = `${lat}, ${lng}`;
        }

        // Total de puntos
        const totalElement = document.getElementById('total-points');
        if (totalElement) {
            const total = stats.total_incidents + stats.total_guards +
                         stats.total_properties + stats.total_routes;
            totalElement.textContent = total.toLocaleString();
        }

        // Período
        const periodElement = document.getElementById('period-info');
        if (periodElement) {
            periodElement.textContent = `${stats.period_days} días`;
        }
    }

    /**
     * Actualizar hora de última actualización
     */
    updateHeatmapTime() {
        const timeElement = document.getElementById('heatmap-update-time');
        if (timeElement) {
            timeElement.textContent = new Date().toLocaleTimeString();
        }
    }

    /**
     * Mostrar/ocultar indicador de carga
     */
    showLoading(show) {
        const loadingElement = document.getElementById('heatmaps-loading');
        if (loadingElement) {
            loadingElement.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * Mostrar error
     */
    showError(message) {
        const errorElement = document.getElementById('heatmaps-error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    /**
     * Ocultar error
     */
    hideError() {
        const errorElement = document.getElementById('heatmaps-error');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    /**
     * Iniciar actualización automática
     */
    startAutoUpdate() {
        // Limpiar intervalo anterior si existe
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }

        // Configurar nuevo intervalo
        this.intervalId = setInterval(() => {
            this.loadHeatmapData();
        }, this.updateInterval);

        console.log(`🔄 Auto-actualización de heatmaps cada ${this.updateInterval/1000} segundos`);
    }

    /**
     * Detener actualización automática
     */
    stopAutoUpdate() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    /**
     * Cargar heatmap temporal (actividad por horas)
     */
    async loadTemporalHeatmap(date = null) {
        try {
            const targetDate = date || new Date().toISOString().split('T')[0];
            const params = new URLSearchParams({ date: targetDate });

            const response = await fetch(`${this.apiBaseUrl}/analytics/temporal-heatmap/?${params}`, {
                method: 'GET',
                headers: window.authManager.getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error(`Error HTTP: ${response.status}`);
            }

            const data = await response.json();
            this.createTemporalHeatmapLayer(data.temporal_heatmap);

            console.log('✅ Heatmap temporal cargado correctamente');

        } catch (error) {
            console.error('❌ Error al cargar heatmap temporal:', error);
            this.showError(`Error: ${error.message}`);
        }
    }

    /**
     * Crear capa de heatmap temporal
     */
    createTemporalHeatmapLayer(temporalData) {
        if (!temporalData.hourly_data || temporalData.hourly_data.length === 0) {
            console.warn('No hay datos temporales disponibles');
            return;
        }

        // Verificar que el plugin de heatmap esté disponible
        if (!L.heatLayer) {
            console.error('Plugin de heatmap no disponible');
            return;
        }

        // Preparar datos para el heatmap
        const heatmapData = temporalData.hourly_data.map(hourData => hourData.coordinates);

        // Crear capa de heatmap temporal
        const temporalHeatmapLayer = L.heatLayer(heatmapData, {
            radius: 30,
            blur: 20,
            maxZoom: 18,
            gradient: {
                0.0: 'blue',
                0.2: 'cyan',
                0.4: 'lime',
                0.6: 'yellow',
                0.8: 'orange',
                1.0: 'red'
            }
        });

        // Almacenar la capa
        this.heatmapLayers.set('temporal', temporalHeatmapLayer);

        // Agregar al mapa
        temporalHeatmapLayer.addTo(this.map);

        // Actualizar estadísticas
        this.updateTemporalStatistics(temporalData);
    }

    /**
     * Actualizar estadísticas temporales
     */
    updateTemporalStatistics(temporalData) {
        const statsContainer = document.querySelector('.heatmap-stats');
        if (!statsContainer) return;

        const peakHour = temporalData.peak_hour;
        const totalActivity = temporalData.total_activity;

        const temporalStatsHTML = `
            <div class="temporal-stats">
                <h4>📅 Análisis Temporal</h4>
                <div class="stat-item">
                    <span class="stat-label">Hora Pico:</span>
                    <span class="stat-value">${peakHour}:00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Actividad Total:</span>
                    <span class="stat-value">${totalActivity}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Fecha:</span>
                    <span class="stat-value">${temporalData.date}</span>
                </div>
            </div>
        `;

        statsContainer.insertAdjacentHTML('beforeend', temporalStatsHTML);
    }

    /**
     * Cargar análisis de zonas
     */
    async loadZoneAnalytics() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/analytics/zone-analytics/`, {
                method: 'GET',
                headers: window.authManager.getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error(`Error HTTP: ${response.status}`);
            }

            const data = await response.json();
            this.createZoneAnalyticsLayer(data.zone_analytics);

            console.log('✅ Análisis de zonas cargado correctamente');

        } catch (error) {
            console.error('❌ Error al cargar análisis de zonas:', error);
            this.showError(`Error: ${error.message}`);
        }
    }

    /**
     * Crear capa de análisis de zonas
     */
    createZoneAnalyticsLayer(zoneData) {
        if (!zoneData.zones || zoneData.zones.length === 0) {
            console.warn('No hay datos de zonas disponibles');
            return;
        }

        // Limpiar capas de zonas anteriores
        this.map.eachLayer(layer => {
            if (layer.options && layer.options.className === 'zone-overlay') {
                this.map.removeLayer(layer);
            }
        });

        // Crear overlays para cada zona
        zoneData.zones.forEach(zone => {
            const bounds = [
                [zone.bounds.lat_min, zone.bounds.lng_min],
                [zone.bounds.lat_max, zone.bounds.lng_max]
            ];

            // Color basado en el tipo de zona
            let color, fillOpacity;
            switch (zone.zone_type) {
                case 'high':
                    color = '#ff4757';
                    fillOpacity = 0.4;
                    break;
                case 'medium':
                    color = '#ffa502';
                    fillOpacity = 0.3;
                    break;
                default:
                    color = '#2ed573';
                    fillOpacity = 0.2;
            }

            const rectangle = L.rectangle(bounds, {
                color: color,
                weight: 2,
                fillOpacity: fillOpacity,
                className: 'zone-overlay'
            }).addTo(this.map);

            // Popup con información de la zona
            rectangle.bindPopup(`
                <div class="zone-popup">
                    <h4>📍 ${zone.zone_id}</h4>
                    <p><strong>Tipo:</strong> ${zone.zone_type}</p>
                    <p><strong>Propiedades:</strong> ${zone.properties_count}</p>
                    <p><strong>Incidentes:</strong> ${zone.incidents_count}</p>
                    <p><strong>Actividad:</strong> ${(zone.activity_level * 100).toFixed(1)}%</p>
                </div>
            `);
        });

        // Actualizar estadísticas de zonas
        this.updateZoneStatistics(zoneData.statistics);
    }

    /**
     * Actualizar estadísticas de zonas
     */
    updateZoneStatistics(statistics) {
        const statsContainer = document.querySelector('.heatmap-stats');
        if (!statsContainer) return;

        const zoneStatsHTML = `
            <div class="zone-stats">
                <h4>🗺️ Análisis de Zonas</h4>
                <div class="stat-item">
                    <span class="stat-label">Zonas Totales:</span>
                    <span class="stat-value">${statistics.total_zones}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Alta Actividad:</span>
                    <span class="stat-value">${statistics.high_activity_zones}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Actividad Promedio:</span>
                    <span class="stat-value">${(statistics.average_activity * 100).toFixed(1)}%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Zona Más Activa:</span>
                    <span class="stat-value">${statistics.most_active_zone}</span>
                </div>
            </div>
        `;

        statsContainer.insertAdjacentHTML('beforeend', zoneStatsHTML);
    }

    /**
     * Destruir el gestor de heatmaps
     */
    destroy() {
        this.stopAutoUpdate();
        this.clearHeatmapLayers();

        if (this.map) {
            this.map.remove();
            this.map = null;
        }

        const heatmapsSection = document.getElementById('heatmaps-section');
        if (heatmapsSection) {
            heatmapsSection.remove();
        }
    }
}

// Instancia global del gestor de heatmaps
window.heatmapsManager = null;

// Función para inicializar heatmaps
function initHeatmaps() {
    if (!window.heatmapsManager) {
        window.heatmapsManager = new HeatmapsManager();
        window.heatmapsManager.init();
    }
}

// Función para destruir heatmaps
function destroyHeatmaps() {
    if (window.heatmapsManager) {
        window.heatmapsManager.destroy();
        window.heatmapsManager = null;
    }
}

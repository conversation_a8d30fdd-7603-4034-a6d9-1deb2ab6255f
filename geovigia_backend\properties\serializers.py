from rest_framework import serializers
from .models import Property, PropertyOwnership, PropertyPerimeter
from users.models import CustomUser


class PropertySerializer(serializers.ModelSerializer):
    """
    Serializer para el modelo Property
    """
    coordinates = serializers.ReadOnlyField()
    full_address = serializers.ReadOnlyField()
    is_high_priority = serializers.ReadOnlyField()

    class Meta:
        model = Property
        fields = [
            'id', 'name', 'property_type', 'address', 'neighborhood',
            'city', 'postal_code', 'latitude', 'longitude', 'description',
            'area_size', 'floors', 'status', 'is_monitored', 'priority_level',
            'emergency_contact_name', 'emergency_contact_phone',
            'created_at', 'updated_at', 'coordinates', 'full_address',
            'is_high_priority'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate_latitude(self, value):
        """Validar que la latitud esté en el rango válido"""
        if not -90 <= float(value) <= 90:
            raise serializers.ValidationError(
                "La latitud debe estar entre -90 y 90 grados"
            )
        return value

    def validate_longitude(self, value):
        """Validar que la longitud esté en el rango válido"""
        if not -180 <= float(value) <= 180:
            raise serializers.ValidationError(
                "La longitud debe estar entre -180 y 180 grados"
            )
        return value


class PropertyOwnershipSerializer(serializers.ModelSerializer):
    """
    Serializer para el modelo PropertyOwnership
    """
    client_username = serializers.CharField(source='client.username', read_only=True)
    client_name = serializers.CharField(source='client.get_full_name', read_only=True)
    property_name = serializers.CharField(source='property.name', read_only=True)

    class Meta:
        model = PropertyOwnership
        fields = [
            'id', 'client', 'property', 'ownership_type', 'is_primary',
            'can_receive_alerts', 'notes', 'created_at',
            'client_username', 'client_name', 'property_name'
        ]
        read_only_fields = ['created_at']

    def validate(self, data):
        """Validación personalizada"""
        # Verificar que el cliente sea del tipo correcto
        client = data.get('client')
        if client and not client.is_cliente:
            raise serializers.ValidationError(
                "Solo los usuarios tipo 'cliente' pueden ser asignados a propiedades"
            )
        return data


class PropertyPerimeterSerializer(serializers.ModelSerializer):
    """
    Serializer para el modelo PropertyPerimeter
    """
    property_name = serializers.CharField(source='property.name', read_only=True)
    area_coverage = serializers.SerializerMethodField()

    class Meta:
        model = PropertyPerimeter
        fields = [
            'id', 'property', 'perimeter_type', 'radius_meters',
            'width_meters', 'height_meters', 'polygon_coordinates',
            'is_active', 'alert_on_entry', 'alert_on_exit', 'description',
            'created_at', 'updated_at', 'property_name', 'area_coverage'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate(self, data):
        """Validación personalizada según el tipo de perímetro"""
        perimeter_type = data.get('perimeter_type')

        if perimeter_type == 'rectangular':
            if not data.get('width_meters') or not data.get('height_meters'):
                raise serializers.ValidationError(
                    "Para perímetro rectangular se requieren ancho y alto"
                )
        elif perimeter_type == 'polygon':
            polygon_coords = data.get('polygon_coordinates')
            if not polygon_coords:
                raise serializers.ValidationError(
                    "Para perímetro de polígono se requieren las coordenadas"
                )
            if len(polygon_coords) < 3:
                raise serializers.ValidationError(
                    "Un polígono requiere al menos 3 puntos"
                )

        return data

    def get_area_coverage(self, obj):
        """Calcular el área de cobertura del perímetro"""
        return obj.get_area_coverage()


class PropertyDetailSerializer(PropertySerializer):
    """
    Serializer detallado para Property que incluye relaciones
    """
    ownerships = PropertyOwnershipSerializer(many=True, read_only=True)
    perimeter = PropertyPerimeterSerializer(read_only=True)

    class Meta(PropertySerializer.Meta):
        fields = PropertySerializer.Meta.fields + ['ownerships', 'perimeter']


class PropertyCreateSerializer(serializers.ModelSerializer):
    """
    Serializer para crear propiedades con asignación automática al cliente
    """
    assign_to_current_user = serializers.BooleanField(
        default=True,
        write_only=True,
        help_text="Asignar automáticamente la propiedad al usuario actual"
    )

    class Meta:
        model = Property
        fields = [
            'id', 'name', 'property_type', 'address', 'neighborhood',
            'city', 'postal_code', 'latitude', 'longitude', 'description',
            'area_size', 'floors', 'priority_level',
            'emergency_contact_name', 'emergency_contact_phone',
            'assign_to_current_user', 'coordinates', 'full_address'
        ]
        read_only_fields = ['id', 'coordinates', 'full_address']

    def create(self, validated_data):
        """Crear propiedad y asignarla al usuario si se especifica"""
        assign_to_user = validated_data.pop('assign_to_current_user', True)
        property_instance = super().create(validated_data)

        # Asignar al usuario actual si es cliente y se especifica
        request = self.context.get('request')
        if assign_to_user and request and request.user.is_cliente:
            PropertyOwnership.objects.create(
                client=request.user,
                property=property_instance,
                ownership_type='propietario',
                is_primary=True
            )

        return property_instance


class ClientPropertyListSerializer(serializers.ModelSerializer):
    """
    Serializer simplificado para listar propiedades de un cliente
    """
    ownership_type = serializers.CharField(source='ownerships.first.ownership_type', read_only=True)
    is_primary = serializers.BooleanField(source='ownerships.first.is_primary', read_only=True)

    class Meta:
        model = Property
        fields = [
            'id', 'name', 'property_type', 'full_address', 'status',
            'is_monitored', 'priority_level', 'ownership_type', 'is_primary'
        ]


class PropertyLocationSerializer(serializers.ModelSerializer):
    """
    Serializer para obtener solo información de ubicación de propiedades
    """
    class Meta:
        model = Property
        fields = [
            'id', 'name', 'latitude', 'longitude', 'coordinates',
            'status', 'is_monitored', 'priority_level'
        ]

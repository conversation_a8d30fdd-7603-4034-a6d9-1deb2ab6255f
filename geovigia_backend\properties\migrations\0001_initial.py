# Generated by Django 5.2.1 on 2025-05-29 00:38

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Property',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Nombre identificativo de la propiedad', max_length=100, verbose_name='Nombre de la Propiedad')),
                ('property_type', models.CharField(choices=[('casa', 'Casa'), ('apartamento', 'Apartamento'), ('oficina', 'Oficina'), ('local', 'Local Comercial'), ('bodega', 'Bodega'), ('otro', '<PERSON>tro')], default='casa', max_length=20, verbose_name='Tipo de Propiedad')),
                ('address', models.TextField(verbose_name='Dirección Completa')),
                ('neighborhood', models.CharField(blank=True, max_length=100, verbose_name='Barrio/Colonia')),
                ('city', models.CharField(default='Ciudad', max_length=100, verbose_name='Ciudad')),
                ('postal_code', models.CharField(blank=True, max_length=10, verbose_name='Código Postal')),
                ('latitude', models.DecimalField(decimal_places=8, help_text='Coordenada de latitud de la propiedad', max_digits=10, verbose_name='Latitud')),
                ('longitude', models.DecimalField(decimal_places=8, help_text='Coordenada de longitud de la propiedad', max_digits=11, verbose_name='Longitud')),
                ('description', models.TextField(blank=True, help_text='Descripción adicional de la propiedad', verbose_name='Descripción')),
                ('area_size', models.PositiveIntegerField(blank=True, help_text='Tamaño del área en metros cuadrados', null=True, verbose_name='Tamaño del Área (m²)')),
                ('floors', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(50)], verbose_name='Número de Pisos')),
                ('status', models.CharField(choices=[('activa', 'Activa'), ('inactiva', 'Inactiva'), ('mantenimiento', 'En Mantenimiento'), ('suspendida', 'Suspendida')], default='activa', max_length=20, verbose_name='Estado')),
                ('is_monitored', models.BooleanField(default=True, help_text='Indica si la propiedad está siendo monitoreada activamente', verbose_name='Bajo Monitoreo')),
                ('priority_level', models.PositiveIntegerField(default=1, help_text='Nivel de prioridad para el monitoreo (1=Baja, 5=Crítica)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Nivel de Prioridad')),
                ('emergency_contact_name', models.CharField(blank=True, max_length=100, verbose_name='Contacto de Emergencia')),
                ('emergency_contact_phone', models.CharField(blank=True, max_length=17, verbose_name='Teléfono de Emergencia')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Fecha de Creación')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Última Actualización')),
            ],
            options={
                'verbose_name': 'Propiedad',
                'verbose_name_plural': 'Propiedades',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['latitude', 'longitude'], name='properties__latitud_6eb2b0_idx'), models.Index(fields=['status', 'is_monitored'], name='properties__status_34e282_idx'), models.Index(fields=['priority_level'], name='properties__priorit_a75dc5_idx')],
            },
        ),
        migrations.CreateModel(
            name='PropertyPerimeter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('perimeter_type', models.CharField(choices=[('circular', 'Circular'), ('rectangular', 'Rectangular'), ('polygon', 'Polígono Personalizado')], default='circular', max_length=20, verbose_name='Tipo de Perímetro')),
                ('radius_meters', models.PositiveIntegerField(default=50, help_text='Radio del perímetro circular en metros', verbose_name='Radio en Metros')),
                ('width_meters', models.PositiveIntegerField(blank=True, help_text='Ancho del perímetro rectangular', null=True, verbose_name='Ancho en Metros')),
                ('height_meters', models.PositiveIntegerField(blank=True, help_text='Alto del perímetro rectangular', null=True, verbose_name='Alto en Metros')),
                ('polygon_coordinates', models.JSONField(blank=True, help_text='Array de coordenadas [lat, lng] que definen el polígono', null=True, verbose_name='Coordenadas del Polígono')),
                ('is_active', models.BooleanField(default=True, help_text='Indica si el perímetro está activo para monitoreo', verbose_name='Perímetro Activo')),
                ('alert_on_entry', models.BooleanField(default=True, help_text='Generar alerta cuando el guardia entra al perímetro', verbose_name='Alerta al Entrar')),
                ('alert_on_exit', models.BooleanField(default=True, help_text='Generar alerta cuando el guardia sale del perímetro', verbose_name='Alerta al Salir')),
                ('description', models.TextField(blank=True, help_text='Descripción del perímetro y sus características', verbose_name='Descripción')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Fecha de Creación')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Última Actualización')),
                ('property', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='perimeter', to='properties.property', verbose_name='Propiedad')),
            ],
            options={
                'verbose_name': 'Perímetro de Propiedad',
                'verbose_name_plural': 'Perímetros de Propiedades',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PropertyOwnership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ownership_type', models.CharField(choices=[('propietario', 'Propietario'), ('inquilino', 'Inquilino'), ('administrador', 'Administrador'), ('contacto', 'Contacto Autorizado')], default='propietario', max_length=20, verbose_name='Tipo de Relación')),
                ('is_primary', models.BooleanField(default=False, help_text='Indica si es el contacto principal para esta propiedad', verbose_name='Contacto Principal')),
                ('can_receive_alerts', models.BooleanField(default=True, help_text='Indica si puede recibir alertas de esta propiedad', verbose_name='Puede Recibir Alertas')),
                ('notes', models.TextField(blank=True, help_text='Notas adicionales sobre la relación', verbose_name='Notas')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Fecha de Asignación')),
                ('client', models.ForeignKey(limit_choices_to={'user_type': 'cliente'}, on_delete=django.db.models.deletion.CASCADE, related_name='property_ownerships', to=settings.AUTH_USER_MODEL, verbose_name='Cliente')),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ownerships', to='properties.property', verbose_name='Propiedad')),
            ],
            options={
                'verbose_name': 'Asignación de Propiedad',
                'verbose_name_plural': 'Asignaciones de Propiedades',
                'ordering': ['-created_at'],
                'unique_together': {('client', 'property')},
            },
        ),
    ]

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from users.models import CustomUser
from properties.models import Property


class Route(models.Model):
    """
    Modelo para representar una ruta de vigilancia en el sistema GeoVigia
    """

    STATUS_CHOICES = (
        ('activa', 'Activa'),
        ('inactiva', 'Inactiva'),
        ('mantenimiento', 'En Mantenimiento'),
        ('suspendida', 'Suspendida'),
    )

    FREQUENCY_CHOICES = (
        ('diaria', 'Diaria'),
        ('semanal', 'Semanal'),
        ('quincenal', 'Quincenal'),
        ('mensual', 'Mensual'),
        ('personalizada', 'Personalizada'),
    )

    # Información básica
    name = models.CharField(
        max_length=100,
        verbose_name='Nombre de la Ruta',
        help_text='Nombre identificativo de la ruta'
    )

    description = models.TextField(
        blank=True,
        verbose_name='Descripción',
        help_text='Descripción detallada de la ruta y sus objetivos'
    )

    # Configuración de la ruta
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='activa',
        verbose_name='Estado'
    )

    frequency = models.CharField(
        max_length=20,
        choices=FREQUENCY_CHOICES,
        default='diaria',
        verbose_name='Frecuencia'
    )

    # Horarios
    start_time = models.TimeField(
        verbose_name='Hora de Inicio',
        help_text='Hora de inicio del recorrido'
    )

    end_time = models.TimeField(
        verbose_name='Hora de Fin',
        help_text='Hora estimada de finalización del recorrido'
    )

    estimated_duration_minutes = models.PositiveIntegerField(
        verbose_name='Duración Estimada (minutos)',
        help_text='Tiempo estimado para completar la ruta en minutos'
    )

    # Configuración avanzada
    max_deviation_meters = models.PositiveIntegerField(
        default=50,
        verbose_name='Desviación Máxima (metros)',
        help_text='Distancia máxima permitida de desviación de la ruta'
    )

    priority_level = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name='Nivel de Prioridad',
        help_text='Nivel de prioridad de la ruta (1=Baja, 5=Crítica)'
    )

    is_emergency_route = models.BooleanField(
        default=False,
        verbose_name='Ruta de Emergencia',
        help_text='Indica si es una ruta de emergencia'
    )

    # Días de la semana (para frecuencia personalizada)
    monday = models.BooleanField(default=True, verbose_name='Lunes')
    tuesday = models.BooleanField(default=True, verbose_name='Martes')
    wednesday = models.BooleanField(default=True, verbose_name='Miércoles')
    thursday = models.BooleanField(default=True, verbose_name='Jueves')
    friday = models.BooleanField(default=True, verbose_name='Viernes')
    saturday = models.BooleanField(default=False, verbose_name='Sábado')
    sunday = models.BooleanField(default=False, verbose_name='Domingo')

    # Propiedades incluidas en la ruta
    properties = models.ManyToManyField(
        Property,
        through='RouteProperty',
        related_name='routes',
        verbose_name='Propiedades'
    )

    # Metadatos
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Fecha de Creación'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='Última Actualización'
    )

    created_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'user_type': 'operador'},
        related_name='created_routes',
        verbose_name='Creado por'
    )

    class Meta:
        verbose_name = 'Ruta'
        verbose_name_plural = 'Rutas'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'frequency']),
            models.Index(fields=['start_time', 'end_time']),
            models.Index(fields=['priority_level']),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_frequency_display()})"

    @property
    def is_active(self):
        """Verifica si la ruta está activa"""
        return self.status == 'activa'

    @property
    def is_high_priority(self):
        """Verifica si la ruta es de alta prioridad"""
        return self.priority_level >= 4

    @property
    def active_days(self):
        """Retorna los días activos de la semana"""
        days = []
        if self.monday: days.append('Lunes')
        if self.tuesday: days.append('Martes')
        if self.wednesday: days.append('Miércoles')
        if self.thursday: days.append('Jueves')
        if self.friday: days.append('Viernes')
        if self.saturday: days.append('Sábado')
        if self.sunday: days.append('Domingo')
        return days

    def is_active_today(self):
        """Verifica si la ruta está activa hoy"""
        if not self.is_active:
            return False

        today = timezone.now().weekday()  # 0=Monday, 6=Sunday
        day_mapping = {
            0: self.monday,
            1: self.tuesday,
            2: self.wednesday,
            3: self.thursday,
            4: self.friday,
            5: self.saturday,
            6: self.sunday,
        }
        return day_mapping.get(today, False)

    def get_total_properties(self):
        """Retorna el número total de propiedades en la ruta"""
        return self.properties.count()

    def get_checkpoint_count(self):
        """Retorna el número total de puntos de control"""
        return self.checkpoints.count()


class RouteProperty(models.Model):
    """
    Modelo intermedio para la relación entre rutas y propiedades
    """
    route = models.ForeignKey(
        Route,
        on_delete=models.CASCADE,
        related_name='route_properties',
        verbose_name='Ruta'
    )

    property = models.ForeignKey(
        Property,
        on_delete=models.CASCADE,
        related_name='property_routes',
        verbose_name='Propiedad'
    )

    order = models.PositiveIntegerField(
        verbose_name='Orden',
        help_text='Orden de visita en la ruta (1, 2, 3...)'
    )

    estimated_time_minutes = models.PositiveIntegerField(
        default=5,
        verbose_name='Tiempo Estimado (minutos)',
        help_text='Tiempo estimado de inspección en esta propiedad'
    )

    is_mandatory = models.BooleanField(
        default=True,
        verbose_name='Visita Obligatoria',
        help_text='Indica si la visita a esta propiedad es obligatoria'
    )

    notes = models.TextField(
        blank=True,
        verbose_name='Notas',
        help_text='Notas específicas para esta propiedad en la ruta'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Fecha de Asignación'
    )

    class Meta:
        verbose_name = 'Propiedad en Ruta'
        verbose_name_plural = 'Propiedades en Rutas'
        unique_together = ['route', 'property']
        ordering = ['route', 'order']

    def __str__(self):
        return f"{self.route.name} - {self.property.name} (#{self.order})"


class GuardRoute(models.Model):
    """
    Modelo para asignar guardias a rutas específicas
    """

    ASSIGNMENT_STATUS = (
        ('activa', 'Activa'),
        ('temporal', 'Temporal'),
        ('suspendida', 'Suspendida'),
        ('finalizada', 'Finalizada'),
    )

    guard = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'guardia'},
        related_name='assigned_routes',
        verbose_name='Guardia'
    )

    route = models.ForeignKey(
        Route,
        on_delete=models.CASCADE,
        related_name='assigned_guards',
        verbose_name='Ruta'
    )

    status = models.CharField(
        max_length=20,
        choices=ASSIGNMENT_STATUS,
        default='activa',
        verbose_name='Estado de Asignación'
    )

    start_date = models.DateField(
        verbose_name='Fecha de Inicio',
        help_text='Fecha de inicio de la asignación'
    )

    end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name='Fecha de Fin',
        help_text='Fecha de fin de la asignación (opcional para asignaciones permanentes)'
    )

    is_primary_guard = models.BooleanField(
        default=False,
        verbose_name='Guardia Principal',
        help_text='Indica si es el guardia principal para esta ruta'
    )

    notes = models.TextField(
        blank=True,
        verbose_name='Notas',
        help_text='Notas sobre la asignación'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Fecha de Asignación'
    )

    assigned_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'user_type': 'operador'},
        related_name='guard_assignments_made',
        verbose_name='Asignado por'
    )

    class Meta:
        verbose_name = 'Asignación de Guardia'
        verbose_name_plural = 'Asignaciones de Guardias'
        unique_together = ['guard', 'route', 'start_date']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.guard.username} - {self.route.name}"

    @property
    def is_active(self):
        """Verifica si la asignación está activa"""
        if self.status != 'activa':
            return False

        today = timezone.now().date()
        if today < self.start_date:
            return False

        if self.end_date and today > self.end_date:
            return False

        return True


class RouteCheckpoint(models.Model):
    """
    Modelo para puntos de control específicos en una ruta
    """

    CHECKPOINT_TYPES = (
        ('inicio', 'Punto de Inicio'),
        ('propiedad', 'Propiedad'),
        ('intermedio', 'Punto Intermedio'),
        ('fin', 'Punto de Fin'),
        ('emergencia', 'Punto de Emergencia'),
    )

    route = models.ForeignKey(
        Route,
        on_delete=models.CASCADE,
        related_name='checkpoints',
        verbose_name='Ruta'
    )

    name = models.CharField(
        max_length=100,
        verbose_name='Nombre del Punto',
        help_text='Nombre identificativo del punto de control'
    )

    checkpoint_type = models.CharField(
        max_length=20,
        choices=CHECKPOINT_TYPES,
        default='intermedio',
        verbose_name='Tipo de Punto'
    )

    # Ubicación del punto de control
    latitude = models.DecimalField(
        max_digits=10,
        decimal_places=8,
        verbose_name='Latitud'
    )

    longitude = models.DecimalField(
        max_digits=11,
        decimal_places=8,
        verbose_name='Longitud'
    )

    # Orden en la ruta
    order = models.PositiveIntegerField(
        verbose_name='Orden',
        help_text='Orden del punto en la ruta'
    )

    # Configuración del punto
    radius_meters = models.PositiveIntegerField(
        default=20,
        verbose_name='Radio de Detección (metros)',
        help_text='Radio en metros para considerar que se visitó el punto'
    )

    is_mandatory = models.BooleanField(
        default=True,
        verbose_name='Punto Obligatorio',
        help_text='Indica si es obligatorio pasar por este punto'
    )

    estimated_time_minutes = models.PositiveIntegerField(
        default=2,
        verbose_name='Tiempo Estimado (minutos)',
        help_text='Tiempo estimado en este punto de control'
    )

    # Relación con propiedad (opcional)
    related_property = models.ForeignKey(
        Property,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='checkpoints',
        verbose_name='Propiedad Asociada'
    )

    description = models.TextField(
        blank=True,
        verbose_name='Descripción',
        help_text='Descripción del punto de control'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Fecha de Creación'
    )

    class Meta:
        verbose_name = 'Punto de Control'
        verbose_name_plural = 'Puntos de Control'
        unique_together = ['route', 'order']
        ordering = ['route', 'order']
        indexes = [
            models.Index(fields=['latitude', 'longitude']),
            models.Index(fields=['checkpoint_type']),
        ]

    def __str__(self):
        return f"{self.route.name} - {self.name} (#{self.order})"

    @property
    def coordinates(self):
        """Retorna las coordenadas como tupla"""
        return (float(self.latitude), float(self.longitude))


class RouteExecution(models.Model):
    """
    Modelo para registrar la ejecución de una ruta por un guardia
    """

    EXECUTION_STATUS = (
        ('iniciada', 'Iniciada'),
        ('en_progreso', 'En Progreso'),
        ('completada', 'Completada'),
        ('incompleta', 'Incompleta'),
        ('cancelada', 'Cancelada'),
    )

    route = models.ForeignKey(
        Route,
        on_delete=models.CASCADE,
        related_name='executions',
        verbose_name='Ruta'
    )

    guard = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        limit_choices_to={'user_type': 'guardia'},
        related_name='route_executions',
        verbose_name='Guardia'
    )

    status = models.CharField(
        max_length=20,
        choices=EXECUTION_STATUS,
        default='iniciada',
        verbose_name='Estado'
    )

    # Tiempos de ejecución
    start_time = models.DateTimeField(
        verbose_name='Hora de Inicio'
    )

    end_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Hora de Fin'
    )

    planned_start_time = models.DateTimeField(
        verbose_name='Hora Planificada de Inicio'
    )

    planned_end_time = models.DateTimeField(
        verbose_name='Hora Planificada de Fin'
    )

    # Métricas de ejecución
    total_distance_meters = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='Distancia Total (metros)',
        help_text='Distancia total recorrida en metros'
    )

    checkpoints_visited = models.PositiveIntegerField(
        default=0,
        verbose_name='Puntos Visitados',
        help_text='Número de puntos de control visitados'
    )

    checkpoints_total = models.PositiveIntegerField(
        verbose_name='Total de Puntos',
        help_text='Número total de puntos de control en la ruta'
    )

    properties_visited = models.PositiveIntegerField(
        default=0,
        verbose_name='Propiedades Visitadas',
        help_text='Número de propiedades visitadas'
    )

    properties_total = models.PositiveIntegerField(
        verbose_name='Total de Propiedades',
        help_text='Número total de propiedades en la ruta'
    )

    # Observaciones
    notes = models.TextField(
        blank=True,
        verbose_name='Observaciones',
        help_text='Observaciones del guardia sobre la ejecución'
    )

    incidents_reported = models.PositiveIntegerField(
        default=0,
        verbose_name='Incidentes Reportados',
        help_text='Número de incidentes reportados durante la ruta'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Fecha de Creación'
    )

    class Meta:
        verbose_name = 'Ejecución de Ruta'
        verbose_name_plural = 'Ejecuciones de Rutas'
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['status', 'start_time']),
            models.Index(fields=['guard', 'start_time']),
        ]

    def __str__(self):
        return f"{self.route.name} - {self.guard.username} ({self.start_time.strftime('%Y-%m-%d %H:%M')})"

    @property
    def duration_minutes(self):
        """Calcula la duración en minutos"""
        if self.end_time:
            delta = self.end_time - self.start_time
            return int(delta.total_seconds() / 60)
        return None

    @property
    def completion_percentage(self):
        """Calcula el porcentaje de completitud"""
        if self.checkpoints_total > 0:
            return (self.checkpoints_visited / self.checkpoints_total) * 100
        return 0

    @property
    def is_delayed(self):
        """Verifica si la ejecución está retrasada"""
        if self.status == 'completada' and self.end_time:
            return self.end_time > self.planned_end_time
        elif self.status in ['iniciada', 'en_progreso']:
            return timezone.now() > self.planned_end_time
        return False

    @property
    def is_on_time(self):
        """Verifica si la ejecución está a tiempo"""
        return not self.is_delayed

/**
 * Sistema de Notificaciones en Tiempo Real para GeoVigia
 * Maneja WebSockets, Push Notifications y notificaciones visuales
 */

class GeoVigiaNotifications {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.heartbeatInterval = null;
        this.notifications = [];
        this.unreadCount = 0;

        // Configuración
        this.config = {
            wsUrl: this.getWebSocketUrl(),
            apiUrl: '/api/notifications/',
            maxNotifications: 50,
            heartbeatInterval: 30000, // 30 segundos
            notificationDuration: 5000 // 5 segundos
        };

        this.init();
    }

    /**
     * Inicializa el sistema de notificaciones
     */
    init() {
        console.log('🔔 Inicializando sistema de notificaciones...');

        // Verificar soporte de WebSocket
        if (!window.WebSocket) {
            console.error('❌ WebSocket no soportado en este navegador');
            return;
        }

        // Inicializar componentes
        this.initializeUI();
        this.connectWebSocket();
        this.requestNotificationPermission();
        this.loadExistingNotifications();

        // Registrar Service Worker para Push Notifications
        this.registerServiceWorker();

        console.log('✅ Sistema de notificaciones inicializado');
    }

    /**
     * Obtiene la URL del WebSocket
     */
    getWebSocketUrl() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        return `${protocol}//${host}/ws/notifications/`;
    }

    /**
     * Conecta al WebSocket
     */
    connectWebSocket() {
        try {
            console.log('🔌 Conectando WebSocket...');

            this.socket = new WebSocket(this.config.wsUrl);

            this.socket.onopen = (event) => {
                console.log('✅ WebSocket conectado');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.startHeartbeat();
                this.showToast('Conectado al sistema de notificaciones', 'success');
            };

            this.socket.onmessage = (event) => {
                this.handleWebSocketMessage(event);
            };

            this.socket.onclose = (event) => {
                console.log('🔌 WebSocket desconectado:', event.code);
                this.isConnected = false;
                this.stopHeartbeat();
                this.handleReconnection();
            };

            this.socket.onerror = (error) => {
                console.error('❌ Error en WebSocket:', error);
                this.showToast('Error de conexión', 'error');
            };

        } catch (error) {
            console.error('❌ Error conectando WebSocket:', error);
        }
    }

    /**
     * Maneja mensajes del WebSocket
     */
    handleWebSocketMessage(event) {
        try {
            const data = JSON.parse(event.data);

            switch (data.type) {
                case 'notification':
                    this.handleNotification(data.notification);
                    break;

                case 'alert':
                    this.handleAlert(data.alert);
                    break;

                case 'location_update':
                    this.handleLocationUpdate(data.location);
                    break;

                case 'route_update':
                    this.handleRouteUpdate(data.route);
                    break;

                case 'system_message':
                    this.handleSystemMessage(data.message);
                    break;

                case 'pong':
                    // Respuesta al ping
                    break;

                default:
                    console.log('📨 Mensaje WebSocket no reconocido:', data);
            }

        } catch (error) {
            console.error('❌ Error procesando mensaje WebSocket:', error);
        }
    }

    /**
     * Maneja notificaciones recibidas
     */
    handleNotification(notification) {
        console.log('🔔 Nueva notificación:', notification);

        // Agregar a la lista
        this.notifications.unshift(notification);

        // Limitar número de notificaciones
        if (this.notifications.length > this.config.maxNotifications) {
            this.notifications = this.notifications.slice(0, this.config.maxNotifications);
        }

        // Actualizar contador
        if (!notification.is_read) {
            this.unreadCount++;
            this.updateUnreadCount();
        }

        // Mostrar notificación visual
        this.showNotification(notification);

        // Actualizar UI
        this.updateNotificationsList();

        // Reproducir sonido si está habilitado
        this.playNotificationSound(notification);
    }

    /**
     * Maneja alertas de emergencia
     */
    handleAlert(alert) {
        console.log('🚨 Nueva alerta:', alert);

        // Mostrar alerta prominente
        this.showAlert(alert);

        // Reproducir sonido de alerta
        this.playAlertSound();

        // Vibrar si está disponible
        if (navigator.vibrate) {
            navigator.vibrate([200, 100, 200, 100, 200]);
        }
    }

    /**
     * Maneja actualizaciones de ubicación
     */
    handleLocationUpdate(location) {
        console.log('📍 Actualización de ubicación:', location);

        // Actualizar mapa si está disponible
        if (window.geoVigiaMaps) {
            window.geoVigiaMaps.updateGuardLocation(location);
        }

        // Emitir evento personalizado
        window.dispatchEvent(new CustomEvent('guardLocationUpdate', {
            detail: location
        }));
    }

    /**
     * Maneja actualizaciones de ruta
     */
    handleRouteUpdate(route) {
        console.log('🛣️ Actualización de ruta:', route);

        // Mostrar notificación
        this.showToast(`Ruta ${route.action}: ${route.name}`, 'info');

        // Emitir evento personalizado
        window.dispatchEvent(new CustomEvent('routeUpdate', {
            detail: route
        }));
    }

    /**
     * Maneja mensajes del sistema
     */
    handleSystemMessage(message) {
        console.log('💬 Mensaje del sistema:', message);

        if (message.type === 'guard_status_update') {
            this.handleGuardStatusUpdate(message.data);
        }
    }

    /**
     * Maneja actualizaciones de estado de guardia
     */
    handleGuardStatusUpdate(data) {
        console.log('👮 Estado de guardia actualizado:', data);

        // Emitir evento personalizado
        window.dispatchEvent(new CustomEvent('guardStatusUpdate', {
            detail: data
        }));
    }

    /**
     * Envía un ping al servidor
     */
    sendPing() {
        if (this.isConnected && this.socket) {
            this.socket.send(JSON.stringify({
                type: 'ping',
                timestamp: new Date().toISOString()
            }));
        }
    }

    /**
     * Inicia el heartbeat
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            this.sendPing();
        }, this.config.heartbeatInterval);
    }

    /**
     * Detiene el heartbeat
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * Maneja la reconexión automática
     */
    handleReconnection() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

            console.log(`🔄 Reintentando conexión en ${delay}ms (intento ${this.reconnectAttempts})`);

            setTimeout(() => {
                this.connectWebSocket();
            }, delay);
        } else {
            console.error('❌ Máximo número de reintentos alcanzado');
            this.showToast('No se pudo reconectar al servidor', 'error');
        }
    }

    /**
     * Solicita permisos de notificación
     */
    async requestNotificationPermission() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            console.log('🔔 Permiso de notificaciones:', permission);

            if (permission === 'granted') {
                this.subscribeToPushNotifications();
            }
        }
    }

    /**
     * Registra el Service Worker
     */
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('✅ Service Worker registrado:', registration);

                // Configurar push notifications
                this.setupPushNotifications(registration);

            } catch (error) {
                console.error('❌ Error registrando Service Worker:', error);
            }
        }
    }

    /**
     * Configura push notifications
     */
    async setupPushNotifications(registration) {
        try {
            // Verificar soporte
            if (!('PushManager' in window)) {
                console.warn('⚠️ Push notifications no soportadas');
                return;
            }

            // Obtener suscripción existente
            const existingSubscription = await registration.pushManager.getSubscription();

            if (existingSubscription) {
                console.log('✅ Suscripción push existente encontrada');
                this.sendSubscriptionToServer(existingSubscription);
            }

        } catch (error) {
            console.error('❌ Error configurando push notifications:', error);
        }
    }

    /**
     * Se suscribe a push notifications
     */
    async subscribeToPushNotifications() {
        try {
            const registration = await navigator.serviceWorker.ready;

            // Configuración de la suscripción
            const subscribeOptions = {
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array(
                    'ejemplo_public_key_vapid' // TODO: Obtener del servidor
                )
            };

            const subscription = await registration.pushManager.subscribe(subscribeOptions);
            console.log('✅ Suscrito a push notifications:', subscription);

            // Enviar suscripción al servidor
            this.sendSubscriptionToServer(subscription);

        } catch (error) {
            console.error('❌ Error suscribiéndose a push notifications:', error);
        }
    }

    /**
     * Envía la suscripción al servidor
     */
    async sendSubscriptionToServer(subscription) {
        try {
            // Obtener token del sistema de auth
            const tokenData = localStorage.getItem('geovigia_token');
            if (!tokenData) {
                console.log('📊 No hay token de autenticación, omitiendo envío de suscripción');
                return;
            }

            const token = JSON.parse(tokenData).token;
            const response = await fetch(`${this.config.apiUrl}push/subscribe/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${token}`
                },
                body: JSON.stringify({
                    endpoint: subscription.endpoint,
                    p256dh: btoa(String.fromCharCode.apply(null, new Uint8Array(subscription.getKey('p256dh')))),
                    auth: btoa(String.fromCharCode.apply(null, new Uint8Array(subscription.getKey('auth'))))
                })
            });

            if (response.ok) {
                console.log('✅ Suscripción enviada al servidor');
            } else {
                console.log('📊 Backend no disponible para suscripción push');
            }

        } catch (error) {
            console.log('📊 Error enviando suscripción (modo demo):', error.message);
        }
    }

    /**
     * Convierte base64 a Uint8Array
     */
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }

        return outputArray;
    }

    /**
     * Carga notificaciones existentes
     */
    async loadExistingNotifications() {
        try {
            // Obtener token del sistema de auth
            const tokenData = localStorage.getItem('geovigia_token');
            if (!tokenData) {
                console.log('📊 No hay token, usando notificaciones simuladas');
                this.loadSimulatedNotifications();
                return;
            }

            const token = JSON.parse(tokenData).token;
            const response = await fetch(`${this.config.apiUrl}?page_size=20`, {
                headers: {
                    'Authorization': `Token ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.notifications = data.results || [];
                this.updateNotificationsList();
                this.loadUnreadCount();
            } else {
                console.log('📊 Backend no disponible, usando notificaciones simuladas');
                this.loadSimulatedNotifications();
            }

        } catch (error) {
            console.log('📊 Error cargando notificaciones, usando simuladas:', error.message);
            this.loadSimulatedNotifications();
        }
    }

    /**
     * Carga notificaciones simuladas para demo
     */
    loadSimulatedNotifications() {
        this.notifications = [
            {
                id: 1,
                title: 'Sistema Iniciado',
                message: 'GeoVigia ha sido iniciado correctamente',
                type: 'info',
                is_read: false,
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                title: 'Guardia en Ruta',
                message: 'Miguel Seguridad ha iniciado su patrullaje',
                type: 'route_update',
                is_read: true,
                created_at: new Date(Date.now() - 300000).toISOString()
            },
            {
                id: 3,
                title: 'Propiedad Verificada',
                message: 'Casa Central - Verificación completada sin incidentes',
                type: 'property_alert',
                is_read: false,
                created_at: new Date(Date.now() - 600000).toISOString()
            }
        ];

        this.unreadCount = this.notifications.filter(n => !n.is_read).length;
        this.updateNotificationsList();
        this.updateUnreadCount();
        console.log('✅ Notificaciones simuladas cargadas');
    }

    /**
     * Carga el contador de no leídas
     */
    async loadUnreadCount() {
        try {
            const response = await fetch(`${this.config.apiUrl}unread-count/`, {
                headers: {
                    'Authorization': `Token ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.unreadCount = data.unread_count;
                this.updateUnreadCount();
            }

        } catch (error) {
            console.error('❌ Error cargando contador de no leídas:', error);
        }
    }

    /**
     * Marca una notificación como leída
     */
    async markAsRead(notificationId) {
        try {
            const response = await fetch(`${this.config.apiUrl}${notificationId}/read/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Token ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                // Actualizar localmente
                const notification = this.notifications.find(n => n.id === notificationId);
                if (notification && !notification.is_read) {
                    notification.is_read = true;
                    this.unreadCount--;
                    this.updateUnreadCount();
                    this.updateNotificationsList();
                }

                // Enviar confirmación via WebSocket
                if (this.isConnected && this.socket) {
                    this.socket.send(JSON.stringify({
                        type: 'mark_read',
                        notification_id: notificationId
                    }));
                }
            }

        } catch (error) {
            console.error('❌ Error marcando notificación como leída:', error);
        }
    }

    /**
     * Marca todas las notificaciones como leídas
     */
    async markAllAsRead() {
        try {
            const response = await fetch(`${this.config.apiUrl}mark-all-read/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Token ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                // Actualizar localmente
                this.notifications.forEach(notification => {
                    notification.is_read = true;
                });
                this.unreadCount = 0;
                this.updateUnreadCount();
                this.updateNotificationsList();

                this.showToast('Todas las notificaciones marcadas como leídas', 'success');
            }

        } catch (error) {
            console.error('❌ Error marcando todas como leídas:', error);
        }
    }

    /**
     * Envía una alerta de emergencia
     */
    async sendEmergencyAlert(alertType, message, location = null) {
        try {
            const response = await fetch(`${this.config.apiUrl}alert/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${localStorage.getItem('authToken')}`
                },
                body: JSON.stringify({
                    alert_type: alertType,
                    message: message,
                    location: location
                })
            });

            if (response.ok) {
                this.showToast('Alerta de emergencia enviada', 'success');
                return true;
            } else {
                this.showToast('Error enviando alerta', 'error');
                return false;
            }

        } catch (error) {
            console.error('❌ Error enviando alerta:', error);
            this.showToast('Error enviando alerta', 'error');
            return false;
        }
    }

    /**
     * Actualiza la ubicación del usuario (para guardias)
     */
    sendLocationUpdate(latitude, longitude, accuracy = null, heading = null, speed = null) {
        if (this.isConnected && this.socket) {
            this.socket.send(JSON.stringify({
                type: 'location_update',
                latitude: latitude,
                longitude: longitude,
                accuracy: accuracy,
                heading: heading,
                speed: speed,
                timestamp: new Date().toISOString()
            }));
        }
    }

    /**
     * Inicializa la UI de notificaciones
     */
    initializeUI() {
        // Crear contenedor de notificaciones si no existe
        if (!document.getElementById('notifications-container')) {
            const container = document.createElement('div');
            container.id = 'notifications-container';
            container.className = 'notifications-container';
            document.body.appendChild(container);
        }

        // Crear badge de contador si no existe
        this.createNotificationBadge();

        // Agregar estilos CSS
        this.addNotificationStyles();
    }

    /**
     * Crea el badge de contador de notificaciones
     */
    createNotificationBadge() {
        const existingBadge = document.getElementById('notification-badge');
        if (existingBadge) return;

        // Buscar el ícono de notificaciones en la navegación
        const notificationIcon = document.querySelector('[data-section="notifications"]') ||
                                document.querySelector('.notification-icon');

        if (notificationIcon) {
            const badge = document.createElement('span');
            badge.id = 'notification-badge';
            badge.className = 'notification-badge';
            badge.style.display = 'none';
            notificationIcon.style.position = 'relative';
            notificationIcon.appendChild(badge);
        }
    }

    /**
     * Actualiza el contador de no leídas
     */
    updateUnreadCount() {
        const badge = document.getElementById('notification-badge');
        if (badge) {
            if (this.unreadCount > 0) {
                badge.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }

        // Actualizar título de la página
        const originalTitle = document.title.replace(/^\(\d+\) /, '');
        if (this.unreadCount > 0) {
            document.title = `(${this.unreadCount}) ${originalTitle}`;
        } else {
            document.title = originalTitle;
        }
    }

    /**
     * Actualiza la lista de notificaciones en la UI
     */
    updateNotificationsList() {
        const container = document.getElementById('notifications-list');
        if (!container) return;

        container.innerHTML = '';

        if (this.notifications.length === 0) {
            container.innerHTML = '<div class="no-notifications">No hay notificaciones</div>';
            return;
        }

        this.notifications.forEach(notification => {
            const element = this.createNotificationElement(notification);
            container.appendChild(element);
        });
    }

    /**
     * Crea un elemento de notificación
     */
    createNotificationElement(notification) {
        const element = document.createElement('div');
        element.className = `notification-item ${notification.is_read ? 'read' : 'unread'} ${notification.priority}`;
        element.dataset.notificationId = notification.id;

        const timeAgo = this.getTimeAgo(new Date(notification.created_at));

        element.innerHTML = `
            <div class="notification-icon">
                <i class="fas ${this.getNotificationIcon(notification.type)}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-header">
                    <h4>${notification.title}</h4>
                    <span class="notification-time">${timeAgo}</span>
                </div>
                <p class="notification-message">${notification.message}</p>
                ${notification.sender_username ? `<small class="notification-sender">De: ${notification.sender_username}</small>` : ''}
            </div>
            <div class="notification-actions">
                ${!notification.is_read ? '<button class="btn-mark-read" title="Marcar como leída"><i class="fas fa-check"></i></button>' : ''}
                <button class="btn-delete" title="Eliminar"><i class="fas fa-trash"></i></button>
            </div>
        `;

        // Agregar event listeners
        const markReadBtn = element.querySelector('.btn-mark-read');
        if (markReadBtn) {
            markReadBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.markAsRead(notification.id);
            });
        }

        const deleteBtn = element.querySelector('.btn-delete');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteNotification(notification.id);
            });
        }

        // Click en la notificación para marcar como leída
        element.addEventListener('click', () => {
            if (!notification.is_read) {
                this.markAsRead(notification.id);
            }
        });

        return element;
    }

    /**
     * Obtiene el ícono para un tipo de notificación
     */
    getNotificationIcon(type) {
        const icons = {
            'alert': 'fa-exclamation-triangle',
            'info': 'fa-info-circle',
            'warning': 'fa-exclamation-circle',
            'success': 'fa-check-circle',
            'emergency': 'fa-exclamation-triangle',
            'route_update': 'fa-route',
            'property_alert': 'fa-home',
            'guard_status': 'fa-user-shield'
        };

        return icons[type] || 'fa-bell';
    }

    /**
     * Calcula el tiempo transcurrido
     */
    getTimeAgo(date) {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return 'Ahora';
        if (minutes < 60) return `${minutes}m`;
        if (hours < 24) return `${hours}h`;
        return `${days}d`;
    }

    /**
     * Muestra una notificación visual
     */
    showNotification(notification) {
        // Notificación del navegador
        if (Notification.permission === 'granted') {
            new Notification(notification.title, {
                body: notification.message,
                icon: '/static/icons/geovigia-icon-192.png',
                tag: notification.id,
                requireInteraction: notification.priority === 'critical'
            });
        }

        // Toast notification
        this.showToast(notification.title, notification.type, notification.message);
    }

    /**
     * Muestra una alerta prominente
     */
    showAlert(alert) {
        // Crear modal de alerta
        const modal = document.createElement('div');
        modal.className = 'alert-modal emergency';
        modal.innerHTML = `
            <div class="alert-content">
                <div class="alert-header">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h2>🚨 ${alert.title}</h2>
                </div>
                <div class="alert-body">
                    <p>${alert.message}</p>
                    <small>De: ${alert.sender} - ${new Date(alert.timestamp).toLocaleString()}</small>
                </div>
                <div class="alert-actions">
                    <button class="btn btn-primary" onclick="this.closest('.alert-modal').remove()">
                        Entendido
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Auto-remover después de 10 segundos
        setTimeout(() => {
            if (modal.parentNode) {
                modal.remove();
            }
        }, 10000);
    }

    /**
     * Muestra un toast
     */
    showToast(title, type = 'info', message = '') {
        const container = document.getElementById('notifications-container');
        if (!container) return;

        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-icon">
                <i class="fas ${this.getNotificationIcon(type)}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                ${message ? `<div class="toast-message">${message}</div>` : ''}
            </div>
            <button class="toast-close" onclick="this.parentNode.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        container.appendChild(toast);

        // Auto-remover
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, this.config.notificationDuration);
    }

    /**
     * Reproduce sonido de notificación
     */
    playNotificationSound(notification) {
        // Solo reproducir para notificaciones importantes
        if (notification.priority === 'high' || notification.priority === 'critical') {
            this.playSound('/static/sounds/notification.mp3');
        }
    }

    /**
     * Reproduce sonido de alerta
     */
    playAlertSound() {
        this.playSound('/static/sounds/alert.mp3');
    }

    /**
     * Reproduce un sonido
     */
    playSound(src) {
        try {
            const audio = new Audio(src);
            audio.volume = 0.5;
            audio.play().catch(e => {
                console.log('No se pudo reproducir sonido:', e);
            });
        } catch (error) {
            console.log('Error reproduciendo sonido:', error);
        }
    }

    /**
     * Agrega estilos CSS para notificaciones
     */
    addNotificationStyles() {
        if (document.getElementById('notification-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notifications-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
            }

            .notification-badge {
                position: absolute;
                top: -8px;
                right: -8px;
                background: #ff4757;
                color: white;
                border-radius: 50%;
                padding: 2px 6px;
                font-size: 0.75rem;
                font-weight: bold;
                min-width: 18px;
                text-align: center;
            }

            .toast {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                margin-bottom: 10px;
                padding: 16px;
                display: flex;
                align-items: flex-start;
                gap: 12px;
                animation: slideIn 0.3s ease;
            }

            .toast-info { border-left: 4px solid #3498db; }
            .toast-success { border-left: 4px solid #2ecc71; }
            .toast-warning { border-left: 4px solid #f39c12; }
            .toast-error { border-left: 4px solid #e74c3c; }
            .toast-emergency { border-left: 4px solid #ff4757; }

            .toast-icon {
                color: #666;
                font-size: 1.2rem;
            }

            .toast-content {
                flex: 1;
            }

            .toast-title {
                font-weight: 600;
                margin-bottom: 4px;
            }

            .toast-message {
                font-size: 0.9rem;
                color: #666;
            }

            .toast-close {
                background: none;
                border: none;
                color: #999;
                cursor: pointer;
                padding: 0;
                font-size: 1rem;
            }

            .alert-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10001;
                animation: fadeIn 0.3s ease;
            }

            .alert-content {
                background: white;
                border-radius: 12px;
                padding: 2rem;
                max-width: 500px;
                text-align: center;
            }

            .alert-header {
                color: #e74c3c;
                margin-bottom: 1rem;
            }

            .alert-header i {
                font-size: 3rem;
                margin-bottom: 0.5rem;
            }

            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * Destructor - limpia recursos
     */
    destroy() {
        if (this.socket) {
            this.socket.close();
        }

        this.stopHeartbeat();

        const container = document.getElementById('notifications-container');
        if (container) {
            container.remove();
        }

        const styles = document.getElementById('notification-styles');
        if (styles) {
            styles.remove();
        }
    }
}

// Instancia global
window.geoVigiaNotifications = null;

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Solo inicializar si el usuario está autenticado
    if (localStorage.getItem('authToken')) {
        window.geoVigiaNotifications = new GeoVigiaNotifications();
    }
});

// Exportar para uso en módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GeoVigiaNotifications;
}

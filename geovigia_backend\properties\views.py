from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q
from django.shortcuts import get_object_or_404

from .models import Property, PropertyOwnership, PropertyPerimeter
from .serializers import (
    PropertySerializer, PropertyDetailSerializer, PropertyCreateSerializer,
    PropertyOwnershipSerializer, PropertyPerimeterSerializer,
    ClientPropertyListSerializer, PropertyLocationSerializer
)
from users.models import CustomUser


class IsOwnerOrOperador(permissions.BasePermission):
    """
    Permiso personalizado que permite a los propietarios ver sus propiedades
    o a los operadores gestionar cualquier propiedad
    """
    def has_object_permission(self, request, view, obj):
        # Los operadores pueden gestionar cualquier propiedad
        if request.user.is_operador:
            return True

        # Los clientes solo pueden ver sus propias propiedades
        if request.user.is_cliente:
            if isinstance(obj, Property):
                return obj.ownerships.filter(client=request.user).exists()
            elif isinstance(obj, PropertyOwnership):
                return obj.client == request.user
            elif isinstance(obj, PropertyPerimeter):
                return obj.property.ownerships.filter(client=request.user).exists()

        # Los guardias pueden ver propiedades en sus rutas (implementar más tarde)
        return False


class PropertyListCreateView(generics.ListCreateAPIView):
    """
    Vista para listar y crear propiedades
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return PropertyCreateSerializer
        return PropertySerializer

    def get_queryset(self):
        user = self.request.user

        if user.is_operador:
            # Los operadores pueden ver todas las propiedades
            return Property.objects.all()
        elif user.is_cliente:
            # Los clientes solo ven sus propiedades
            return Property.objects.filter(
                ownerships__client=user
            ).distinct()
        elif user.is_guardia:
            # Los guardias ven propiedades en sus rutas (implementar más tarde)
            return Property.objects.filter(is_monitored=True)

        return Property.objects.none()


class PropertyDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Vista para ver, actualizar o eliminar una propiedad específica
    """
    serializer_class = PropertyDetailSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrOperador]
    queryset = Property.objects.all()


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def my_properties(request):
    """
    Vista para que los clientes obtengan sus propiedades
    """
    if not request.user.is_cliente:
        return Response({
            'error': 'Solo los clientes pueden acceder a esta función'
        }, status=status.HTTP_403_FORBIDDEN)

    properties = Property.objects.filter(
        ownerships__client=request.user
    ).distinct()

    serializer = ClientPropertyListSerializer(properties, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def properties_locations(request):
    """
    Vista para obtener ubicaciones de propiedades (para mapas)
    """
    user = request.user

    if user.is_operador:
        properties = Property.objects.filter(is_monitored=True)
    elif user.is_cliente:
        properties = Property.objects.filter(
            ownerships__client=user,
            is_monitored=True
        ).distinct()
    elif user.is_guardia:
        # Los guardias ven propiedades en sus rutas
        properties = Property.objects.filter(is_monitored=True)
    else:
        return Response({
            'error': 'No autorizado'
        }, status=status.HTTP_403_FORBIDDEN)

    serializer = PropertyLocationSerializer(properties, many=True)
    return Response(serializer.data)


class PropertyOwnershipListCreateView(generics.ListCreateAPIView):
    """
    Vista para listar y crear asignaciones de propiedades
    """
    serializer_class = PropertyOwnershipSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if user.is_operador:
            return PropertyOwnership.objects.all()
        elif user.is_cliente:
            return PropertyOwnership.objects.filter(client=user)

        return PropertyOwnership.objects.none()

    def perform_create(self, serializer):
        # Solo los operadores pueden crear asignaciones
        if not self.request.user.is_operador:
            raise permissions.PermissionDenied(
                "Solo los operadores pueden crear asignaciones de propiedades"
            )
        serializer.save()


class PropertyOwnershipDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Vista para gestionar asignaciones específicas de propiedades
    """
    serializer_class = PropertyOwnershipSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrOperador]
    queryset = PropertyOwnership.objects.all()


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def assign_property_to_client(request, property_id):
    """
    Vista para asignar una propiedad a un cliente (solo operadores)
    """
    if not request.user.is_operador:
        return Response({
            'error': 'Solo los operadores pueden asignar propiedades'
        }, status=status.HTTP_403_FORBIDDEN)

    property_obj = get_object_or_404(Property, id=property_id)
    client_id = request.data.get('client_id')
    ownership_type = request.data.get('ownership_type', 'propietario')
    is_primary = request.data.get('is_primary', False)

    if not client_id:
        return Response({
            'error': 'Se requiere client_id'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        client = CustomUser.objects.get(id=client_id, user_type='cliente')
    except CustomUser.DoesNotExist:
        return Response({
            'error': 'Cliente no encontrado'
        }, status=status.HTTP_404_NOT_FOUND)

    # Verificar si ya existe la asignación
    if PropertyOwnership.objects.filter(client=client, property=property_obj).exists():
        return Response({
            'error': 'El cliente ya está asignado a esta propiedad'
        }, status=status.HTTP_400_BAD_REQUEST)

    ownership = PropertyOwnership.objects.create(
        client=client,
        property=property_obj,
        ownership_type=ownership_type,
        is_primary=is_primary
    )

    serializer = PropertyOwnershipSerializer(ownership)
    return Response({
        'message': 'Propiedad asignada exitosamente',
        'assignment': serializer.data
    }, status=status.HTTP_201_CREATED)


class PropertyPerimeterListCreateView(generics.ListCreateAPIView):
    """
    Vista para listar y crear perímetros de propiedades
    """
    serializer_class = PropertyPerimeterSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if user.is_operador:
            return PropertyPerimeter.objects.all()
        elif user.is_cliente:
            return PropertyPerimeter.objects.filter(
                property__ownerships__client=user
            ).distinct()
        elif user.is_guardia:
            return PropertyPerimeter.objects.filter(
                property__is_monitored=True,
                is_active=True
            )

        return PropertyPerimeter.objects.none()

    def perform_create(self, serializer):
        # Verificar permisos para crear perímetros
        property_obj = serializer.validated_data['property']
        user = self.request.user

        if user.is_operador:
            # Los operadores pueden crear perímetros para cualquier propiedad
            pass
        elif user.is_cliente:
            # Los clientes solo pueden crear perímetros para sus propiedades
            if not property_obj.ownerships.filter(client=user).exists():
                raise permissions.PermissionDenied(
                    "No tienes permisos para crear perímetros en esta propiedad"
                )
        else:
            raise permissions.PermissionDenied(
                "No tienes permisos para crear perímetros"
            )

        serializer.save()


class PropertyPerimeterDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Vista para gestionar perímetros específicos de propiedades
    """
    serializer_class = PropertyPerimeterSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrOperador]
    queryset = PropertyPerimeter.objects.all()


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def property_perimeter(request, property_id):
    """
    Vista para obtener el perímetro de una propiedad específica
    """
    property_obj = get_object_or_404(Property, id=property_id)

    # Verificar permisos
    user = request.user
    if not user.is_operador:
        if user.is_cliente:
            if not property_obj.ownerships.filter(client=user).exists():
                return Response({
                    'error': 'No tienes permisos para ver esta propiedad'
                }, status=status.HTTP_403_FORBIDDEN)
        elif user.is_guardia:
            if not property_obj.is_monitored:
                return Response({
                    'error': 'Esta propiedad no está bajo monitoreo'
                }, status=status.HTTP_403_FORBIDDEN)
        else:
            return Response({
                'error': 'No autorizado'
            }, status=status.HTTP_403_FORBIDDEN)

    try:
        perimeter = property_obj.perimeter
        serializer = PropertyPerimeterSerializer(perimeter)
        return Response(serializer.data)
    except PropertyPerimeter.DoesNotExist:
        return Response({
            'message': 'Esta propiedad no tiene perímetro definido'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_property_perimeter(request, property_id):
    """
    Vista para crear un perímetro para una propiedad específica
    """
    property_obj = get_object_or_404(Property, id=property_id)

    # Verificar si ya existe un perímetro
    if hasattr(property_obj, 'perimeter'):
        return Response({
            'error': 'Esta propiedad ya tiene un perímetro definido'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Verificar permisos
    user = request.user
    if not user.is_operador:
        if user.is_cliente:
            if not property_obj.ownerships.filter(client=user).exists():
                return Response({
                    'error': 'No tienes permisos para crear perímetros en esta propiedad'
                }, status=status.HTTP_403_FORBIDDEN)
        else:
            return Response({
                'error': 'No tienes permisos para crear perímetros'
            }, status=status.HTTP_403_FORBIDDEN)

    # Crear el perímetro
    data = request.data.copy()
    data['property'] = property_obj.id

    serializer = PropertyPerimeterSerializer(data=data)
    if serializer.is_valid():
        serializer.save()
        return Response({
            'message': 'Perímetro creado exitosamente',
            'perimeter': serializer.data
        }, status=status.HTTP_201_CREATED)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def properties_near_location(request):
    """
    Vista para obtener propiedades cerca de una ubicación específica
    """
    latitude = request.GET.get('latitude')
    longitude = request.GET.get('longitude')
    radius_km = float(request.GET.get('radius_km', 5))  # Radio por defecto 5km

    if not latitude or not longitude:
        return Response({
            'error': 'Se requieren parámetros latitude y longitude'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        lat = float(latitude)
        lng = float(longitude)
    except ValueError:
        return Response({
            'error': 'Coordenadas inválidas'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Cálculo aproximado de propiedades cercanas
    # (Para una implementación más precisa, usar PostGIS o similar)
    lat_range = radius_km / 111.0  # Aproximadamente 111 km por grado de latitud
    lng_range = radius_km / (111.0 * abs(lat))  # Ajustado por latitud

    user = request.user
    base_queryset = Property.objects.filter(
        latitude__range=(lat - lat_range, lat + lat_range),
        longitude__range=(lng - lng_range, lng + lng_range),
        is_monitored=True
    )

    if user.is_operador:
        properties = base_queryset
    elif user.is_cliente:
        properties = base_queryset.filter(ownerships__client=user).distinct()
    elif user.is_guardia:
        properties = base_queryset
    else:
        return Response({
            'error': 'No autorizado'
        }, status=status.HTTP_403_FORBIDDEN)

    serializer = PropertyLocationSerializer(properties, many=True)
    return Response({
        'center': {'latitude': lat, 'longitude': lng},
        'radius_km': radius_km,
        'properties': serializer.data
    })

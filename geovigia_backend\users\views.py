from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from django.contrib.auth import login, logout
from django.utils import timezone

from .models import CustomUser, ClienteProfile, GuardiaProfile, OperadorProfile
from .serializers import (
    CustomUserSerializer, LoginSerializer, UserRegistrationSerializer,
    UserProfileSerializer, GuardiaLocationUpdateSerializer,
    ClienteProfileSerializer, GuardiaProfileSerializer, OperadorProfileSerializer
)


class IsOwnerOrOperador(permissions.BasePermission):
    """
    Permiso personalizado que permite a los usuarios ver/editar solo su propia información
    o a los operadores gestionar cualquier usuario
    """
    def has_object_permission(self, request, view, obj):
        # Los operadores pueden gestionar cualquier usuario
        if request.user.is_operador:
            return True

        # Los usuarios solo pueden ver/editar su propia información
        if hasattr(obj, 'user'):
            return obj.user == request.user
        return obj == request.user


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def login_view(request):
    """
    Vista para el login de usuarios
    """
    serializer = LoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']

        # Crear o obtener token
        token, created = Token.objects.get_or_create(user=user)

        # Obtener perfil completo del usuario
        profile_serializer = UserProfileSerializer(user)

        return Response({
            'token': token.key,
            'user': profile_serializer.data,
            'message': 'Login exitoso'
        }, status=status.HTTP_200_OK)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout_view(request):
    """
    Vista para el logout de usuarios
    """
    try:
        # Eliminar el token del usuario
        request.user.auth_token.delete()
        return Response({
            'message': 'Logout exitoso'
        }, status=status.HTTP_200_OK)
    except:
        return Response({
            'error': 'Error al cerrar sesión'
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])  # Cambiar a IsOperador en producción
def register_user(request):
    """
    Vista para registrar nuevos usuarios
    Solo los operadores deberían poder registrar usuarios en producción
    """
    serializer = UserRegistrationSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.save()

        # Crear token para el nuevo usuario
        token, created = Token.objects.get_or_create(user=user)

        # Obtener perfil completo
        profile_serializer = UserProfileSerializer(user)

        return Response({
            'token': token.key,
            'user': profile_serializer.data,
            'message': 'Usuario registrado exitosamente'
        }, status=status.HTTP_201_CREATED)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_profile(request):
    """
    Vista para obtener el perfil del usuario autenticado
    """
    serializer = UserProfileSerializer(request.user)
    return Response(serializer.data)


@api_view(['PUT', 'PATCH'])
@permission_classes([permissions.IsAuthenticated])
def update_profile(request):
    """
    Vista para actualizar el perfil del usuario autenticado
    """
    serializer = CustomUserSerializer(
        request.user,
        data=request.data,
        partial=True
    )

    if serializer.is_valid():
        serializer.save()

        # Retornar perfil completo actualizado
        profile_serializer = UserProfileSerializer(request.user)
        return Response({
            'user': profile_serializer.data,
            'message': 'Perfil actualizado exitosamente'
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserListView(generics.ListAPIView):
    """
    Vista para listar usuarios (solo para operadores)
    """
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Solo los operadores pueden ver la lista de usuarios
        if self.request.user.is_operador:
            return CustomUser.objects.all()
        else:
            # Los demás usuarios solo ven su propio perfil
            return CustomUser.objects.filter(id=self.request.user.id)


class UserDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Vista para ver, actualizar o eliminar un usuario específico
    """
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrOperador]
    queryset = CustomUser.objects.all()


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def update_guardia_location(request):
    """
    Vista para que los guardias actualicen su ubicación
    """
    if not request.user.is_guardia:
        return Response({
            'error': 'Solo los guardias pueden actualizar su ubicación'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        guardia_profile = request.user.guardia_profile
    except GuardiaProfile.DoesNotExist:
        return Response({
            'error': 'Perfil de guardia no encontrado'
        }, status=status.HTTP_404_NOT_FOUND)

    serializer = GuardiaLocationUpdateSerializer(
        guardia_profile,
        data=request.data,
        partial=True
    )

    if serializer.is_valid():
        serializer.save()
        return Response({
            'message': 'Ubicación actualizada exitosamente',
            'location': {
                'latitude': guardia_profile.current_latitude,
                'longitude': guardia_profile.current_longitude,
                'last_update': guardia_profile.last_location_update
            }
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def toggle_duty_status(request):
    """
    Vista para que los guardias cambien su estado de servicio (en servicio/fuera de servicio)
    """
    if not request.user.is_guardia:
        return Response({
            'error': 'Solo los guardias pueden cambiar su estado de servicio'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        guardia_profile = request.user.guardia_profile
        guardia_profile.is_on_duty = not guardia_profile.is_on_duty
        guardia_profile.save()

        status_text = "en servicio" if guardia_profile.is_on_duty else "fuera de servicio"

        return Response({
            'message': f'Estado cambiado a: {status_text}',
            'is_on_duty': guardia_profile.is_on_duty
        })

    except GuardiaProfile.DoesNotExist:
        return Response({
            'error': 'Perfil de guardia no encontrado'
        }, status=status.HTTP_404_NOT_FOUND)

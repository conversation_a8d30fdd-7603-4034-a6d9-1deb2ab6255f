#!/usr/bin/env python3
"""
🚀 GeoVigia System Launcher
===========================

Ejecuta el sistema completo de GeoVigia (Backend Django + Frontend) con un solo comando.

Uso:
    python run_geovigia.py

Características:
- ✅ Backend Django completo con todas las APIs
- ✅ Frontend web con todas las funcionalidades
- ✅ Abre automáticamente el navegador
- ✅ Manejo de errores y logs detallados
- ✅ Detección automática de puertos disponibles
- ✅ Funciona en Windows, Mac y Linux

Autor: GeoVigia Team
Versión: 1.0
"""

import os
import sys
import subprocess
import threading
import time
import webbrowser
import signal
import socket
import http.server
import socketserver
from pathlib import Path

# Configuración
BACKEND_PORT = 5021
FRONTEND_PORT = 5022
REDIS_PORT = 5023
BACKEND_DIR = "geovigia_backend"
FRONTEND_DIR = "geovigia-frontend"

class ColoredOutput:
    """Colores para output en terminal"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_colored(message, color):
    """Imprimir mensaje con color"""
    print(f"{color}{message}{ColoredOutput.ENDC}")

def check_port_available(port):
    """Verificar si un puerto está disponible"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def check_requirements():
    """Verificar que todos los requisitos estén disponibles"""
    print_colored("🔍 Verificando requisitos del sistema...", ColoredOutput.OKCYAN)
    
    # Verificar Python
    if sys.version_info < (3, 8):
        print_colored("❌ Error: Se requiere Python 3.8 o superior", ColoredOutput.FAIL)
        return False
    
    # Verificar directorios
    if not os.path.exists(BACKEND_DIR):
        print_colored(f"❌ Error: Directorio {BACKEND_DIR} no encontrado", ColoredOutput.FAIL)
        return False
    
    if not os.path.exists(FRONTEND_DIR):
        print_colored(f"❌ Error: Directorio {FRONTEND_DIR} no encontrado", ColoredOutput.FAIL)
        return False
    
    # Verificar manage.py
    manage_py = os.path.join(BACKEND_DIR, "manage.py")
    if not os.path.exists(manage_py):
        print_colored(f"❌ Error: {manage_py} no encontrado", ColoredOutput.FAIL)
        return False
    
    # Verificar index.html
    index_html = os.path.join(FRONTEND_DIR, "index.html")
    if not os.path.exists(index_html):
        print_colored(f"❌ Error: {index_html} no encontrado", ColoredOutput.FAIL)
        return False
    
    # Verificar puertos
    if not check_port_available(BACKEND_PORT):
        print_colored(f"❌ Error: Puerto {BACKEND_PORT} ya está en uso", ColoredOutput.FAIL)
        print_colored("💡 Solución: Cierra cualquier servidor Django anterior", ColoredOutput.WARNING)
        return False

    if not check_port_available(FRONTEND_PORT):
        print_colored(f"❌ Error: Puerto {FRONTEND_PORT} ya está en uso", ColoredOutput.FAIL)
        print_colored("💡 Solución: Cierra cualquier servidor frontend anterior", ColoredOutput.WARNING)
        return False

    if not check_port_available(REDIS_PORT):
        print_colored(f"❌ Error: Puerto {REDIS_PORT} ya está en uso", ColoredOutput.FAIL)
        print_colored("💡 Solución: Cierra cualquier servidor Redis anterior", ColoredOutput.WARNING)
        return False
    
    print_colored("✅ Todos los requisitos verificados correctamente", ColoredOutput.OKGREEN)
    return True

def check_dependencies():
    """Verificar e instalar dependencias si es necesario"""
    print_colored("📦 Verificando dependencias...", ColoredOutput.OKCYAN)
    
    try:
        # Verificar Django
        import django
        print_colored(f"✅ Django {django.get_version()} encontrado", ColoredOutput.OKGREEN)
    except ImportError:
        print_colored("⚠️ Django no encontrado. Instalando dependencias...", ColoredOutput.WARNING)
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                         check=True, capture_output=True)
            print_colored("✅ Dependencias instaladas correctamente", ColoredOutput.OKGREEN)
        except subprocess.CalledProcessError as e:
            print_colored(f"❌ Error instalando dependencias: {e}", ColoredOutput.FAIL)
            return False
    
    return True

class FrontendHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Handler personalizado para el servidor frontend"""
    
    def do_GET(self):
        # Si la ruta es '/', servir index.html
        if self.path == '/':
            self.path = '/index.html'
        # Manejar favicon.ico
        elif self.path == '/favicon.ico':
            if not os.path.exists('favicon.ico'):
                self.send_response(204)  # No Content
                self.end_headers()
                return
        return super().do_GET()
    
    def end_headers(self):
        # Agregar headers para evitar caché y CORS
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def log_message(self, format, *args):
        # Personalizar logs del servidor frontend
        message = format % args
        if 'favicon.ico' not in message and 'GET /index.html' not in message:
            print_colored(f"🌐 Frontend: {message}", ColoredOutput.OKBLUE)

def check_redis_available():
    """Verificar si Redis está disponible en el sistema"""
    try:
        # Intentar ejecutar redis-server --version
        result = subprocess.run(['redis-server', '--version'],
                              capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        return False

def run_redis():
    """Ejecutar servidor Redis"""
    print_colored(f"🔴 Iniciando servidor Redis en puerto {REDIS_PORT}...", ColoredOutput.OKCYAN)

    try:
        # Verificar si Redis está disponible
        if not check_redis_available():
            print_colored("⚠️ Redis no está instalado. Intentando usar Redis embebido...", ColoredOutput.WARNING)
            print_colored("💡 Para funcionalidad completa, instala Redis: https://redis.io/download", ColoredOutput.WARNING)
            return

        # Ejecutar Redis con configuración personalizada
        redis_cmd = [
            'redis-server',
            '--port', str(REDIS_PORT),
            '--bind', '127.0.0.1',
            '--save', '',  # Deshabilitar persistencia para desarrollo
            '--appendonly', 'no',  # Deshabilitar AOF
            '--protected-mode', 'no',  # Permitir conexiones locales
            '--loglevel', 'notice'
        ]

        process = subprocess.Popen(redis_cmd,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.STDOUT,
                                 text=True, bufsize=1)

        # Leer output del proceso
        for line in iter(process.stdout.readline, ''):
            if line.strip():
                if "Ready to accept connections" in line:
                    print_colored("✅ Redis iniciado correctamente", ColoredOutput.OKGREEN)
                elif "Server initialized" in line:
                    print_colored("🔴 Redis: Servidor inicializado", ColoredOutput.OKBLUE)
                elif "Error" in line or "error" in line:
                    print_colored(f"❌ Redis Error: {line.strip()}", ColoredOutput.FAIL)
                else:
                    # Solo mostrar líneas importantes
                    if any(keyword in line.lower() for keyword in ['ready', 'listening', 'port', 'started']):
                        print_colored(f"🔴 Redis: {line.strip()}", ColoredOutput.OKBLUE)

        process.wait()

    except subprocess.CalledProcessError as e:
        print_colored(f"❌ Error ejecutando Redis: {e}", ColoredOutput.FAIL)
        print_colored("💡 El sistema funcionará sin notificaciones en tiempo real", ColoredOutput.WARNING)
    except KeyboardInterrupt:
        print_colored("\n🛑 Redis detenido por el usuario", ColoredOutput.WARNING)
    except Exception as e:
        print_colored(f"❌ Error inesperado en Redis: {e}", ColoredOutput.FAIL)
        print_colored("💡 El sistema funcionará sin notificaciones en tiempo real", ColoredOutput.WARNING)

def run_backend():
    """Ejecutar el servidor Django backend"""
    print_colored(f"🔧 Iniciando backend Django en puerto {BACKEND_PORT}...", ColoredOutput.OKCYAN)
    
    try:
        # Cambiar al directorio backend
        os.chdir(BACKEND_DIR)
        
        # Aplicar migraciones si es necesario
        print_colored("📊 Aplicando migraciones de base de datos...", ColoredOutput.OKCYAN)
        subprocess.run([sys.executable, "manage.py", "migrate"], check=True, capture_output=True)
        
        # Ejecutar servidor Django
        process = subprocess.Popen([
            sys.executable, "manage.py", "runserver", f"127.0.0.1:{BACKEND_PORT}"
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1)
        
        # Leer output del proceso
        for line in iter(process.stdout.readline, ''):
            if line.strip():
                if "Starting development server" in line:
                    print_colored("✅ Backend Django iniciado correctamente", ColoredOutput.OKGREEN)
                elif "Quit the server" in line:
                    print_colored("🔧 Backend: Presiona Ctrl+C para detener", ColoredOutput.OKCYAN)
                elif "Error" in line or "Exception" in line:
                    print_colored(f"❌ Backend Error: {line.strip()}", ColoredOutput.FAIL)
                else:
                    print_colored(f"🔧 Backend: {line.strip()}", ColoredOutput.OKBLUE)
        
        process.wait()
        
    except subprocess.CalledProcessError as e:
        print_colored(f"❌ Error ejecutando backend: {e}", ColoredOutput.FAIL)
    except KeyboardInterrupt:
        print_colored("\n🛑 Backend detenido por el usuario", ColoredOutput.WARNING)
    finally:
        # Volver al directorio raíz
        os.chdir("..")

def run_frontend():
    """Ejecutar el servidor frontend"""
    print_colored(f"🌐 Iniciando servidor frontend en puerto {FRONTEND_PORT}...", ColoredOutput.OKCYAN)
    
    try:
        # Cambiar al directorio frontend
        os.chdir(FRONTEND_DIR)
        
        # Crear servidor HTTP
        with socketserver.TCPServer(("", FRONTEND_PORT), FrontendHTTPRequestHandler) as httpd:
            print_colored(f"✅ Servidor frontend ejecutándose en http://localhost:{FRONTEND_PORT}", ColoredOutput.OKGREEN)
            
            # Ejecutar servidor
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 98 or e.errno == 10048:  # Puerto en uso
            print_colored(f"❌ Error: Puerto {FRONTEND_PORT} ya está en uso", ColoredOutput.FAIL)
        else:
            print_colored(f"❌ Error del servidor frontend: {e}", ColoredOutput.FAIL)
    except KeyboardInterrupt:
        print_colored("\n🛑 Servidor frontend detenido por el usuario", ColoredOutput.WARNING)
    finally:
        # Volver al directorio raíz
        os.chdir("..")

def open_browser():
    """Abrir navegador después de que los servicios estén listos"""
    print_colored("⏳ Esperando que los servicios estén listos...", ColoredOutput.OKCYAN)
    time.sleep(5)  # Esperar a que ambos servicios estén listos
    
    try:
        webbrowser.open(f'http://localhost:{FRONTEND_PORT}')
        print_colored("🌐 Navegador abierto automáticamente", ColoredOutput.OKGREEN)
    except Exception as e:
        print_colored(f"⚠️ No se pudo abrir el navegador automáticamente: {e}", ColoredOutput.WARNING)
        print_colored(f"📱 Abre manualmente: http://localhost:{FRONTEND_PORT}", ColoredOutput.OKCYAN)

def print_banner():
    """Mostrar banner de inicio"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                   🚀 GEOVIGIA SYSTEM 🚀                     ║
║                                                              ║
║           Sistema Completo de Vigilancia Inteligente        ║
║                                                              ║
║  ✅ Backend Django con APIs completas                       ║
║  ✅ Frontend web con todas las funcionalidades              ║
║  ✅ Sistema de autenticación robusto                        ║
║  ✅ Mapas interactivos y analytics avanzados                ║
║  ✅ Notificaciones en tiempo real                           ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""
    print_colored(banner, ColoredOutput.HEADER)

def print_instructions():
    """Mostrar instrucciones de uso"""
    instructions = f"""
🎯 SISTEMA INICIADO CORRECTAMENTE:

📡 SERVICIOS EJECUTÁNDOSE:
- 🔴 Redis Server: 127.0.0.1:{REDIS_PORT}
- 🔧 Backend Django: http://127.0.0.1:{BACKEND_PORT}
- 🌐 Frontend Web: http://localhost:{FRONTEND_PORT}
- 🔧 Admin Django: http://127.0.0.1:{BACKEND_PORT}/admin

🔑 CREDENCIALES DE ACCESO:
- 👨‍💼 Operador: operador / operador123
- 👮‍♂️ Guardia: guardia / guardia123  
- 👤 Cliente: cliente / cliente123
- 🔧 Admin: admin / admin123

🎯 FUNCIONALIDADES DISPONIBLES:
- ✅ Gestión completa de usuarios
- ✅ Administración de propiedades
- ✅ Sistema de rutas y patrullaje
- ✅ Mapas interactivos con Leaflet
- ✅ Notificaciones en tiempo real
- ✅ Analytics y reportes avanzados
- ✅ Dashboards especializados por rol

🔧 CONTROLES:
- Ctrl+C para detener ambos servicios
- F5 para recargar la aplicación
- F12 para abrir herramientas de desarrollador

📱 URL Principal: http://localhost:{FRONTEND_PORT}
"""
    print_colored(instructions, ColoredOutput.OKCYAN)

# Variables globales para los procesos
backend_thread = None
frontend_thread = None
redis_thread = None

def signal_handler(signum, frame):
    """Manejar señal de interrupción"""
    print_colored("\n🛑 Deteniendo sistema GeoVigia...", ColoredOutput.WARNING)
    print_colored("👋 ¡Gracias por usar GeoVigia!", ColoredOutput.OKGREEN)
    sys.exit(0)

def main():
    """Función principal"""
    global backend_thread, frontend_thread, redis_thread

    # Configurar manejo de señales
    signal.signal(signal.SIGINT, signal_handler)

    # Mostrar banner
    print_banner()

    # Verificar requisitos
    if not check_requirements():
        sys.exit(1)

    # Verificar dependencias
    if not check_dependencies():
        sys.exit(1)

    try:
        # Iniciar Redis en hilo separado (primero)
        redis_thread = threading.Thread(target=run_redis, daemon=True)
        redis_thread.start()

        # Esperar un poco para que Redis inicie
        time.sleep(3)

        # Iniciar backend en hilo separado
        backend_thread = threading.Thread(target=run_backend, daemon=True)
        backend_thread.start()

        # Esperar un poco para que el backend inicie
        time.sleep(2)

        # Iniciar frontend en hilo separado
        frontend_thread = threading.Thread(target=run_frontend, daemon=True)
        frontend_thread.start()

        # Abrir navegador en hilo separado
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()

        # Mostrar instrucciones
        time.sleep(3)
        print_instructions()

        # Mantener el programa principal ejecutándose
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print_colored("\n🛑 Deteniendo sistema...", ColoredOutput.WARNING)
    except Exception as e:
        print_colored(f"❌ Error inesperado: {e}", ColoredOutput.FAIL)
    finally:
        print_colored("👋 ¡Sistema GeoVigia detenido!", ColoredOutput.OKGREEN)
        sys.exit(0)

if __name__ == "__main__":
    main()

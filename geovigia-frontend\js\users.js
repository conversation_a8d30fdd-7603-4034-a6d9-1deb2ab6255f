/**
 * Módulo de Gestión de Usuarios para GeoVigia Frontend
 */

class UsersModule {
    constructor() {
        this.users = [];
        this.currentUser = null;
        this.table = null;
        this.filters = {
            search: '',
            userType: '',
            status: ''
        };
    }

    /**
     * Carga el módulo de usuarios
     */
    async load() {
        try {
            await this.loadUsers();
            this.render();
            this.setupEventListeners();
        } catch (error) {
            console.error('Error cargando usuarios:', error);
            GeoVigia.Components.Toast.show(
                'error',
                'Error',
                'Error al cargar los usuarios'
            );
        }
    }

    /**
     * Carga la lista de usuarios
     */
    async loadUsers() {
        try {
            const response = await GeoVigia.API.Users.getUsers();
            this.users = Array.isArray(response) ? response : (response.results || []);
        } catch (error) {
            console.error('Error cargando usuarios:', error);
            this.users = [];
        }
    }

    /**
     * Renderiza el módulo de usuarios
     */
    render() {
        const container = document.getElementById('users-page');
        if (!container) return;

        container.innerHTML = `
            <div class="users-header">
                <div class="users-actions">
                    <button class="btn btn-primary" id="add-user-btn">
                        <i class="fas fa-plus"></i>
                        Nuevo Usuario
                    </button>
                    <button class="btn btn-ghost" id="refresh-users-btn">
                        <i class="fas fa-sync-alt"></i>
                        Actualizar
                    </button>
                </div>
                
                <div class="users-filters">
                    <div class="filter-group">
                        <select id="user-type-filter">
                            <option value="">Todos los tipos</option>
                            <option value="operador">Operadores</option>
                            <option value="guardia">Guardias</option>
                            <option value="cliente">Clientes</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <select id="status-filter">
                            <option value="">Todos los estados</option>
                            <option value="activo">Activos</option>
                            <option value="inactivo">Inactivos</option>
                            <option value="suspendido">Suspendidos</option>
                        </select>
                    </div>
                </div>
            </div>

            <div id="users-table-container"></div>
        `;

        this.renderTable();
    }

    /**
     * Renderiza la tabla de usuarios
     */
    renderTable() {
        const container = document.getElementById('users-table-container');
        if (!container) return;

        const columns = [
            {
                key: 'username',
                title: 'Usuario',
                width: '150px'
            },
            {
                key: 'full_name',
                title: 'Nombre Completo',
                render: (value, row) => {
                    return row.first_name && row.last_name 
                        ? `${row.first_name} ${row.last_name}`
                        : row.username;
                }
            },
            {
                key: 'email',
                title: 'Email',
                width: '200px'
            },
            {
                key: 'user_type',
                title: 'Tipo',
                width: '120px',
                render: (value) => {
                    const types = {
                        'operador': 'Operador',
                        'guardia': 'Guardia',
                        'cliente': 'Cliente'
                    };
                    return `<span class="status-badge ${value}">${types[value] || value}</span>`;
                }
            },
            {
                key: 'is_active',
                title: 'Estado',
                width: '100px',
                render: (value) => {
                    return `<span class="status-badge ${value ? 'active' : 'inactive'}">
                        ${value ? 'Activo' : 'Inactivo'}
                    </span>`;
                }
            },
            {
                key: 'date_joined',
                title: 'Fecha de Registro',
                width: '150px',
                render: (value) => GeoVigia.Utils.Date.formatDate(value)
            },
            {
                key: 'actions',
                title: 'Acciones',
                width: '150px',
                render: (value, row) => `
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-ghost" onclick="GeoVigia.Users.editUser(${row.id})" title="Editar">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-ghost" onclick="GeoVigia.Users.viewUser(${row.id})" title="Ver">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-ghost" onclick="GeoVigia.Users.toggleUserStatus(${row.id})" title="${row.is_active ? 'Desactivar' : 'Activar'}">
                            <i class="fas fa-${row.is_active ? 'ban' : 'check'}"></i>
                        </button>
                    </div>
                `
            }
        ];

        this.table = new GeoVigia.Components.Table(container, {
            columns: columns,
            data: this.users,
            searchPlaceholder: 'Buscar usuarios...',
            emptyMessage: 'No se encontraron usuarios'
        });
    }

    /**
     * Configura los event listeners
     */
    setupEventListeners() {
        // Botón de nuevo usuario
        const addUserBtn = document.getElementById('add-user-btn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => this.showUserForm());
        }

        // Botón de actualizar
        const refreshBtn = document.getElementById('refresh-users-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refresh());
        }

        // Filtros
        const userTypeFilter = document.getElementById('user-type-filter');
        const statusFilter = document.getElementById('status-filter');

        if (userTypeFilter) {
            userTypeFilter.addEventListener('change', (e) => {
                this.filters.userType = e.target.value;
                this.applyFilters();
            });
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFilters();
            });
        }
    }

    /**
     * Aplica los filtros a la tabla
     */
    applyFilters() {
        let filteredUsers = [...this.users];

        // Filtrar por tipo de usuario
        if (this.filters.userType) {
            filteredUsers = filteredUsers.filter(user => user.user_type === this.filters.userType);
        }

        // Filtrar por estado
        if (this.filters.status) {
            const isActive = this.filters.status === 'activo';
            filteredUsers = filteredUsers.filter(user => user.is_active === isActive);
        }

        // Actualizar tabla
        if (this.table) {
            this.table.setData(filteredUsers);
        }
    }

    /**
     * Muestra el formulario de usuario
     */
    async showUserForm(userId = null) {
        const isEdit = !!userId;
        let userData = {};

        if (isEdit) {
            try {
                userData = await GeoVigia.API.Users.getUser(userId);
            } catch (error) {
                GeoVigia.Components.Toast.show('error', 'Error', 'Error al cargar los datos del usuario');
                return;
            }
        }

        const formContent = `
            <form id="user-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="username">Usuario *</label>
                        <input type="text" id="username" name="username" value="${userData.username || ''}" required ${isEdit ? 'readonly' : ''}>
                    </div>
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" value="${userData.email || ''}" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name">Nombre</label>
                        <input type="text" id="first_name" name="first_name" value="${userData.first_name || ''}">
                    </div>
                    <div class="form-group">
                        <label for="last_name">Apellido</label>
                        <input type="text" id="last_name" name="last_name" value="${userData.last_name || ''}">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="user_type">Tipo de Usuario *</label>
                        <select id="user_type" name="user_type" required>
                            <option value="">Seleccionar tipo</option>
                            <option value="operador" ${userData.user_type === 'operador' ? 'selected' : ''}>Operador</option>
                            <option value="guardia" ${userData.user_type === 'guardia' ? 'selected' : ''}>Guardia</option>
                            <option value="cliente" ${userData.user_type === 'cliente' ? 'selected' : ''}>Cliente</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="phone">Teléfono</label>
                        <input type="tel" id="phone" name="phone" value="${userData.phone || ''}">
                    </div>
                </div>

                ${!isEdit ? `
                    <div class="form-row">
                        <div class="form-group">
                            <label for="password">Contraseña *</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        <div class="form-group">
                            <label for="confirm_password">Confirmar Contraseña *</label>
                            <input type="password" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>
                ` : ''}

                <div class="form-group">
                    <label for="address">Dirección</label>
                    <textarea id="address" name="address" rows="3">${userData.address || ''}</textarea>
                </div>
            </form>
        `;

        const confirmed = await GeoVigia.Components.Modal.show(
            isEdit ? 'Editar Usuario' : 'Nuevo Usuario',
            formContent,
            {
                confirmText: isEdit ? 'Actualizar' : 'Crear',
                cancelText: 'Cancelar'
            }
        );

        if (confirmed) {
            await this.saveUser(userId);
        }
    }

    /**
     * Guarda un usuario
     */
    async saveUser(userId = null) {
        const form = document.getElementById('user-form');
        if (!form) return;

        const formData = GeoVigia.Utils.DOM.getFormData(form);
        const isEdit = !!userId;

        // Validaciones
        if (!isEdit && formData.password !== formData.confirm_password) {
            GeoVigia.Components.Toast.show('error', 'Error', 'Las contraseñas no coinciden');
            return;
        }

        try {
            if (isEdit) {
                await GeoVigia.API.Users.updateUser(userId, formData);
                GeoVigia.Components.Toast.show('success', 'Actualizado', 'Usuario actualizado correctamente');
            } else {
                await GeoVigia.API.Users.createUser(formData);
                GeoVigia.Components.Toast.show('success', 'Creado', 'Usuario creado correctamente');
            }

            await this.refresh();
        } catch (error) {
            console.error('Error guardando usuario:', error);
            GeoVigia.Components.Toast.show('error', 'Error', 'Error al guardar el usuario');
        }
    }

    /**
     * Ve los detalles de un usuario
     */
    async viewUser(userId) {
        try {
            const user = await GeoVigia.API.Users.getUser(userId);
            
            const content = `
                <div class="user-details">
                    <div class="detail-row">
                        <strong>Usuario:</strong> ${user.username}
                    </div>
                    <div class="detail-row">
                        <strong>Nombre:</strong> ${user.first_name} ${user.last_name}
                    </div>
                    <div class="detail-row">
                        <strong>Email:</strong> ${user.email}
                    </div>
                    <div class="detail-row">
                        <strong>Tipo:</strong> ${user.user_type}
                    </div>
                    <div class="detail-row">
                        <strong>Estado:</strong> ${user.is_active ? 'Activo' : 'Inactivo'}
                    </div>
                    <div class="detail-row">
                        <strong>Fecha de registro:</strong> ${GeoVigia.Utils.Date.formatDateTime(user.date_joined)}
                    </div>
                    ${user.phone ? `<div class="detail-row"><strong>Teléfono:</strong> ${user.phone}</div>` : ''}
                    ${user.address ? `<div class="detail-row"><strong>Dirección:</strong> ${user.address}</div>` : ''}
                </div>
            `;

            await GeoVigia.Components.Modal.show('Detalles del Usuario', content, {
                showCancel: false,
                confirmText: 'Cerrar'
            });
        } catch (error) {
            GeoVigia.Components.Toast.show('error', 'Error', 'Error al cargar los detalles del usuario');
        }
    }

    /**
     * Edita un usuario
     */
    async editUser(userId) {
        await this.showUserForm(userId);
    }

    /**
     * Alterna el estado de un usuario
     */
    async toggleUserStatus(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        const action = user.is_active ? 'desactivar' : 'activar';
        const confirmed = await GeoVigia.Components.Modal.confirm(
            'Confirmar Acción',
            `¿Está seguro de que desea ${action} este usuario?`
        );

        if (!confirmed) return;

        try {
            if (user.is_active) {
                await GeoVigia.API.Users.deactivateUser(userId);
            } else {
                await GeoVigia.API.Users.activateUser(userId);
            }

            GeoVigia.Components.Toast.show('success', 'Actualizado', `Usuario ${action}do correctamente`);
            await this.refresh();
        } catch (error) {
            GeoVigia.Components.Toast.show('error', 'Error', `Error al ${action} el usuario`);
        }
    }

    /**
     * Refresca la lista de usuarios
     */
    async refresh() {
        await this.loadUsers();
        this.applyFilters();
    }
}

// Crear instancia global del módulo de usuarios
window.GeoVigia.Users = new UsersModule();

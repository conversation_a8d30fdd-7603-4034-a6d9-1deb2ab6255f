/**
 * Advanced Charts Module - Gráficos Avanzados para GeoVigia
 * Maneja visualizaciones avanzadas, tendencias y análisis predictivo
 */

class AdvancedCharts {
    constructor() {
        this.apiBaseUrl = 'http://127.0.0.1:8000/api';
        this.charts = new Map();
        this.currentFilters = {
            period: '7d',
            zone: 'all',
            metric: 'incidents'
        };
    }

    /**
     * Inicializar el módulo de gráficos avanzados
     */
    init() {
        console.log('📊 Inicializando Advanced Charts...');
        this.createAdvancedChartsSection();
        this.loadAdvancedCharts();
        this.setupFilterEvents();
    }

    /**
     * Crear la sección de gráficos avanzados
     */
    createAdvancedChartsSection() {
        const mainContent = document.querySelector('.main-content');
        if (!mainContent) {
            console.error('No se encontró .main-content');
            return;
        }

        // Verificar si ya existe la sección
        if (document.getElementById('advanced-charts-section')) {
            return;
        }

        const advancedChartsHTML = `
            <div id="advanced-charts-section" class="dashboard-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-line"></i> Gráficos Avanzados</h2>
                    <div class="chart-controls">
                        <select id="period-filter" class="form-control">
                            <option value="24h">Últimas 24 horas</option>
                            <option value="7d" selected>Últimos 7 días</option>
                            <option value="30d">Últimos 30 días</option>
                            <option value="90d">Últimos 3 meses</option>
                        </select>
                        <select id="metric-filter" class="form-control">
                            <option value="incidents">Incidentes</option>
                            <option value="guards">Actividad de Guardias</option>
                            <option value="response_time">Tiempo de Respuesta</option>
                            <option value="efficiency">Eficiencia</option>
                        </select>
                        <button id="refresh-charts" class="btn btn-primary">
                            <i class="fas fa-sync-alt"></i> Actualizar
                        </button>
                    </div>
                </div>

                <div class="charts-grid">
                    <!-- Gráfico de Tendencias -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>📈 Tendencias Temporales</h3>
                            <div class="chart-info">
                                <span id="trend-info">Cargando...</span>
                            </div>
                        </div>
                        <canvas id="trend-chart" width="400" height="200"></canvas>
                    </div>

                    <!-- Gráfico de Comparación -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>📊 Comparación por Zonas</h3>
                            <div class="chart-info">
                                <span id="comparison-info">Cargando...</span>
                            </div>
                        </div>
                        <canvas id="comparison-chart" width="400" height="200"></canvas>
                    </div>

                    <!-- Gráfico de Distribución -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>🎯 Distribución de Actividad</h3>
                            <div class="chart-info">
                                <span id="distribution-info">Cargando...</span>
                            </div>
                        </div>
                        <canvas id="distribution-chart" width="400" height="200"></canvas>
                    </div>

                    <!-- Gráfico de Predicción -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>🔮 Predicción de Tendencias</h3>
                            <div class="chart-info">
                                <span id="prediction-info">Cargando...</span>
                            </div>
                        </div>
                        <canvas id="prediction-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="loading-overlay" id="charts-loading" style="display: none;">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Cargando gráficos avanzados...</p>
                    </div>
                </div>
            </div>
        `;

        mainContent.insertAdjacentHTML('beforeend', advancedChartsHTML);
    }

    /**
     * Configurar eventos de filtros
     */
    setupFilterEvents() {
        const periodFilter = document.getElementById('period-filter');
        const metricFilter = document.getElementById('metric-filter');
        const refreshBtn = document.getElementById('refresh-charts');

        if (periodFilter) {
            periodFilter.addEventListener('change', (e) => {
                this.currentFilters.period = e.target.value;
                this.loadAdvancedCharts();
            });
        }

        if (metricFilter) {
            metricFilter.addEventListener('change', (e) => {
                this.currentFilters.metric = e.target.value;
                this.loadAdvancedCharts();
            });
        }

        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadAdvancedCharts();
            });
        }
    }

    /**
     * Cargar todos los gráficos avanzados
     */
    async loadAdvancedCharts() {
        this.showLoading(true);

        try {
            // Cargar datos de diferentes endpoints
            const [trendsData, zonesData, chartsData] = await Promise.all([
                this.fetchTrendsData(),
                this.fetchZonesData(),
                this.fetchChartsData()
            ]);

            // Crear gráficos
            this.createTrendChart(trendsData);
            this.createComparisonChart(zonesData);
            this.createDistributionChart(chartsData);
            this.createPredictionChart(trendsData);

            console.log('✅ Gráficos avanzados cargados correctamente');

        } catch (error) {
            console.error('❌ Error al cargar gráficos avanzados:', error);
            this.showError(`Error: ${error.message}`);
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Obtener datos de tendencias
     */
    async fetchTrendsData() {
        const params = new URLSearchParams({
            period: this.currentFilters.period,
            metric: this.currentFilters.metric
        });

        const response = await fetch(`${this.apiBaseUrl}/analytics/charts/?${params}`, {
            method: 'GET',
            headers: window.authManager.getAuthHeaders()
        });

        if (!response.ok) {
            throw new Error(`Error HTTP: ${response.status}`);
        }

        return await response.json();
    }

    /**
     * Obtener datos de zonas
     */
    async fetchZonesData() {
        const response = await fetch(`${this.apiBaseUrl}/analytics/zone-analytics/`, {
            method: 'GET',
            headers: window.authManager.getAuthHeaders()
        });

        if (!response.ok) {
            throw new Error(`Error HTTP: ${response.status}`);
        }

        return await response.json();
    }

    /**
     * Obtener datos de gráficos generales
     */
    async fetchChartsData() {
        const response = await fetch(`${this.apiBaseUrl}/analytics/charts/`, {
            method: 'GET',
            headers: window.authManager.getAuthHeaders()
        });

        if (!response.ok) {
            throw new Error(`Error HTTP: ${response.status}`);
        }

        return await response.json();
    }

    /**
     * Crear gráfico de tendencias
     */
    createTrendChart(data) {
        const ctx = document.getElementById('trend-chart');
        if (!ctx) return;

        // Destruir gráfico anterior si existe
        if (this.charts.has('trend')) {
            this.charts.get('trend').destroy();
        }

        // Verificar que Chart.js esté disponible
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js no está disponible');
            return;
        }

        const chartData = data.charts?.hourly_activity || {};
        const labels = chartData.labels || [];
        const values = chartData.data || [];

        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Actividad',
                    data: values,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                }
            }
        });

        this.charts.set('trend', chart);

        // Actualizar información
        const trendInfo = document.getElementById('trend-info');
        if (trendInfo) {
            const total = values.reduce((a, b) => a + b, 0);
            trendInfo.textContent = `Total: ${total} eventos`;
        }
    }

    /**
     * Mostrar/ocultar loading
     */
    showLoading(show) {
        const loading = document.getElementById('charts-loading');
        if (loading) {
            loading.style.display = show ? 'flex' : 'none';
        }
    }

    /**
     * Mostrar error
     */
    showError(message) {
        // Implementar notificación de error
        console.error(message);
    }

    /**
     * Crear gráfico de comparación por zonas
     */
    createComparisonChart(data) {
        const ctx = document.getElementById('comparison-chart');
        if (!ctx) return;

        // Destruir gráfico anterior si existe
        if (this.charts.has('comparison')) {
            this.charts.get('comparison').destroy();
        }

        if (typeof Chart === 'undefined') {
            console.warn('Chart.js no está disponible');
            return;
        }

        const zoneData = data.zone_analytics?.zones || [];
        const labels = zoneData.slice(0, 10).map(zone => zone.zone_id);
        const values = zoneData.slice(0, 10).map(zone => zone.activity_level * 100);

        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Actividad (%)',
                    data: values,
                    backgroundColor: 'rgba(255, 159, 64, 0.6)',
                    borderColor: 'rgba(255, 159, 64, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                }
            }
        });

        this.charts.set('comparison', chart);

        // Actualizar información
        const comparisonInfo = document.getElementById('comparison-info');
        if (comparisonInfo) {
            const avgActivity = values.reduce((a, b) => a + b, 0) / values.length;
            comparisonInfo.textContent = `Promedio: ${avgActivity.toFixed(1)}%`;
        }
    }

    /**
     * Crear gráfico de distribución
     */
    createDistributionChart(data) {
        const ctx = document.getElementById('distribution-chart');
        if (!ctx) return;

        // Destruir gráfico anterior si existe
        if (this.charts.has('distribution')) {
            this.charts.get('distribution').destroy();
        }

        if (typeof Chart === 'undefined') {
            console.warn('Chart.js no está disponible');
            return;
        }

        const chartData = data.charts?.daily_distribution || {};
        const labels = chartData.labels || ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'];
        const values = chartData.data || [12, 19, 3, 5, 2, 3, 9];

        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40',
                        '#FF6384'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        this.charts.set('distribution', chart);

        // Actualizar información
        const distributionInfo = document.getElementById('distribution-info');
        if (distributionInfo) {
            const total = values.reduce((a, b) => a + b, 0);
            distributionInfo.textContent = `Total: ${total} eventos`;
        }
    }

    /**
     * Crear gráfico de predicción
     */
    createPredictionChart(data) {
        const ctx = document.getElementById('prediction-chart');
        if (!ctx) return;

        // Destruir gráfico anterior si existe
        if (this.charts.has('prediction')) {
            this.charts.get('prediction').destroy();
        }

        if (typeof Chart === 'undefined') {
            console.warn('Chart.js no está disponible');
            return;
        }

        // Datos históricos
        const historical = data.charts?.hourly_activity?.data || [];
        const labels = data.charts?.hourly_activity?.labels || [];

        // Generar predicción simple (tendencia lineal)
        const prediction = this.generatePrediction(historical);

        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [...labels, ...labels.slice(-6).map((_, i) => `+${i+1}h`)],
                datasets: [
                    {
                        label: 'Datos Históricos',
                        data: [...historical, ...Array(6).fill(null)],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 2,
                        fill: false
                    },
                    {
                        label: 'Predicción',
                        data: [...Array(historical.length).fill(null), ...prediction],
                        borderColor: '#f093fb',
                        backgroundColor: 'rgba(240, 147, 251, 0.1)',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                }
            }
        });

        this.charts.set('prediction', chart);

        // Actualizar información
        const predictionInfo = document.getElementById('prediction-info');
        if (predictionInfo) {
            const trend = prediction[prediction.length - 1] > prediction[0] ? 'Creciente' : 'Decreciente';
            predictionInfo.textContent = `Tendencia: ${trend}`;
        }
    }

    /**
     * Generar predicción simple basada en tendencia lineal
     */
    generatePrediction(historical) {
        if (historical.length < 2) {
            return [0, 0, 0, 0, 0, 0];
        }

        // Calcular tendencia promedio
        const lastValue = historical[historical.length - 1];
        const secondLastValue = historical[historical.length - 2];
        const trend = lastValue - secondLastValue;

        // Generar 6 puntos de predicción
        const prediction = [];
        for (let i = 1; i <= 6; i++) {
            const predictedValue = Math.max(0, lastValue + (trend * i));
            prediction.push(Math.round(predictedValue));
        }

        return prediction;
    }

    /**
     * Destruir todos los gráficos
     */
    destroy() {
        this.charts.forEach(chart => chart.destroy());
        this.charts.clear();

        const section = document.getElementById('advanced-charts-section');
        if (section) {
            section.remove();
        }
    }
}

// Instancia global
window.advancedCharts = null;

// Función para inicializar
function initAdvancedCharts() {
    if (!window.advancedCharts) {
        window.advancedCharts = new AdvancedCharts();
        window.advancedCharts.init();
    }
}

// Exportar para uso global
window.initAdvancedCharts = initAdvancedCharts;

import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import WebSocketConnection, Notification

logger = logging.getLogger(__name__)
User = get_user_model()


class NotificationConsumer(AsyncWebsocketConsumer):
    """Consumer para manejar notificaciones en tiempo real via WebSocket"""

    async def connect(self):
        """Maneja la conexión WebSocket"""
        self.user = self.scope["user"]
        
        # Verificar autenticación
        if self.user.is_anonymous:
            await self.close()
            return

        # Crear grupo basado en el tipo de usuario
        self.user_group = f"user_{self.user.id}"
        self.role_group = f"role_{self.user.user_type}"
        
        # Unirse a los grupos
        await self.channel_layer.group_add(
            self.user_group,
            self.channel_name
        )
        
        await self.channel_layer.group_add(
            self.role_group,
            self.channel_name
        )
        
        # Unirse al grupo general
        await self.channel_layer.group_add(
            "notifications",
            self.channel_name
        )

        # Registrar conexión en la base de datos
        await self.register_connection()

        # Aceptar conexión
        await self.accept()
        
        # Enviar notificaciones pendientes
        await self.send_pending_notifications()
        
        logger.info(f"WebSocket conectado: {self.user.username} ({self.channel_name})")

    async def disconnect(self, close_code):
        """Maneja la desconexión WebSocket"""
        if hasattr(self, 'user') and not self.user.is_anonymous:
            # Salir de los grupos
            await self.channel_layer.group_discard(
                self.user_group,
                self.channel_name
            )
            
            await self.channel_layer.group_discard(
                self.role_group,
                self.channel_name
            )
            
            await self.channel_layer.group_discard(
                "notifications",
                self.channel_name
            )

            # Eliminar conexión de la base de datos
            await self.unregister_connection()
            
            logger.info(f"WebSocket desconectado: {self.user.username} ({self.channel_name})")

    async def receive(self, text_data):
        """Maneja mensajes recibidos del cliente"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'ping':
                # Responder a ping con pong
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': timezone.now().isoformat()
                }))
                
            elif message_type == 'mark_read':
                # Marcar notificación como leída
                notification_id = data.get('notification_id')
                await self.mark_notification_read(notification_id)
                
            elif message_type == 'get_notifications':
                # Enviar notificaciones pendientes
                await self.send_pending_notifications()
                
            elif message_type == 'location_update' and self.user.user_type == 'guardia':
                # Actualización de ubicación de guardia
                await self.handle_location_update(data)
                
        except json.JSONDecodeError:
            logger.error(f"JSON inválido recibido de {self.user.username}")
        except Exception as e:
            logger.error(f"Error procesando mensaje de {self.user.username}: {e}")

    async def notification_message(self, event):
        """Envía notificación al cliente"""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'notification': event['notification']
        }))

    async def alert_message(self, event):
        """Envía alerta al cliente"""
        await self.send(text_data=json.dumps({
            'type': 'alert',
            'alert': event['alert']
        }))

    async def location_update(self, event):
        """Envía actualización de ubicación"""
        await self.send(text_data=json.dumps({
            'type': 'location_update',
            'location': event['location']
        }))

    async def route_update(self, event):
        """Envía actualización de ruta"""
        await self.send(text_data=json.dumps({
            'type': 'route_update',
            'route': event['route']
        }))

    async def system_message(self, event):
        """Envía mensaje del sistema"""
        await self.send(text_data=json.dumps({
            'type': 'system_message',
            'message': event['message']
        }))

    @database_sync_to_async
    def register_connection(self):
        """Registra la conexión WebSocket en la base de datos"""
        try:
            # Eliminar conexiones anteriores del mismo usuario
            WebSocketConnection.objects.filter(user=self.user).delete()
            
            # Crear nueva conexión
            WebSocketConnection.objects.create(
                user=self.user,
                channel_name=self.channel_name,
                user_agent=self.scope.get('headers', {}).get('user-agent', ''),
                ip_address=self.get_client_ip()
            )
        except Exception as e:
            logger.error(f"Error registrando conexión WebSocket: {e}")

    @database_sync_to_async
    def unregister_connection(self):
        """Elimina la conexión WebSocket de la base de datos"""
        try:
            WebSocketConnection.objects.filter(
                user=self.user,
                channel_name=self.channel_name
            ).delete()
        except Exception as e:
            logger.error(f"Error eliminando conexión WebSocket: {e}")

    @database_sync_to_async
    def get_pending_notifications(self):
        """Obtiene notificaciones pendientes del usuario"""
        try:
            notifications = Notification.objects.filter(
                recipient=self.user,
                is_sent=False
            ).order_by('-created_at')[:10]
            
            return [notification.to_dict() for notification in notifications]
        except Exception as e:
            logger.error(f"Error obteniendo notificaciones pendientes: {e}")
            return []

    @database_sync_to_async
    def mark_notification_read(self, notification_id):
        """Marca una notificación como leída"""
        try:
            notification = Notification.objects.get(
                id=notification_id,
                recipient=self.user
            )
            notification.mark_as_read()
            return True
        except Notification.DoesNotExist:
            logger.warning(f"Notificación {notification_id} no encontrada para {self.user.username}")
            return False
        except Exception as e:
            logger.error(f"Error marcando notificación como leída: {e}")
            return False

    async def send_pending_notifications(self):
        """Envía todas las notificaciones pendientes al cliente"""
        notifications = await self.get_pending_notifications()
        
        for notification in notifications:
            await self.send(text_data=json.dumps({
                'type': 'notification',
                'notification': notification
            }))

    async def handle_location_update(self, data):
        """Maneja actualización de ubicación de guardia"""
        if self.user.user_type != 'guardia':
            return
            
        location_data = {
            'user_id': self.user.id,
            'username': self.user.username,
            'latitude': data.get('latitude'),
            'longitude': data.get('longitude'),
            'timestamp': timezone.now().isoformat(),
            'accuracy': data.get('accuracy'),
            'heading': data.get('heading'),
            'speed': data.get('speed')
        }
        
        # Enviar actualización a operadores
        await self.channel_layer.group_send(
            "role_operador",
            {
                'type': 'location_update',
                'location': location_data
            }
        )
        
        # Enviar a clientes que tengan este guardia asignado
        # TODO: Implementar lógica para determinar clientes asignados

    def get_client_ip(self):
        """Obtiene la IP del cliente"""
        headers = dict(self.scope.get('headers', []))
        x_forwarded_for = headers.get(b'x-forwarded-for')
        
        if x_forwarded_for:
            ip = x_forwarded_for.decode('utf-8').split(',')[0]
        else:
            ip = self.scope.get('client', [''])[0]
            
        return ip if ip else None

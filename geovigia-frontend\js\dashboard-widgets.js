/**
 * Dashboard Widgets - Widgets Interactivos para GeoVigia
 * Maneja filtros dinámicos, drill-down y configuración personalizable
 */

class DashboardWidgets {
    constructor() {
        this.apiBaseUrl = 'http://127.0.0.1:8000/api';
        this.widgets = new Map();
        this.filters = {
            dateRange: {
                start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                end: new Date().toISOString().split('T')[0]
            },
            zone: 'all',
            type: 'all',
            guardia: 'all'
        };
        this.isExporting = false;
    }

    /**
     * Inicializar widgets del dashboard
     */
    init() {
        console.log('🎯 Inicializando Dashboard Widgets...');
        this.createWidgetsSection();
        this.setupFilterEvents();
        this.loadWidgets();
    }

    /**
     * Crear la sección de widgets
     */
    createWidgetsSection() {
        const mainContent = document.querySelector('.main-content');
        if (!mainContent) {
            console.error('No se encontró .main-content');
            return;
        }

        // Verificar si ya existe la sección
        if (document.getElementById('widgets-section')) {
            return;
        }

        const widgetsHTML = `
            <div id="widgets-section" class="dashboard-section">
                <div class="section-header">
                    <h2><i class="fas fa-th-large"></i> Widgets Interactivos</h2>
                    <div class="widget-controls">
                        <button id="configure-widgets" class="btn btn-secondary">
                            <i class="fas fa-cog"></i> Configurar
                        </button>
                        <button id="export-dashboard" class="btn btn-success">
                            <i class="fas fa-download"></i> Exportar
                        </button>
                    </div>
                </div>

                <!-- Filtros Dinámicos -->
                <div class="filters-panel">
                    <div class="filter-group">
                        <label>📅 Rango de Fechas:</label>
                        <input type="date" id="start-date" class="form-control">
                        <span>hasta</span>
                        <input type="date" id="end-date" class="form-control">
                    </div>
                    <div class="filter-group">
                        <label>🗺️ Zona:</label>
                        <select id="zone-filter" class="form-control">
                            <option value="all">Todas las zonas</option>
                            <option value="north">Zona Norte</option>
                            <option value="south">Zona Sur</option>
                            <option value="east">Zona Este</option>
                            <option value="west">Zona Oeste</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>📋 Tipo:</label>
                        <select id="type-filter" class="form-control">
                            <option value="all">Todos los tipos</option>
                            <option value="alert">Alertas</option>
                            <option value="incident">Incidentes</option>
                            <option value="patrol">Patrullajes</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>👮 Guardia:</label>
                        <select id="guardia-filter" class="form-control">
                            <option value="all">Todos los guardias</option>
                        </select>
                    </div>
                    <button id="apply-filters" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Aplicar Filtros
                    </button>
                </div>

                <!-- Grid de Widgets -->
                <div class="widgets-grid">
                    <!-- Widget de Métricas Clave -->
                    <div class="widget-container" data-widget="key-metrics">
                        <div class="widget-header">
                            <h3>📊 Métricas Clave</h3>
                            <div class="widget-actions">
                                <button class="btn-drill-down" data-metric="incidents">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <button class="btn-export" data-widget="key-metrics">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="widget-content" id="key-metrics-content">
                            <div class="loading">Cargando...</div>
                        </div>
                    </div>

                    <!-- Widget de Actividad por Hora -->
                    <div class="widget-container" data-widget="hourly-activity">
                        <div class="widget-header">
                            <h3>⏰ Actividad por Hora</h3>
                            <div class="widget-actions">
                                <button class="btn-drill-down" data-metric="hourly">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <button class="btn-export" data-widget="hourly-activity">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="widget-content" id="hourly-activity-content">
                            <canvas id="hourly-chart" width="300" height="150"></canvas>
                        </div>
                    </div>

                    <!-- Widget de Top Zonas -->
                    <div class="widget-container" data-widget="top-zones">
                        <div class="widget-header">
                            <h3>🏆 Top Zonas</h3>
                            <div class="widget-actions">
                                <button class="btn-drill-down" data-metric="zones">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <button class="btn-export" data-widget="top-zones">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="widget-content" id="top-zones-content">
                            <div class="loading">Cargando...</div>
                        </div>
                    </div>

                    <!-- Widget de Eficiencia -->
                    <div class="widget-container" data-widget="efficiency">
                        <div class="widget-header">
                            <h3>⚡ Eficiencia del Sistema</h3>
                            <div class="widget-actions">
                                <button class="btn-drill-down" data-metric="efficiency">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <button class="btn-export" data-widget="efficiency">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="widget-content" id="efficiency-content">
                            <div class="efficiency-gauge" id="efficiency-gauge"></div>
                        </div>
                    </div>
                </div>

                <!-- Modal de Drill-Down -->
                <div id="drill-down-modal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 id="drill-down-title">Análisis Detallado</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body" id="drill-down-content">
                            <!-- Contenido dinámico -->
                        </div>
                    </div>
                </div>

                <!-- Modal de Configuración -->
                <div id="config-modal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>⚙️ Configuración de Dashboard</h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="config-section">
                                <h4>Widgets Visibles</h4>
                                <div class="widget-toggles">
                                    <label><input type="checkbox" checked data-widget="key-metrics"> Métricas Clave</label>
                                    <label><input type="checkbox" checked data-widget="hourly-activity"> Actividad por Hora</label>
                                    <label><input type="checkbox" checked data-widget="top-zones"> Top Zonas</label>
                                    <label><input type="checkbox" checked data-widget="efficiency"> Eficiencia</label>
                                </div>
                            </div>
                            <div class="config-section">
                                <h4>Actualización Automática</h4>
                                <select id="auto-refresh" class="form-control">
                                    <option value="0">Desactivado</option>
                                    <option value="30">30 segundos</option>
                                    <option value="60" selected>1 minuto</option>
                                    <option value="300">5 minutos</option>
                                </select>
                            </div>
                            <div class="config-actions">
                                <button id="save-config" class="btn btn-primary">Guardar</button>
                                <button id="reset-config" class="btn btn-secondary">Restablecer</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        mainContent.insertAdjacentHTML('beforeend', widgetsHTML);
    }

    /**
     * Configurar eventos de filtros y controles
     */
    setupFilterEvents() {
        // Filtros de fecha
        const startDate = document.getElementById('start-date');
        const endDate = document.getElementById('end-date');

        if (startDate && endDate) {
            startDate.value = this.filters.dateRange.start;
            endDate.value = this.filters.dateRange.end;
        }

        // Botón aplicar filtros
        const applyBtn = document.getElementById('apply-filters');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.updateFilters();
                this.loadWidgets();
            });
        }

        // Botones de drill-down
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-drill-down')) {
                const metric = e.target.closest('.btn-drill-down').dataset.metric;
                this.showDrillDown(metric);
            }
        });

        // Botones de exportar
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-export')) {
                const widget = e.target.closest('.btn-export').dataset.widget;
                this.exportWidget(widget);
            }
        });

        // Configuración
        const configBtn = document.getElementById('configure-widgets');
        if (configBtn) {
            configBtn.addEventListener('click', () => {
                this.showConfigModal();
            });
        }

        // Exportar dashboard completo
        const exportBtn = document.getElementById('export-dashboard');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportDashboard();
            });
        }

        // Modales
        this.setupModalEvents();
    }

    /**
     * Configurar eventos de modales
     */
    setupModalEvents() {
        // Cerrar modales
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-close') || e.target.classList.contains('modal')) {
                this.closeModals();
            }
        });

        // Escape para cerrar modales
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModals();
            }
        });
    }

    /**
     * Actualizar filtros desde la UI
     */
    updateFilters() {
        const startDate = document.getElementById('start-date');
        const endDate = document.getElementById('end-date');
        const zoneFilter = document.getElementById('zone-filter');
        const typeFilter = document.getElementById('type-filter');
        const guardiaFilter = document.getElementById('guardia-filter');

        if (startDate && endDate) {
            this.filters.dateRange.start = startDate.value;
            this.filters.dateRange.end = endDate.value;
        }

        if (zoneFilter) this.filters.zone = zoneFilter.value;
        if (typeFilter) this.filters.type = typeFilter.value;
        if (guardiaFilter) this.filters.guardia = guardiaFilter.value;

        console.log('🔄 Filtros actualizados:', this.filters);
    }

    /**
     * Cargar todos los widgets
     */
    async loadWidgets() {
        console.log('📊 Cargando widgets...');

        try {
            // Cargar widgets en paralelo
            await Promise.all([
                this.loadKeyMetrics(),
                this.loadHourlyActivity(),
                this.loadTopZones(),
                this.loadEfficiency()
            ]);

            console.log('✅ Widgets cargados correctamente');

        } catch (error) {
            console.error('❌ Error al cargar widgets:', error);
        }
    }

    /**
     * Cargar métricas clave
     */
    async loadKeyMetrics() {
        const content = document.getElementById('key-metrics-content');
        if (!content) return;

        try {
            const response = await fetch(`${this.apiBaseUrl}/analytics/kpis/`, {
                method: 'GET',
                headers: window.authManager.getAuthHeaders()
            });

            if (!response.ok) throw new Error(`Error HTTP: ${response.status}`);

            const data = await response.json();

            const metricsHTML = `
                <div class="metrics-grid">
                    <div class="metric-item" data-metric="guards">
                        <div class="metric-value">${data.active_guards?.value || 0}</div>
                        <div class="metric-label">Guardias Activos</div>
                        <div class="metric-change ${data.active_guards?.change >= 0 ? 'positive' : 'negative'}">
                            ${data.active_guards?.change >= 0 ? '+' : ''}${data.active_guards?.change || 0}%
                        </div>
                    </div>
                    <div class="metric-item" data-metric="properties">
                        <div class="metric-value">${data.monitored_properties?.value || 0}</div>
                        <div class="metric-label">Propiedades</div>
                        <div class="metric-change positive">+0%</div>
                    </div>
                    <div class="metric-item" data-metric="alerts">
                        <div class="metric-value">${data.alerts_today?.value || 0}</div>
                        <div class="metric-label">Alertas Hoy</div>
                        <div class="metric-change negative">-5%</div>
                    </div>
                    <div class="metric-item" data-metric="response">
                        <div class="metric-value">${data.avg_response_time?.value || 0}min</div>
                        <div class="metric-label">Tiempo Respuesta</div>
                        <div class="metric-change positive">-2%</div>
                    </div>
                </div>
            `;

            content.innerHTML = metricsHTML;

        } catch (error) {
            content.innerHTML = '<div class="error">Error al cargar métricas</div>';
            console.error('Error en loadKeyMetrics:', error);
        }
    }

    /**
     * Cargar actividad por hora
     */
    async loadHourlyActivity() {
        const content = document.getElementById('hourly-activity-content');
        if (!content) return;

        try {
            const response = await fetch(`${this.apiBaseUrl}/analytics/charts/`, {
                method: 'GET',
                headers: window.authManager.getAuthHeaders()
            });

            if (!response.ok) throw new Error(`Error HTTP: ${response.status}`);

            const data = await response.json();
            this.createHourlyChart(data);

        } catch (error) {
            console.error('Error en loadHourlyActivity:', error);
        }
    }

    /**
     * Crear gráfico de actividad por hora
     */
    createHourlyChart(data) {
        const ctx = document.getElementById('hourly-chart');
        if (!ctx || typeof Chart === 'undefined') return;

        // Destruir gráfico anterior si existe
        if (this.widgets.has('hourly-chart')) {
            this.widgets.get('hourly-chart').destroy();
        }

        const chartData = data.charts?.hourly_activity || {};
        const labels = chartData.labels || [];
        const values = chartData.data || [];

        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Actividad',
                    data: values,
                    borderColor: '#48bb78',
                    backgroundColor: 'rgba(72, 187, 120, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                }
            }
        });

        this.widgets.set('hourly-chart', chart);
    }

    /**
     * Cargar top zonas
     */
    async loadTopZones() {
        const content = document.getElementById('top-zones-content');
        if (!content) return;

        try {
            const response = await fetch(`${this.apiBaseUrl}/analytics/zone-analytics/`, {
                method: 'GET',
                headers: window.authManager.getAuthHeaders()
            });

            if (!response.ok) throw new Error(`Error HTTP: ${response.status}`);

            const data = await response.json();
            this.displayTopZones(data.zone_analytics);

        } catch (error) {
            content.innerHTML = '<div class="error">Error al cargar zonas</div>';
            console.error('Error en loadTopZones:', error);
        }
    }

    /**
     * Mostrar top zonas
     */
    displayTopZones(zoneData) {
        const content = document.getElementById('top-zones-content');
        if (!content || !zoneData.zones) return;

        const topZones = zoneData.zones
            .sort((a, b) => b.activity_level - a.activity_level)
            .slice(0, 5);

        const zonesHTML = `
            <div class="top-zones-list">
                ${topZones.map((zone, index) => `
                    <div class="zone-item" data-zone="${zone.zone_id}">
                        <div class="zone-rank">#${index + 1}</div>
                        <div class="zone-info">
                            <div class="zone-name">${zone.zone_id}</div>
                            <div class="zone-stats">
                                ${zone.properties_count} propiedades • ${zone.incidents_count} incidentes
                            </div>
                        </div>
                        <div class="zone-activity">
                            <div class="activity-bar">
                                <div class="activity-fill" style="width: ${zone.activity_level * 100}%"></div>
                            </div>
                            <div class="activity-percent">${(zone.activity_level * 100).toFixed(1)}%</div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        content.innerHTML = zonesHTML;
    }

    /**
     * Cargar eficiencia del sistema
     */
    async loadEfficiency() {
        const content = document.getElementById('efficiency-content');
        if (!content) return;

        try {
            const response = await fetch(`${this.apiBaseUrl}/analytics/kpis/`, {
                method: 'GET',
                headers: window.authManager.getAuthHeaders()
            });

            if (!response.ok) throw new Error(`Error HTTP: ${response.status}`);

            const data = await response.json();
            this.displayEfficiencyGauge(data);

        } catch (error) {
            content.innerHTML = '<div class="error">Error al cargar eficiencia</div>';
            console.error('Error en loadEfficiency:', error);
        }
    }

    /**
     * Mostrar gauge de eficiencia
     */
    displayEfficiencyGauge(data) {
        const content = document.getElementById('efficiency-content');
        if (!content) return;

        // Calcular eficiencia general (simulada)
        const efficiency = data.system_efficiency || 85;

        const gaugeHTML = `
            <div class="efficiency-gauge-container">
                <div class="gauge">
                    <div class="gauge-body">
                        <div class="gauge-fill" style="transform: rotate(${(efficiency / 100) * 180}deg)"></div>
                        <div class="gauge-cover">
                            <div class="gauge-value">${efficiency}%</div>
                            <div class="gauge-label">Eficiencia</div>
                        </div>
                    </div>
                </div>
                <div class="efficiency-details">
                    <div class="detail-item">
                        <span class="detail-label">Tiempo Respuesta:</span>
                        <span class="detail-value">${data.avg_response_time?.value || 0}min</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Alertas Resueltas:</span>
                        <span class="detail-value">95%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Cobertura:</span>
                        <span class="detail-value">98%</span>
                    </div>
                </div>
            </div>
        `;

        content.innerHTML = gaugeHTML;
    }

    /**
     * Mostrar drill-down modal
     */
    showDrillDown(metric) {
        const modal = document.getElementById('drill-down-modal');
        const title = document.getElementById('drill-down-title');
        const content = document.getElementById('drill-down-content');

        if (!modal || !title || !content) return;

        title.textContent = `Análisis Detallado: ${metric}`;
        content.innerHTML = `
            <div class="drill-down-placeholder">
                <i class="fas fa-chart-line fa-3x"></i>
                <h3>Análisis Detallado de ${metric}</h3>
                <p>Aquí se mostraría información detallada sobre ${metric}</p>
                <div class="drill-down-actions">
                    <button class="btn btn-primary" onclick="this.closest('.modal').style.display='none'">
                        Cerrar
                    </button>
                </div>
            </div>
        `;

        modal.style.display = 'flex';
    }

    /**
     * Exportar widget individual
     */
    exportWidget(widgetName) {
        console.log(`📊 Exportando widget: ${widgetName}`);

        // Simular exportación
        const data = {
            widget: widgetName,
            timestamp: new Date().toISOString(),
            filters: this.filters
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${widgetName}-export.json`;
        a.click();
        URL.revokeObjectURL(url);

        if (typeof showToast === 'function') {
            showToast(`Widget ${widgetName} exportado correctamente`, 'success');
        }
    }

    /**
     * Exportar dashboard completo
     */
    exportDashboard() {
        if (this.isExporting) return;

        this.isExporting = true;
        console.log('📊 Exportando dashboard completo...');

        // Simular proceso de exportación
        setTimeout(() => {
            const data = {
                dashboard: 'GeoVigia Analytics',
                timestamp: new Date().toISOString(),
                filters: this.filters,
                widgets: Array.from(this.widgets.keys())
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `geovigia-dashboard-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);

            this.isExporting = false;

            if (typeof showToast === 'function') {
                showToast('Dashboard exportado correctamente', 'success');
            }
        }, 1000);
    }

    /**
     * Mostrar modal de configuración
     */
    showConfigModal() {
        const modal = document.getElementById('config-modal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    /**
     * Cerrar modales
     */
    closeModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
    }

    /**
     * Destruir widgets
     */
    destroy() {
        this.widgets.forEach(widget => {
            if (widget.destroy) widget.destroy();
        });
        this.widgets.clear();

        const section = document.getElementById('widgets-section');
        if (section) {
            section.remove();
        }
    }
}

// Instancia global
window.dashboardWidgets = null;

// Función para inicializar
function initDashboardWidgets() {
    if (!window.dashboardWidgets) {
        window.dashboardWidgets = new DashboardWidgets();
        window.dashboardWidgets.init();
    }
}

// Exportar para uso global
window.initDashboardWidgets = initDashboardWidgets;

# Generated by Django 5.2.1 on 2025-05-29 00:59

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('properties', '0002_auto_20250528_2138'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Route',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Nombre identificativo de la ruta', max_length=100, verbose_name='Nombre de la Ruta')),
                ('description', models.TextField(blank=True, help_text='Descripción detallada de la ruta y sus objetivos', verbose_name='Descripción')),
                ('status', models.CharField(choices=[('activa', 'Activa'), ('inactiva', 'Inactiva'), ('mantenimiento', 'En Mantenimiento'), ('suspendida', 'Suspendida')], default='activa', max_length=20, verbose_name='Estado')),
                ('frequency', models.CharField(choices=[('diaria', 'Diaria'), ('semanal', 'Semanal'), ('quincenal', 'Quincenal'), ('mensual', 'Mensual'), ('personalizada', 'Personalizada')], default='diaria', max_length=20, verbose_name='Frecuencia')),
                ('start_time', models.TimeField(help_text='Hora de inicio del recorrido', verbose_name='Hora de Inicio')),
                ('end_time', models.TimeField(help_text='Hora estimada de finalización del recorrido', verbose_name='Hora de Fin')),
                ('estimated_duration_minutes', models.PositiveIntegerField(help_text='Tiempo estimado para completar la ruta en minutos', verbose_name='Duración Estimada (minutos)')),
                ('max_deviation_meters', models.PositiveIntegerField(default=50, help_text='Distancia máxima permitida de desviación de la ruta', verbose_name='Desviación Máxima (metros)')),
                ('priority_level', models.PositiveIntegerField(default=1, help_text='Nivel de prioridad de la ruta (1=Baja, 5=Crítica)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Nivel de Prioridad')),
                ('is_emergency_route', models.BooleanField(default=False, help_text='Indica si es una ruta de emergencia', verbose_name='Ruta de Emergencia')),
                ('monday', models.BooleanField(default=True, verbose_name='Lunes')),
                ('tuesday', models.BooleanField(default=True, verbose_name='Martes')),
                ('wednesday', models.BooleanField(default=True, verbose_name='Miércoles')),
                ('thursday', models.BooleanField(default=True, verbose_name='Jueves')),
                ('friday', models.BooleanField(default=True, verbose_name='Viernes')),
                ('saturday', models.BooleanField(default=False, verbose_name='Sábado')),
                ('sunday', models.BooleanField(default=False, verbose_name='Domingo')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Fecha de Creación')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Última Actualización')),
                ('created_by', models.ForeignKey(blank=True, limit_choices_to={'user_type': 'operador'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_routes', to=settings.AUTH_USER_MODEL, verbose_name='Creado por')),
            ],
            options={
                'verbose_name': 'Ruta',
                'verbose_name_plural': 'Rutas',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RouteProperty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.PositiveIntegerField(help_text='Orden de visita en la ruta (1, 2, 3...)', verbose_name='Orden')),
                ('estimated_time_minutes', models.PositiveIntegerField(default=5, help_text='Tiempo estimado de inspección en esta propiedad', verbose_name='Tiempo Estimado (minutos)')),
                ('is_mandatory', models.BooleanField(default=True, help_text='Indica si la visita a esta propiedad es obligatoria', verbose_name='Visita Obligatoria')),
                ('notes', models.TextField(blank=True, help_text='Notas específicas para esta propiedad en la ruta', verbose_name='Notas')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Fecha de Asignación')),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='property_routes', to='properties.property', verbose_name='Propiedad')),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='route_properties', to='routes.route', verbose_name='Ruta')),
            ],
            options={
                'verbose_name': 'Propiedad en Ruta',
                'verbose_name_plural': 'Propiedades en Rutas',
                'ordering': ['route', 'order'],
                'unique_together': {('route', 'property')},
            },
        ),
        migrations.AddField(
            model_name='route',
            name='properties',
            field=models.ManyToManyField(related_name='routes', through='routes.RouteProperty', to='properties.property', verbose_name='Propiedades'),
        ),
        migrations.CreateModel(
            name='GuardRoute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('activa', 'Activa'), ('temporal', 'Temporal'), ('suspendida', 'Suspendida'), ('finalizada', 'Finalizada')], default='activa', max_length=20, verbose_name='Estado de Asignación')),
                ('start_date', models.DateField(help_text='Fecha de inicio de la asignación', verbose_name='Fecha de Inicio')),
                ('end_date', models.DateField(blank=True, help_text='Fecha de fin de la asignación (opcional para asignaciones permanentes)', null=True, verbose_name='Fecha de Fin')),
                ('is_primary_guard', models.BooleanField(default=False, help_text='Indica si es el guardia principal para esta ruta', verbose_name='Guardia Principal')),
                ('notes', models.TextField(blank=True, help_text='Notas sobre la asignación', verbose_name='Notas')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Fecha de Asignación')),
                ('assigned_by', models.ForeignKey(blank=True, limit_choices_to={'user_type': 'operador'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='guard_assignments_made', to=settings.AUTH_USER_MODEL, verbose_name='Asignado por')),
                ('guard', models.ForeignKey(limit_choices_to={'user_type': 'guardia'}, on_delete=django.db.models.deletion.CASCADE, related_name='assigned_routes', to=settings.AUTH_USER_MODEL, verbose_name='Guardia')),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assigned_guards', to='routes.route', verbose_name='Ruta')),
            ],
            options={
                'verbose_name': 'Asignación de Guardia',
                'verbose_name_plural': 'Asignaciones de Guardias',
                'ordering': ['-created_at'],
                'unique_together': {('guard', 'route', 'start_date')},
            },
        ),
        migrations.CreateModel(
            name='RouteCheckpoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Nombre identificativo del punto de control', max_length=100, verbose_name='Nombre del Punto')),
                ('checkpoint_type', models.CharField(choices=[('inicio', 'Punto de Inicio'), ('propiedad', 'Propiedad'), ('intermedio', 'Punto Intermedio'), ('fin', 'Punto de Fin'), ('emergencia', 'Punto de Emergencia')], default='intermedio', max_length=20, verbose_name='Tipo de Punto')),
                ('latitude', models.DecimalField(decimal_places=8, max_digits=10, verbose_name='Latitud')),
                ('longitude', models.DecimalField(decimal_places=8, max_digits=11, verbose_name='Longitud')),
                ('order', models.PositiveIntegerField(help_text='Orden del punto en la ruta', verbose_name='Orden')),
                ('radius_meters', models.PositiveIntegerField(default=20, help_text='Radio en metros para considerar que se visitó el punto', verbose_name='Radio de Detección (metros)')),
                ('is_mandatory', models.BooleanField(default=True, help_text='Indica si es obligatorio pasar por este punto', verbose_name='Punto Obligatorio')),
                ('estimated_time_minutes', models.PositiveIntegerField(default=2, help_text='Tiempo estimado en este punto de control', verbose_name='Tiempo Estimado (minutos)')),
                ('description', models.TextField(blank=True, help_text='Descripción del punto de control', verbose_name='Descripción')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Fecha de Creación')),
                ('related_property', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='checkpoints', to='properties.property', verbose_name='Propiedad Asociada')),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='checkpoints', to='routes.route', verbose_name='Ruta')),
            ],
            options={
                'verbose_name': 'Punto de Control',
                'verbose_name_plural': 'Puntos de Control',
                'ordering': ['route', 'order'],
                'indexes': [models.Index(fields=['latitude', 'longitude'], name='routes_rout_latitud_93f3fd_idx'), models.Index(fields=['checkpoint_type'], name='routes_rout_checkpo_f5f3ba_idx')],
                'unique_together': {('route', 'order')},
            },
        ),
        migrations.CreateModel(
            name='RouteExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('iniciada', 'Iniciada'), ('en_progreso', 'En Progreso'), ('completada', 'Completada'), ('incompleta', 'Incompleta'), ('cancelada', 'Cancelada')], default='iniciada', max_length=20, verbose_name='Estado')),
                ('start_time', models.DateTimeField(verbose_name='Hora de Inicio')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='Hora de Fin')),
                ('planned_start_time', models.DateTimeField(verbose_name='Hora Planificada de Inicio')),
                ('planned_end_time', models.DateTimeField(verbose_name='Hora Planificada de Fin')),
                ('total_distance_meters', models.PositiveIntegerField(blank=True, help_text='Distancia total recorrida en metros', null=True, verbose_name='Distancia Total (metros)')),
                ('checkpoints_visited', models.PositiveIntegerField(default=0, help_text='Número de puntos de control visitados', verbose_name='Puntos Visitados')),
                ('checkpoints_total', models.PositiveIntegerField(help_text='Número total de puntos de control en la ruta', verbose_name='Total de Puntos')),
                ('properties_visited', models.PositiveIntegerField(default=0, help_text='Número de propiedades visitadas', verbose_name='Propiedades Visitadas')),
                ('properties_total', models.PositiveIntegerField(help_text='Número total de propiedades en la ruta', verbose_name='Total de Propiedades')),
                ('notes', models.TextField(blank=True, help_text='Observaciones del guardia sobre la ejecución', verbose_name='Observaciones')),
                ('incidents_reported', models.PositiveIntegerField(default=0, help_text='Número de incidentes reportados durante la ruta', verbose_name='Incidentes Reportados')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Fecha de Creación')),
                ('guard', models.ForeignKey(limit_choices_to={'user_type': 'guardia'}, on_delete=django.db.models.deletion.CASCADE, related_name='route_executions', to=settings.AUTH_USER_MODEL, verbose_name='Guardia')),
                ('route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='executions', to='routes.route', verbose_name='Ruta')),
            ],
            options={
                'verbose_name': 'Ejecución de Ruta',
                'verbose_name_plural': 'Ejecuciones de Rutas',
                'ordering': ['-start_time'],
                'indexes': [models.Index(fields=['status', 'start_time'], name='routes_rout_status_473541_idx'), models.Index(fields=['guard', 'start_time'], name='routes_rout_guard_i_76116a_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['status', 'frequency'], name='routes_rout_status_266460_idx'),
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['start_time', 'end_time'], name='routes_rout_start_t_a2e003_idx'),
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['priority_level'], name='routes_rout_priorit_5f7ce8_idx'),
        ),
    ]

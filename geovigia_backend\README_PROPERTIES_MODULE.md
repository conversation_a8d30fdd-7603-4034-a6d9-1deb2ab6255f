# Módulo de Propiedades - Sistema GeoVigia

## Descripción
Este es el segundo módulo completado del sistema GeoVigia. Maneja la gestión de propiedades, asignaciones de clientes y perímetros de seguridad para el sistema de vigilancia.

## Características Implementadas

### 🏠 Gestión de Propiedades
- Registro completo de propiedades (casas, apartamentos, oficinas, etc.)
- Información de ubicación con coordenadas GPS
- Clasificación por tipo y prioridad
- Estado de monitoreo activo/inactivo
- Contactos de emergencia por propiedad

### 👥 Asignaciones Cliente-Propiedad
- Relación muchos a muchos entre clientes y propiedades
- Tipos de relación: Propietario, Inquilino, Administrador, Contacto Autorizado
- Contacto principal por propiedad
- Control de recepción de alertas

### 📍 Perímetros de Seguridad
- Perímetros circulares, rectangulares y polígonos personalizados
- Configuración de alertas de entrada y salida
- Cálculo automático de área de cobertura
- Activación/desactivación de perímetros

### 🗺️ Funcionalidades Geográficas
- Búsqueda de propiedades por ubicación
- Cálculo de propiedades cercanas por radio
- Validación de coordenadas GPS
- Optimización con índices geográficos

## APIs Disponibles

### Gestión de Propiedades
- `GET /api/properties/` - Listar propiedades
- `POST /api/properties/` - Crear nueva propiedad
- `GET /api/properties/<id>/` - Obtener propiedad específica
- `PUT /api/properties/<id>/` - Actualizar propiedad
- `DELETE /api/properties/<id>/` - Eliminar propiedad
- `GET /api/properties/my-properties/` - Propiedades del cliente autenticado
- `GET /api/properties/locations/` - Ubicaciones para mapas
- `GET /api/properties/near/` - Propiedades cercanas a ubicación

### Asignaciones de Propiedades
- `GET /api/properties/assignments/` - Listar asignaciones
- `POST /api/properties/assignments/` - Crear asignación
- `GET /api/properties/assignments/<id>/` - Obtener asignación específica
- `PUT /api/properties/assignments/<id>/` - Actualizar asignación
- `DELETE /api/properties/assignments/<id>/` - Eliminar asignación
- `POST /api/properties/<id>/assign/` - Asignar propiedad a cliente

### Perímetros de Propiedades
- `GET /api/properties/perimeters/` - Listar perímetros
- `POST /api/properties/perimeters/` - Crear perímetro
- `GET /api/properties/perimeters/<id>/` - Obtener perímetro específico
- `PUT /api/properties/perimeters/<id>/` - Actualizar perímetro
- `DELETE /api/properties/perimeters/<id>/` - Eliminar perímetro
- `GET /api/properties/<id>/perimeter/` - Perímetro de propiedad específica
- `POST /api/properties/<id>/perimeter/create/` - Crear perímetro para propiedad

## Estructura de Datos

### Propiedad (Property)
```json
{
  "id": 1,
  "name": "Casa Principal",
  "property_type": "casa",
  "address": "Calle 123, Colonia Centro",
  "neighborhood": "Centro",
  "city": "Ciudad de México",
  "postal_code": "01000",
  "latitude": "19.4326",
  "longitude": "-99.1332",
  "description": "Casa principal de la familia",
  "area_size": 200,
  "floors": 2,
  "status": "activa",
  "is_monitored": true,
  "priority_level": 3,
  "emergency_contact_name": "Juan Pérez",
  "emergency_contact_phone": "+5215551234567",
  "coordinates": [19.4326, -99.1332],
  "full_address": "Calle 123, Colonia Centro, Centro, Ciudad de México, 01000",
  "is_high_priority": false,
  "created_at": "2025-05-28T21:00:00Z",
  "updated_at": "2025-05-28T21:00:00Z"
}
```

### Asignación de Propiedad (PropertyOwnership)
```json
{
  "id": 1,
  "client": 2,
  "property": 1,
  "ownership_type": "propietario",
  "is_primary": true,
  "can_receive_alerts": true,
  "notes": "Contacto principal",
  "client_username": "cliente_test",
  "client_name": "Juan Pérez",
  "property_name": "Casa Principal",
  "created_at": "2025-05-28T21:00:00Z"
}
```

### Perímetro de Propiedad (PropertyPerimeter)
```json
{
  "id": 1,
  "property": 1,
  "perimeter_type": "circular",
  "radius_meters": 100,
  "width_meters": null,
  "height_meters": null,
  "polygon_coordinates": null,
  "is_active": true,
  "alert_on_entry": true,
  "alert_on_exit": true,
  "description": "Perímetro de seguridad estándar",
  "property_name": "Casa Principal",
  "area_coverage": 31415.926535897932,
  "created_at": "2025-05-28T21:00:00Z",
  "updated_at": "2025-05-28T21:00:00Z"
}
```

## Permisos y Autorización

### Por Tipo de Usuario:
- **Operadores**: Acceso completo a todas las propiedades y funciones
- **Clientes**: Solo pueden ver y gestionar sus propias propiedades
- **Guardias**: Pueden ver propiedades bajo monitoreo en sus rutas

### Funciones Específicas:
- **Crear Propiedades**: Operadores y Clientes (auto-asignación)
- **Asignar Propiedades**: Solo Operadores
- **Crear Perímetros**: Operadores y Propietarios de la propiedad
- **Ver Ubicaciones**: Según permisos de cada propiedad

## Validaciones Implementadas

### Coordenadas GPS:
- Latitud: -90° a 90°
- Longitud: -180° a 180°

### Perímetros:
- Circular: Radio mínimo requerido
- Rectangular: Ancho y alto requeridos
- Polígono: Mínimo 3 puntos

### Asignaciones:
- Solo usuarios tipo 'cliente' pueden ser asignados
- No duplicar asignaciones cliente-propiedad

## Pruebas
Ejecuta el script de pruebas para verificar todas las funcionalidades:
```bash
python test_properties_apis.py
```

### Pruebas Incluidas:
- ✅ Creación de propiedades
- ✅ Asignación de propiedades a clientes
- ✅ Creación de perímetros de seguridad
- ✅ Obtención de ubicaciones para mapas
- ✅ Búsqueda de propiedades cercanas

## Integración con Otros Módulos

### Con Módulo de Usuarios:
- Asignaciones automáticas a clientes
- Validación de tipos de usuario
- Permisos basados en roles

### Para Módulos Futuros:
- **Rutas**: Propiedades incluidas en recorridos
- **Alertas**: Origen de alertas por propiedad
- **Seguimiento**: Verificación de perímetros

## Optimizaciones Implementadas

### Base de Datos:
- Índices en coordenadas geográficas
- Índices en estado y monitoreo
- Índices en nivel de prioridad

### APIs:
- Serializers optimizados por caso de uso
- Consultas con prefetch_related
- Paginación automática

---
**Estado**: ✅ COMPLETADO
**Fecha**: Mayo 2025
**Versión**: 1.0
**Dependencias**: Módulo de Usuarios

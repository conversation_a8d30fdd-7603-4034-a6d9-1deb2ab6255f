#!/usr/bin/env python3
"""
Script para probar las APIs de analytics
"""

import requests
import json

# Configuración
BASE_URL = "http://127.0.0.1:8000/api"

def get_auth_token():
    """Obtener token de autenticación"""
    print("🔐 Obteniendo token de autenticación...")

    response = requests.post(f"{BASE_URL}/../api-token-auth/", {
        'username': 'operador',
        'password': 'operador123'
    })

    if response.status_code == 200:
        token = response.json()['token']
        print(f"✅ Token obtenido: {token[:20]}...")
        return token
    else:
        print(f"❌ Error obteniendo token: {response.status_code}")
        print(response.text)
        return None

def test_kpi_api(token):
    """Probar la API de KPIs"""
    print("\n📊 Probando API de KPIs...")

    headers = {'Authorization': f'Token {token}'}
    response = requests.get(f"{BASE_URL}/analytics/kpis/", headers=headers)

    if response.status_code == 200:
        data = response.json()
        print("✅ API de KPIs funcionando correctamente")
        print(f"📈 KPIs obtenidos:")
        for kpi_name, kpi_data in data['kpis'].items():
            print(f"   • {kpi_data['label']}: {kpi_data['value']} ({kpi_data['change']})")
        return True
    else:
        print(f"❌ Error en API de KPIs: {response.status_code}")
        print(response.text)
        return False

def test_charts_api(token):
    """Probar la API de gráficos"""
    print("\n📊 Probando API de Gráficos...")

    headers = {'Authorization': f'Token {token}'}
    response = requests.get(f"{BASE_URL}/analytics/charts/", headers=headers)

    if response.status_code == 200:
        data = response.json()
        print("✅ API de Gráficos funcionando correctamente")
        print(f"📈 Gráficos disponibles:")
        for chart_name, chart_data in data['charts'].items():
            print(f"   • {chart_data['title']} ({chart_data['type']})")
        return True
    else:
        print(f"❌ Error en API de Gráficos: {response.status_code}")
        print(response.text)
        return False

def test_individual_apis(token):
    """Probar APIs individuales"""
    print("\n🔍 Probando APIs individuales...")

    headers = {'Authorization': f'Token {token}'}
    apis = [
        ('users/analytics/stats/', 'Usuarios'),
        ('properties/analytics/stats/', 'Propiedades'),
        ('notifications/analytics/stats/', 'Notificaciones')
    ]

    for endpoint, name in apis:
        print(f"\n📡 Probando API de {name}...")
        response = requests.get(f"{BASE_URL}/{endpoint}", headers=headers)

        if response.status_code == 200:
            print(f"✅ API de {name} funcionando")
            data = response.json()
            print(f"   Datos obtenidos: {len(str(data))} caracteres")
        else:
            print(f"❌ Error en API de {name}: {response.status_code}")

def main():
    print("🚀 Iniciando pruebas de Analytics...")

    # Obtener token
    token = get_auth_token()
    if not token:
        return

    # Probar API principal de KPIs
    if test_kpi_api(token):
        print("\n🎯 ¡La API principal de KPIs está funcionando!")

    # Probar API de gráficos
    if test_charts_api(token):
        print("\n📊 ¡La API de gráficos está funcionando!")

    # Probar APIs individuales
    test_individual_apis(token)

    print("\n" + "="*60)
    print("✅ Pruebas completadas")
    print("🌐 Abre http://127.0.0.1:3000 para ver el dashboard")
    print("🔑 Usa las credenciales: operador / operador123")
    print("="*60)

if __name__ == '__main__':
    main()

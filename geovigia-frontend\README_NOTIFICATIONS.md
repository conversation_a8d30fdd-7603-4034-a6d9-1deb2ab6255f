# 🔔 Sistema de Notificaciones Push en Tiempo Real - GeoVigia

## Descripción General

GeoVigia ahora incluye un **sistema completo de notificaciones push en tiempo real** que permite comunicación bidireccional entre operadores, guardias y clientes usando WebSockets, Push API del navegador y notificaciones visuales.

## 🚀 Características Implementadas

### ✅ **Backend (Django Channels)**
- **WebSockets**: Comunicación en tiempo real
- **Modelos de Notificaciones**: Base de datos completa
- **API REST**: Endpoints para gestión de notificaciones
- **Push Notifications**: Soporte para notificaciones del navegador
- **Signals**: Automatización de notificaciones por eventos

### ✅ **Frontend (JavaScript)**
- **Cliente WebSocket**: Conexión automática y reconexión
- **Service Worker**: PWA con notificaciones offline
- **UI Interactiva**: Dashboard completo de notificaciones
- **Toast Notifications**: Notificaciones visuales en tiempo real
- **Filtros y Búsqueda**: Gestión avanzada de notificaciones

## 📊 Tipos de Notificaciones

### 🔔 **Notificaciones Básicas**
- **INFO**: Información general del sistema
- **SUCCESS**: Confirmaciones de acciones exitosas
- **WARNING**: Advertencias importantes
- **ERROR**: Errores que requieren atención

### 🚨 **Alertas Especiales**
- **EMERGENCY**: Alertas de emergencia críticas
- **ROUTE_UPDATE**: Actualizaciones de rutas de patrullaje
- **PROPERTY_ALERT**: Alertas específicas de propiedades
- **GUARD_STATUS**: Cambios de estado de guardias

## 🎯 Prioridades de Notificaciones

- **LOW**: Notificaciones informativas
- **NORMAL**: Notificaciones estándar
- **HIGH**: Notificaciones importantes
- **CRITICAL**: Alertas críticas que requieren atención inmediata

## 🔧 Configuración Técnica

### **WebSocket URL**
```javascript
ws://localhost:8000/ws/notifications/
```

### **API Endpoints**
```
GET    /api/notifications/              # Listar notificaciones
POST   /api/notifications/create/       # Crear notificación
POST   /api/notifications/{id}/read/    # Marcar como leída
POST   /api/notifications/mark-all-read/ # Marcar todas como leídas
GET    /api/notifications/unread-count/ # Contador de no leídas
POST   /api/notifications/push/subscribe/ # Suscribirse a push
POST   /api/notifications/alert/        # Enviar alerta de emergencia
```

### **Configuración de Redis**
```python
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [('127.0.0.1', 6379)],
        },
    },
}
```

## 📱 Funcionalidades por Rol

### 🏢 **Operadores**
- **Dashboard Completo**: Vista de todas las notificaciones del sistema
- **Filtros Avanzados**: Por tipo, estado, prioridad y fecha
- **Crear Notificaciones**: Enviar mensajes a guardias y clientes
- **Gestión de Alertas**: Responder a emergencias
- **Estadísticas**: Métricas de notificaciones y usuarios conectados

### 👮 **Guardias**
- **Notificaciones de Ruta**: Actualizaciones automáticas de recorridos
- **Alertas de Emergencia**: Recepción inmediata de alertas críticas
- **Ubicación en Tiempo Real**: Envío automático de posición GPS
- **Estado de Servicio**: Notificaciones de cambios de estado
- **Comunicación Directa**: Chat con operadores

### 🏠 **Clientes**
- **Alertas de Propiedad**: Notificaciones específicas de su inmueble
- **Estado del Guardia**: Información del guardia asignado
- **Botones de Emergencia**: Envío rápido de alertas
- **Historial de Eventos**: Registro de actividad en su propiedad

## 🎨 Interfaz de Usuario

### **Dashboard de Notificaciones**
- **Estadísticas en Tiempo Real**: Contadores dinámicos
- **Lista Interactiva**: Notificaciones con acciones rápidas
- **Filtros Inteligentes**: Búsqueda y clasificación avanzada
- **Indicadores Visuales**: Colores por prioridad y estado

### **Toast Notifications**
- **Aparición Automática**: Notificaciones no intrusivas
- **Colores por Tipo**: Identificación visual inmediata
- **Acciones Rápidas**: Botones de acción directa
- **Auto-desaparición**: Limpieza automática después de 5 segundos

### **Alertas de Emergencia**
- **Modal Prominente**: Ventana modal para alertas críticas
- **Sonido de Alerta**: Notificación auditiva
- **Vibración**: Feedback háptico en dispositivos móviles
- **Acción Requerida**: Confirmación obligatoria

## 🔄 Flujo de Notificaciones

### **1. Creación de Notificación**
```
Usuario/Sistema → API → Base de Datos → WebSocket → Cliente
```

### **2. Notificación Push**
```
Servidor → Service Worker → Navegador → Usuario
```

### **3. Actualización en Tiempo Real**
```
WebSocket → JavaScript → UI → Usuario
```

## 🛠️ Comandos de Desarrollo

### **Iniciar Redis (requerido)**
```bash
# Windows (con Redis instalado)
redis-server

# Docker
docker run -d -p 6379:6379 redis:alpine
```

### **Iniciar Servidor Django**
```bash
cd geovigia_backend
python manage.py runserver
```

### **Verificar WebSockets**
```bash
# Abrir en navegador
ws://localhost:8000/ws/notifications/
```

## 📋 Testing y Debugging

### **Probar Notificaciones**
1. **Abrir múltiples pestañas** con diferentes roles
2. **Enviar notificación** desde el dashboard de operador
3. **Verificar recepción** en tiempo real
4. **Probar filtros** y acciones de notificaciones

### **Debugging WebSocket**
```javascript
// En consola del navegador
console.log(window.geoVigiaNotifications.isConnected);
window.geoVigiaNotifications.sendPing();
```

### **Logs del Sistema**
```python
# En settings.py
LOGGING = {
    'loggers': {
        'notifications': {
            'level': 'DEBUG',
        },
    },
}
```

## 🔐 Seguridad

### **Autenticación WebSocket**
- **Token Authentication**: Verificación de usuario autenticado
- **Grupos por Rol**: Separación de canales por tipo de usuario
- **Validación de Permisos**: Control de acceso a notificaciones

### **Push Notifications**
- **VAPID Keys**: Claves de autenticación para push
- **Suscripciones Seguras**: Validación de endpoints
- **Datos Encriptados**: Payload seguro en notificaciones

## 🚀 Próximas Mejoras

- **📱 App Móvil**: Notificaciones nativas en iOS/Android
- **🤖 Chatbot**: Respuestas automáticas inteligentes
- **📊 Analytics**: Métricas avanzadas de notificaciones
- **🔊 Sonidos Personalizados**: Tonos específicos por tipo
- **🌐 Multiidioma**: Soporte para múltiples idiomas
- **📧 Email Backup**: Respaldo por correo electrónico

## 📞 Soporte

Para problemas o preguntas sobre el sistema de notificaciones:

1. **Revisar logs** del servidor y navegador
2. **Verificar conexión** a Redis
3. **Comprobar permisos** de notificaciones del navegador
4. **Validar configuración** de WebSocket

---

**¡El sistema de notificaciones push está completamente funcional! 🎉**

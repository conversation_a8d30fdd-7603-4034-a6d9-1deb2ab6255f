<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Notificaciones Push GeoVigia</title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2rem;
        }
        
        .content {
            padding: 2rem;
        }
        
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 2px solid #e9ecef;
            border-radius: 12px;
        }
        
        .test-section h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .controls {
            margin: 1rem 0;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn.success { background: #48bb78; }
        .btn.warning { background: #ed8936; }
        .btn.danger { background: #f56565; }
        .btn.info { background: #4299e1; }
        
        .status {
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
            border-left: 4px solid #667eea;
        }
        
        .status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .status.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem;
            border-radius: 3px;
        }
        
        .log-entry.info { background: #e3f2fd; }
        .log-entry.success { background: #e8f5e8; }
        .log-entry.error { background: #ffebee; }
        .log-entry.warning { background: #fff8e1; }
        
        .connection-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .connection-indicator.connected { background: #48bb78; }
        .connection-indicator.disconnected { background: #f56565; }
        .connection-indicator.connecting { background: #ed8936; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bell"></i> Test de Notificaciones Push GeoVigia</h1>
            <p>Verificación completa del sistema de notificaciones en tiempo real</p>
        </div>
        
        <div class="content">
            <!-- Estado de Conexión -->
            <div class="test-section">
                <h2>🔌 Estado de Conexión WebSocket</h2>
                <div id="connection-status" class="status">
                    <span class="connection-indicator disconnected"></span>
                    Desconectado
                </div>
                <div class="controls">
                    <button class="btn" onclick="testConnection()">
                        <i class="fas fa-plug"></i> Probar Conexión
                    </button>
                    <button class="btn" onclick="reconnectWebSocket()">
                        <i class="fas fa-sync"></i> Reconectar
                    </button>
                    <button class="btn" onclick="sendPing()">
                        <i class="fas fa-heartbeat"></i> Enviar Ping
                    </button>
                </div>
            </div>
            
            <!-- Test de Notificaciones -->
            <div class="test-section">
                <h2>🔔 Test de Notificaciones</h2>
                <div class="controls">
                    <button class="btn info" onclick="sendTestNotification('info')">
                        <i class="fas fa-info-circle"></i> Info
                    </button>
                    <button class="btn success" onclick="sendTestNotification('success')">
                        <i class="fas fa-check-circle"></i> Éxito
                    </button>
                    <button class="btn warning" onclick="sendTestNotification('warning')">
                        <i class="fas fa-exclamation-triangle"></i> Advertencia
                    </button>
                    <button class="btn danger" onclick="sendTestNotification('emergency')">
                        <i class="fas fa-exclamation-triangle"></i> Emergencia
                    </button>
                </div>
                <div id="notification-status" class="status">
                    Presiona un botón para enviar una notificación de prueba
                </div>
            </div>
            
            <!-- Test de Push Notifications -->
            <div class="test-section">
                <h2>📱 Test de Push Notifications</h2>
                <div class="controls">
                    <button class="btn" onclick="requestNotificationPermission()">
                        <i class="fas fa-bell"></i> Solicitar Permisos
                    </button>
                    <button class="btn" onclick="testPushNotification()">
                        <i class="fas fa-mobile-alt"></i> Test Push
                    </button>
                    <button class="btn" onclick="registerServiceWorker()">
                        <i class="fas fa-cog"></i> Registrar SW
                    </button>
                </div>
                <div id="push-status" class="status">
                    Estado de push notifications: <span id="push-permission">Desconocido</span>
                </div>
            </div>
            
            <!-- Test de Geolocalización -->
            <div class="test-section">
                <h2>📍 Test de Geolocalización</h2>
                <div class="controls">
                    <button class="btn" onclick="getCurrentLocation()">
                        <i class="fas fa-map-marker-alt"></i> Obtener Ubicación
                    </button>
                    <button class="btn" onclick="sendLocationUpdate()">
                        <i class="fas fa-location-arrow"></i> Enviar Ubicación
                    </button>
                    <button class="btn" onclick="startLocationTracking()">
                        <i class="fas fa-satellite"></i> Seguimiento Continuo
                    </button>
                </div>
                <div id="location-status" class="status">
                    Ubicación: No disponible
                </div>
            </div>
            
            <!-- Log de Eventos -->
            <div class="test-section">
                <h2>📋 Log de Eventos</h2>
                <div class="controls">
                    <button class="btn" onclick="clearLog()">
                        <i class="fas fa-trash"></i> Limpiar Log
                    </button>
                    <button class="btn" onclick="exportLog()">
                        <i class="fas fa-download"></i> Exportar Log
                    </button>
                </div>
                <div id="event-log" class="log">
                    <div class="log-entry info">Sistema de test iniciado...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/notifications.js"></script>
    
    <script>
        let testNotifications = null;
        let locationWatcher = null;
        
        // Inicializar cuando el DOM esté listo
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Iniciando sistema de test...', 'info');
            
            // Simular token de autenticación para testing
            localStorage.setItem('authToken', 'test-token-123');
            
            // Inicializar sistema de notificaciones
            if (typeof GeoVigiaNotifications !== 'undefined') {
                testNotifications = new GeoVigiaNotifications();
                log('✅ Sistema de notificaciones inicializado', 'success');
                
                // Monitorear estado de conexión
                monitorConnection();
            } else {
                log('❌ Sistema de notificaciones no disponible', 'error');
            }
            
            // Verificar permisos de notificación
            updatePushPermissionStatus();
        });

        function log(message, type = 'info') {
            const logContainer = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function testConnection() {
            log('🔌 Probando conexión WebSocket...', 'info');
            
            if (testNotifications && testNotifications.isConnected) {
                log('✅ WebSocket conectado correctamente', 'success');
                updateConnectionStatus('connected', 'Conectado');
            } else {
                log('❌ WebSocket no conectado', 'error');
                updateConnectionStatus('disconnected', 'Desconectado');
            }
        }

        function reconnectWebSocket() {
            log('🔄 Intentando reconectar WebSocket...', 'info');
            
            if (testNotifications) {
                testNotifications.connectWebSocket();
                setTimeout(() => {
                    testConnection();
                }, 2000);
            }
        }

        function sendPing() {
            log('💓 Enviando ping al servidor...', 'info');
            
            if (testNotifications && testNotifications.isConnected) {
                testNotifications.sendPing();
                log('✅ Ping enviado', 'success');
            } else {
                log('❌ No se puede enviar ping - WebSocket desconectado', 'error');
            }
        }

        function sendTestNotification(type) {
            log(`🔔 Enviando notificación de prueba: ${type}`, 'info');
            
            const messages = {
                info: 'Esta es una notificación informativa de prueba',
                success: '¡Operación completada exitosamente!',
                warning: 'Advertencia: Esto es una prueba',
                emergency: '🚨 ALERTA DE EMERGENCIA - ESTO ES UNA PRUEBA'
            };
            
            if (testNotifications) {
                testNotifications.showToast(
                    `Test ${type.toUpperCase()}`,
                    type,
                    messages[type]
                );
                log(`✅ Notificación ${type} mostrada`, 'success');
            } else {
                log('❌ Sistema de notificaciones no disponible', 'error');
            }
        }

        function requestNotificationPermission() {
            log('🔔 Solicitando permisos de notificación...', 'info');
            
            if ('Notification' in window) {
                Notification.requestPermission().then(permission => {
                    log(`📱 Permiso de notificación: ${permission}`, permission === 'granted' ? 'success' : 'warning');
                    updatePushPermissionStatus();
                });
            } else {
                log('❌ Notificaciones no soportadas en este navegador', 'error');
            }
        }

        function testPushNotification() {
            log('📱 Probando push notification...', 'info');
            
            if (Notification.permission === 'granted') {
                new Notification('GeoVigia Test', {
                    body: 'Esta es una notificación push de prueba',
                    icon: '/favicon.ico',
                    tag: 'test-notification'
                });
                log('✅ Push notification enviada', 'success');
            } else {
                log('❌ Permisos de notificación no concedidos', 'error');
            }
        }

        function registerServiceWorker() {
            log('⚙️ Registrando Service Worker...', 'info');
            
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        log('✅ Service Worker registrado', 'success');
                    })
                    .catch(error => {
                        log(`❌ Error registrando Service Worker: ${error}`, 'error');
                    });
            } else {
                log('❌ Service Worker no soportado', 'error');
            }
        }

        function getCurrentLocation() {
            log('📍 Obteniendo ubicación actual...', 'info');
            
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(
                    position => {
                        const { latitude, longitude, accuracy } = position.coords;
                        log(`✅ Ubicación obtenida: ${latitude}, ${longitude} (±${accuracy}m)`, 'success');
                        updateLocationStatus(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);
                    },
                    error => {
                        log(`❌ Error obteniendo ubicación: ${error.message}`, 'error');
                    }
                );
            } else {
                log('❌ Geolocalización no soportada', 'error');
            }
        }

        function sendLocationUpdate() {
            log('📡 Enviando actualización de ubicación...', 'info');
            
            if ('geolocation' in navigator && testNotifications) {
                navigator.geolocation.getCurrentPosition(
                    position => {
                        const { latitude, longitude, accuracy, heading, speed } = position.coords;
                        testNotifications.sendLocationUpdate(latitude, longitude, accuracy, heading, speed);
                        log('✅ Ubicación enviada al servidor', 'success');
                    },
                    error => {
                        log(`❌ Error enviando ubicación: ${error.message}`, 'error');
                    }
                );
            }
        }

        function startLocationTracking() {
            log('🛰️ Iniciando seguimiento continuo...', 'info');
            
            if (locationWatcher) {
                navigator.geolocation.clearWatch(locationWatcher);
                locationWatcher = null;
                log('⏹️ Seguimiento detenido', 'warning');
                return;
            }
            
            if ('geolocation' in navigator) {
                locationWatcher = navigator.geolocation.watchPosition(
                    position => {
                        const { latitude, longitude } = position.coords;
                        log(`📍 Ubicación actualizada: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`, 'info');
                        
                        if (testNotifications) {
                            testNotifications.sendLocationUpdate(latitude, longitude);
                        }
                    },
                    error => {
                        log(`❌ Error en seguimiento: ${error.message}`, 'error');
                    },
                    { enableHighAccuracy: true, timeout: 10000, maximumAge: 60000 }
                );
                log('✅ Seguimiento iniciado', 'success');
            }
        }

        function clearLog() {
            document.getElementById('event-log').innerHTML = '';
            log('🧹 Log limpiado', 'info');
        }

        function exportLog() {
            const logEntries = document.querySelectorAll('.log-entry');
            let logText = 'GeoVigia Notifications Test Log\n';
            logText += '================================\n\n';
            
            logEntries.forEach(entry => {
                logText += entry.textContent + '\n';
            });
            
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `geovigia-test-log-${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('📥 Log exportado', 'success');
        }

        function updateConnectionStatus(status, text) {
            const statusElement = document.getElementById('connection-status');
            const indicator = statusElement.querySelector('.connection-indicator');
            
            indicator.className = `connection-indicator ${status}`;
            statusElement.innerHTML = `<span class="connection-indicator ${status}"></span>${text}`;
            statusElement.className = `status ${status === 'connected' ? 'success' : 'error'}`;
        }

        function updatePushPermissionStatus() {
            const permissionSpan = document.getElementById('push-permission');
            const permission = Notification.permission;
            permissionSpan.textContent = permission;
            
            const statusElement = document.getElementById('push-status');
            statusElement.className = `status ${permission === 'granted' ? 'success' : 'warning'}`;
        }

        function updateLocationStatus(location) {
            const statusElement = document.getElementById('location-status');
            statusElement.innerHTML = `Ubicación: ${location}`;
            statusElement.className = 'status success';
        }

        function monitorConnection() {
            setInterval(() => {
                if (testNotifications) {
                    const isConnected = testNotifications.isConnected;
                    updateConnectionStatus(
                        isConnected ? 'connected' : 'disconnected',
                        isConnected ? 'Conectado' : 'Desconectado'
                    );
                }
            }, 5000);
        }
    </script>
</body>
</html>

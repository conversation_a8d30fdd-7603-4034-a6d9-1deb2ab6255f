import json
import logging
from django.conf import settings
from .models import PushSubscription

logger = logging.getLogger(__name__)

# Nota: Para implementación completa de Web Push, se necesitaría instalar pywebpush
# pip install pywebpush
# Por ahora, implementamos la estructura básica

def send_push_notification(notification):
    """
    Envía una notificación push al navegador del usuario
    
    Args:
        notification: Instancia del modelo Notification
    """
    try:
        # Obtener suscripciones activas del usuario
        subscriptions = PushSubscription.objects.filter(
            user=notification.recipient,
            is_active=True
        )
        
        if not subscriptions.exists():
            logger.info(f"No hay suscripciones push para {notification.recipient.username}")
            return
        
        # Preparar payload de la notificación
        payload = {
            'title': notification.title,
            'body': notification.message,
            'icon': '/static/icons/geovigia-icon-192.png',
            'badge': '/static/icons/geovigia-badge-72.png',
            'tag': str(notification.id),
            'data': {
                'notification_id': str(notification.id),
                'type': notification.notification_type,
                'priority': notification.priority,
                'url': get_notification_url(notification),
                'timestamp': notification.created_at.isoformat(),
                'extra_data': notification.extra_data
            },
            'actions': get_notification_actions(notification),
            'requireInteraction': notification.priority in ['high', 'critical'],
            'silent': notification.priority == 'low'
        }
        
        # Enviar a cada suscripción
        for subscription in subscriptions:
            try:
                send_to_subscription(subscription, payload)
                logger.info(f"Push notification enviada a {notification.recipient.username}")
            except Exception as e:
                logger.error(f"Error enviando push a suscripción {subscription.id}: {e}")
                # Marcar suscripción como inactiva si falla
                subscription.is_active = False
                subscription.save()
                
    except Exception as e:
        logger.error(f"Error enviando push notification: {e}")


def send_to_subscription(subscription, payload):
    """
    Envía payload a una suscripción específica
    
    Args:
        subscription: Instancia del modelo PushSubscription
        payload: Diccionario con los datos de la notificación
    """
    # TODO: Implementar envío real con pywebpush
    # from pywebpush import webpush, WebPushException
    
    # try:
    #     webpush(
    #         subscription_info={
    #             "endpoint": subscription.endpoint,
    #             "keys": {
    #                 "p256dh": subscription.p256dh_key,
    #                 "auth": subscription.auth_key
    #             }
    #         },
    #         data=json.dumps(payload),
    #         vapid_private_key=settings.PUSH_NOTIFICATIONS['VAPID_PRIVATE_KEY'],
    #         vapid_claims={
    #             "sub": f"mailto:{settings.PUSH_NOTIFICATIONS['VAPID_ADMIN_EMAIL']}"
    #         }
    #     )
    # except WebPushException as ex:
    #     logger.error(f"WebPush failed: {ex}")
    #     raise
    
    # Por ahora, solo loggeamos que se enviaría
    logger.info(f"[SIMULADO] Push notification enviada a {subscription.endpoint[:50]}...")


def get_notification_url(notification):
    """
    Obtiene la URL a la que debe dirigir la notificación
    
    Args:
        notification: Instancia del modelo Notification
        
    Returns:
        str: URL de destino
    """
    base_url = getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')
    
    # URLs según el tipo de usuario
    if notification.recipient.user_type == 'operador':
        return f"{base_url}/#notifications"
    elif notification.recipient.user_type == 'guardia':
        return f"{base_url}/guardia-dashboard.html"
    elif notification.recipient.user_type == 'cliente':
        return f"{base_url}/cliente-dashboard.html"
    
    return base_url


def get_notification_actions(notification):
    """
    Obtiene las acciones disponibles para la notificación
    
    Args:
        notification: Instancia del modelo Notification
        
    Returns:
        list: Lista de acciones
    """
    actions = []
    
    # Acción básica de ver
    actions.append({
        'action': 'view',
        'title': 'Ver',
        'icon': '/static/icons/view-icon.png'
    })
    
    # Acciones específicas según el tipo
    if notification.notification_type == 'emergency':
        actions.append({
            'action': 'respond',
            'title': 'Responder',
            'icon': '/static/icons/emergency-icon.png'
        })
    elif notification.notification_type == 'alert':
        actions.append({
            'action': 'acknowledge',
            'title': 'Confirmar',
            'icon': '/static/icons/check-icon.png'
        })
    
    # Acción de cerrar
    actions.append({
        'action': 'close',
        'title': 'Cerrar',
        'icon': '/static/icons/close-icon.png'
    })
    
    return actions


def generate_vapid_keys():
    """
    Genera claves VAPID para push notifications
    
    Returns:
        tuple: (private_key, public_key)
    """
    # TODO: Implementar generación real de claves VAPID
    # from pywebpush import generate_vapid_keys
    # return generate_vapid_keys()
    
    # Por ahora retornamos claves de ejemplo
    return (
        "ejemplo_private_key_vapid",
        "ejemplo_public_key_vapid"
    )


def validate_subscription_data(data):
    """
    Valida los datos de una suscripción push
    
    Args:
        data: Diccionario con los datos de la suscripción
        
    Returns:
        bool: True si los datos son válidos
    """
    required_fields = ['endpoint', 'p256dh', 'auth']
    
    for field in required_fields:
        if field not in data or not data[field]:
            return False
    
    # Validar formato del endpoint
    if not data['endpoint'].startswith(('https://', 'http://')):
        return False
    
    return True


def cleanup_expired_subscriptions():
    """
    Limpia suscripciones expiradas o inactivas
    """
    try:
        # Eliminar suscripciones inactivas más antiguas de 30 días
        from django.utils import timezone
        from datetime import timedelta
        
        cutoff_date = timezone.now() - timedelta(days=30)
        
        deleted_count = PushSubscription.objects.filter(
            is_active=False,
            last_used__lt=cutoff_date
        ).delete()[0]
        
        logger.info(f"Limpiadas {deleted_count} suscripciones push expiradas")
        
    except Exception as e:
        logger.error(f"Error limpiando suscripciones expiradas: {e}")


def get_push_statistics():
    """
    Obtiene estadísticas de push notifications
    
    Returns:
        dict: Estadísticas
    """
    try:
        from django.db.models import Count
        
        stats = {
            'total_subscriptions': PushSubscription.objects.count(),
            'active_subscriptions': PushSubscription.objects.filter(is_active=True).count(),
            'subscriptions_by_user_type': PushSubscription.objects.filter(
                is_active=True
            ).values('user__user_type').annotate(count=Count('id')),
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error obteniendo estadísticas push: {e}")
        return {}

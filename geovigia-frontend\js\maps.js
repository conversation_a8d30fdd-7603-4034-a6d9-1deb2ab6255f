/**
 * <PERSON><PERSON><PERSON><PERSON> de mapas interactivos para GeoVigia
 * Permite al operador definir perímetros y rutas de vigilancia
 */

class GeoVigiaMaps {
    constructor() {
        this.map = null;
        this.drawControl = null;
        this.drawnItems = null;
        this.currentMode = 'view'; // 'view', 'perimeter', 'route'
        this.currentProperty = null;
        this.currentRoute = null;
        this.guards = new Map();
        this.properties = new Map();
        this.routes = new Map();

        // Configuración de colores
        this.colors = {
            perimeter: '#667eea',
            route: '#48bb78',
            guard: '#ff6b6b',
            property: '#ffa502'
        };
    }

    /**
     * Inicializa el mapa
     */
    init(containerId = 'map-container') {
        if (!document.getElementById(containerId)) {
            console.error(`Contenedor ${containerId} no encontrado`);
            return;
        }

        // Crear mapa
        this.map = L.map(containerId, {
            center: [19.4326, -99.1332], // Ciudad de México
            zoom: 13,
            zoomControl: true
        });

        // Agregar capa base
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 18
        }).addTo(this.map);

        // Inicializar herramientas de dibujo
        this.initDrawingTools();

        // Cargar datos existentes
        this.loadExistingData();

        // Configurar actualizaciones en tiempo real
        this.startRealTimeUpdates();

        // Configurar delegación de eventos global para botones de eliminar
        this.setupGlobalEventDelegation();

        console.log('Mapa inicializado correctamente');
    }

    /**
     * Inicializa las herramientas de dibujo
     */
    initDrawingTools() {
        // Crear grupo para elementos dibujados
        this.drawnItems = new L.FeatureGroup();
        this.map.addLayer(this.drawnItems);

        // Configurar herramientas de dibujo (sin marcadores - solo perímetros y rutas)
        this.drawControl = new L.Control.Draw({
            position: 'topright',
            draw: {
                polygon: {
                    allowIntersection: false,
                    drawError: {
                        color: '#e1e100',
                        message: '<strong>Error:</strong> Los bordes no pueden cruzarse!'
                    },
                    shapeOptions: {
                        color: this.colors.perimeter,
                        weight: 3,
                        opacity: 0.8,
                        fillOpacity: 0.2
                    }
                },
                polyline: {
                    shapeOptions: {
                        color: this.colors.route,
                        weight: 4,
                        opacity: 0.8
                    }
                },
                marker: false, // Deshabilitar creación de nuevas propiedades
                circle: false,
                rectangle: false,
                circlemarker: false
            },
            edit: {
                featureGroup: this.drawnItems,
                remove: true
            }
        });

        this.map.addControl(this.drawControl);

        // Event listeners para dibujo
        this.setupDrawingEvents();
    }

    /**
     * Configura los eventos de dibujo
     */
    setupDrawingEvents() {
        this.map.on(L.Draw.Event.CREATED, (e) => {
            const layer = e.layer;
            const type = e.layerType;

            // Agregar al mapa
            this.drawnItems.addLayer(layer);

            // Procesar según el tipo
            if (type === 'polygon') {
                this.handlePerimeterCreated(layer);
            } else if (type === 'polyline') {
                this.handleRouteCreated(layer);
            } else if (type === 'marker') {
                this.handlePropertyMarkerCreated(layer);
            }
        });

        this.map.on(L.Draw.Event.EDITED, (e) => {
            const layers = e.layers;
            layers.eachLayer((layer) => {
                this.handleLayerEdited(layer);
            });
        });

        this.map.on(L.Draw.Event.DELETED, (e) => {
            const layers = e.layers;
            layers.eachLayer((layer) => {
                this.handleLayerDeleted(layer);
            });
        });
    }

    /**
     * Maneja la creación de perímetros
     */
    handlePerimeterCreated(layer) {
        const coordinates = layer.getLatLngs()[0].map(latlng => [latlng.lat, latlng.lng]);

        // Mostrar modal para asociar con propiedad
        this.showPerimeterDialog(layer, coordinates);
    }

    /**
     * Maneja la creación de rutas
     */
    handleRouteCreated(layer) {
        const coordinates = layer.getLatLngs().map(latlng => [latlng.lat, latlng.lng]);

        // Mostrar modal para configurar ruta
        this.showRouteDialog(layer, coordinates);
    }

    /**
     * Maneja la creación de marcadores de propiedades
     */
    handlePropertyMarkerCreated(layer) {
        const coordinates = [layer.getLatLng().lat, layer.getLatLng().lng];

        // Mostrar modal para crear propiedad
        this.showPropertyDialog(layer, coordinates);
    }

    /**
     * Maneja la edición de capas
     */
    handleLayerEdited(layer) {
        console.log('Capa editada:', layer);
        // Aquí se implementaría la lógica de actualización
    }

    /**
     * Maneja la eliminación de capas
     */
    handleLayerDeleted(layer) {
        console.log('Capa eliminada:', layer);
        // Aquí se implementaría la lógica de eliminación
    }

    /**
     * Crea iconos personalizados
     */
    createCustomIcon(type, color = null, isMovable = false) {
        const icons = {
            property: 'fa-home',
            guard: 'fa-user-shield',
            checkpoint: 'fa-map-marker-alt',
            alert: 'fa-exclamation-triangle'
        };

        const colors = {
            property: this.colors.property,
            guard: this.colors.guard,
            checkpoint: this.colors.route,
            alert: '#ff4757'
        };

        const iconColor = color || colors[type] || '#667eea';
        const iconClass = icons[type] || 'fa-map-marker-alt';

        // Estilo especial para casas (círculo con casa adentro - MEJOR)
        if (type === 'property') {
            const cursorStyle = isMovable ? 'cursor: move;' : '';
            return L.divIcon({
                html: `
                    <div style="background-color: ${iconColor}; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; border: 3px solid white; box-shadow: 0 3px 12px rgba(0,0,0,0.4); position: relative; ${cursorStyle}" class="custom-house-icon" data-movable="${isMovable}">
                        <i class="fas ${iconClass}" style="color: white; font-size: 16px;"></i>
                        ${isMovable ? '<div class="delete-btn" style="position: absolute; top: -10px; right: -10px; background: #ff4757; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; cursor: pointer; opacity: 0; transition: opacity 0.3s ease; z-index: 1000; border: 2px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.3);"><i class="fas fa-times" style="color: white; font-size: 10px; pointer-events: none;"></i></div>' : ''}
                    </div>
                `,
                className: 'custom-house-icon',
                iconSize: [35, 35],
                iconAnchor: [17, 17]
            });
        }

        // Iconos normales para otros tipos
        const cursorStyle = isMovable ? 'cursor: move;' : '';
        return L.divIcon({
            html: `<div style="background-color: ${iconColor}; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; border: 2px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.3); ${cursorStyle}"><i class="fas ${iconClass}" style="color: white; font-size: 14px;"></i></div>`,
            className: 'custom-div-icon',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        });
    }

    /**
     * Muestra diálogo para configurar perímetro
     */
    showPerimeterDialog(layer, coordinates) {
        const modal = this.createModal('Definir Perímetro de Propiedad', `
            <div class="form-group">
                <label for="perimeter-property">Seleccionar Propiedad:</label>
                <select id="perimeter-property" class="form-control">
                    <option value="">Seleccione una propiedad...</option>
                </select>
            </div>
            <div class="form-group">
                <label for="perimeter-name">Nombre del Perímetro:</label>
                <input type="text" id="perimeter-name" class="form-control" placeholder="Ej: Perímetro Principal">
            </div>
            <div class="form-group">
                <label for="perimeter-description">Descripción:</label>
                <textarea id="perimeter-description" class="form-control" rows="3" placeholder="Descripción del perímetro..."></textarea>
            </div>
        `, [
            {
                text: 'Cancelar',
                class: 'btn-secondary',
                action: () => {
                    this.drawnItems.removeLayer(layer);
                    this.closeModal();
                }
            },
            {
                text: 'Guardar Perímetro',
                class: 'btn-primary',
                action: () => this.savePerimeter(layer, coordinates)
            }
        ]);

        // Cargar propiedades en el select
        this.loadPropertiesForSelect('perimeter-property');
    }

    /**
     * Muestra diálogo para configurar ruta
     */
    showRouteDialog(layer, coordinates) {
        const modal = this.createModal('Definir Ruta de Vigilancia', `
            <div class="form-group">
                <label for="route-name">Nombre de la Ruta:</label>
                <input type="text" id="route-name" class="form-control" placeholder="Ej: Ruta Nocturna Sector A">
            </div>
            <div class="form-group">
                <label for="route-guard">Asignar Guardia:</label>
                <select id="route-guard" class="form-control">
                    <option value="">Seleccione un guardia...</option>
                </select>
            </div>
            <div class="form-group">
                <label for="route-schedule">Horario:</label>
                <div class="row">
                    <div class="col-6">
                        <input type="time" id="route-start-time" class="form-control" placeholder="Inicio">
                    </div>
                    <div class="col-6">
                        <input type="time" id="route-end-time" class="form-control" placeholder="Fin">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="route-frequency">Frecuencia (minutos):</label>
                <input type="number" id="route-frequency" class="form-control" value="30" min="5" max="120">
            </div>
        `, [
            {
                text: 'Cancelar',
                class: 'btn-secondary',
                action: () => {
                    this.drawnItems.removeLayer(layer);
                    this.closeModal();
                }
            },
            {
                text: 'Guardar Ruta',
                class: 'btn-primary',
                action: () => this.saveRoute(layer, coordinates)
            }
        ]);

        // Cargar guardias en el select
        this.loadGuardsForSelect('route-guard');
    }

    /**
     * Muestra diálogo para crear propiedad
     */
    showPropertyDialog(layer, coordinates) {
        const modal = this.createModal('Crear Nueva Propiedad', `
            <div class="form-group">
                <label for="property-name">Nombre de la Propiedad:</label>
                <input type="text" id="property-name" class="form-control" placeholder="Ej: Casa Residencial">
            </div>
            <div class="form-group">
                <label for="property-address">Dirección:</label>
                <input type="text" id="property-address" class="form-control" placeholder="Ej: Av. Reforma 123">
            </div>
            <div class="form-group">
                <label for="property-type">Tipo de Propiedad:</label>
                <select id="property-type" class="form-control">
                    <option value="residencial">Residencial</option>
                    <option value="comercial">Comercial</option>
                    <option value="industrial">Industrial</option>
                </select>
            </div>
            <div class="form-group">
                <label for="property-description">Descripción:</label>
                <textarea id="property-description" class="form-control" rows="3" placeholder="Descripción de la propiedad..."></textarea>
            </div>
        `, [
            {
                text: 'Cancelar',
                class: 'btn-secondary',
                action: () => {
                    this.drawnItems.removeLayer(layer);
                    this.closeModal();
                }
            },
            {
                text: 'Crear Propiedad',
                class: 'btn-primary',
                action: () => this.saveProperty(layer, coordinates)
            }
        ]);
    }

    /**
     * Carga propiedades para select
     */
    async loadPropertiesForSelect(selectId) {
        try {
            const response = await GeoVigia.API.Properties.getProperties();
            const select = document.getElementById(selectId);

            // Manejar respuesta paginada o array directo
            let properties = [];
            if (Array.isArray(response)) {
                properties = response;
            } else if (response && response.results && Array.isArray(response.results)) {
                properties = response.results;
            } else {
                console.warn('Formato de propiedades no reconocido:', response);
                return;
            }

            properties.forEach(property => {
                const option = document.createElement('option');
                option.value = property.id;
                option.textContent = `${property.name} - ${property.address}`;
                select.appendChild(option);
            });
        } catch (error) {
            console.error('Error cargando propiedades:', error);
        }
    }

    /**
     * Carga guardias para select
     */
    async loadGuardsForSelect(selectId) {
        try {
            const response = await GeoVigia.API.Users.getUsers({ user_type: 'guardia' });
            const select = document.getElementById(selectId);

            // Manejar respuesta paginada o array directo
            let users = [];
            if (Array.isArray(response)) {
                users = response;
            } else if (response && response.results && Array.isArray(response.results)) {
                users = response.results;
            } else {
                console.warn('Formato de usuarios no reconocido:', response);
                // Usar datos simulados si no hay respuesta válida
                users = [
                    { id: 1, first_name: 'Miguel', last_name: 'Seguridad', username: 'guardia1' },
                    { id: 2, first_name: 'Carlos', last_name: 'Vigilante', username: 'guardia2' }
                ];
            }

            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.first_name} ${user.last_name} (${user.username})`;
                select.appendChild(option);
            });
        } catch (error) {
            console.error('Error cargando guardias:', error);
            // Usar datos simulados en caso de error
            const select = document.getElementById(selectId);
            const simulatedUsers = [
                { id: 1, first_name: 'Miguel', last_name: 'Seguridad', username: 'guardia1' },
                { id: 2, first_name: 'Carlos', last_name: 'Vigilante', username: 'guardia2' }
            ];

            simulatedUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.first_name} ${user.last_name} (${user.username})`;
                select.appendChild(option);
            });
        }
    }

    /**
     * Guarda un perímetro
     */
    async savePerimeter(layer, coordinates) {
        const propertyId = document.getElementById('perimeter-property').value;
        const name = document.getElementById('perimeter-name').value;
        const description = document.getElementById('perimeter-description').value;

        if (!propertyId || !name) {
            GeoVigia.Components.Toast.show('error', 'Error', 'Debe seleccionar una propiedad y proporcionar un nombre');
            return;
        }

        try {
            // Guardar perímetro en el backend
            const perimeterData = {
                property_id: propertyId,
                name: name,
                description: description,
                coordinates: coordinates,
                type: 'perimeter'
            };

            // Simular guardado (aquí iría la llamada real al API)
            console.log('Guardando perímetro:', perimeterData);

            // Agregar metadatos al layer
            layer.perimeterData = perimeterData;
            layer.bindPopup(`
                <div class="map-popup">
                    <h4><i class="fas fa-shield-alt"></i> ${name}</h4>
                    <p><strong>Propiedad:</strong> ${document.getElementById('perimeter-property').selectedOptions[0].text}</p>
                    <p><strong>Descripción:</strong> ${description}</p>
                    <div class="popup-actions">
                        <button onclick="geoVigiaMaps.editPerimeter('${layer._leaflet_id}')" class="btn btn-sm btn-primary">
                            <i class="fas fa-edit"></i> Editar
                        </button>
                        <button onclick="geoVigiaMaps.deletePerimeterLayer('${layer._leaflet_id}')" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash"></i> Eliminar
                        </button>
                    </div>
                </div>
            `);

            GeoVigia.Components.Toast.show('success', 'Perímetro Guardado', 'El perímetro se ha guardado correctamente');
            this.closeModal();

        } catch (error) {
            console.error('Error guardando perímetro:', error);
            GeoVigia.Components.Toast.show('error', 'Error', 'Error al guardar el perímetro');
        }
    }

    /**
     * Guarda una ruta
     */
    async saveRoute(layer, coordinates) {
        const name = document.getElementById('route-name').value;
        const guardId = document.getElementById('route-guard').value;
        const startTime = document.getElementById('route-start-time').value;
        const endTime = document.getElementById('route-end-time').value;
        const frequency = document.getElementById('route-frequency').value;

        if (!name || !guardId || !startTime || !endTime) {
            GeoVigia.Components.Toast.show('error', 'Error', 'Todos los campos son obligatorios');
            return;
        }

        try {
            // Guardar ruta en el backend
            const routeData = {
                name: name,
                guard_id: guardId,
                start_time: startTime,
                end_time: endTime,
                frequency: frequency,
                coordinates: coordinates,
                type: 'route'
            };

            // Simular guardado (aquí iría la llamada real al API)
            console.log('Guardando ruta:', routeData);

            // Agregar metadatos al layer
            layer.routeData = routeData;
            layer.bindPopup(`
                <div class="map-popup">
                    <h4><i class="fas fa-route"></i> ${name}</h4>
                    <p><strong>Guardia:</strong> ${document.getElementById('route-guard').selectedOptions[0].text}</p>
                    <p><strong>Horario:</strong> ${startTime} - ${endTime}</p>
                    <p><strong>Frecuencia:</strong> Cada ${frequency} minutos</p>
                    <div class="popup-actions">
                        <button onclick="geoVigiaMaps.editRoute('${layer._leaflet_id}')" class="btn btn-sm btn-primary">
                            <i class="fas fa-edit"></i> Editar
                        </button>
                        <button onclick="geoVigiaMaps.deleteRouteLayer('${layer._leaflet_id}')" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash"></i> Eliminar
                        </button>
                        <button onclick="geoVigiaMaps.activateRoute('${layer._leaflet_id}')" class="btn btn-sm btn-success">
                            <i class="fas fa-play"></i> Activar
                        </button>
                    </div>
                </div>
            `);

            GeoVigia.Components.Toast.show('success', 'Ruta Guardada', 'La ruta se ha guardado correctamente');
            this.closeModal();

        } catch (error) {
            console.error('Error guardando ruta:', error);
            GeoVigia.Components.Toast.show('error', 'Error', 'Error al guardar la ruta');
        }
    }

    /**
     * Guarda una propiedad
     */
    async saveProperty(layer, coordinates) {
        const name = document.getElementById('property-name').value;
        const address = document.getElementById('property-address').value;
        const type = document.getElementById('property-type').value;
        const description = document.getElementById('property-description').value;

        if (!name || !address) {
            GeoVigia.Components.Toast.show('error', 'Error', 'Nombre y dirección son obligatorios');
            return;
        }

        try {
            // Guardar propiedad en el backend
            const propertyData = {
                name: name,
                address: address,
                property_type: type,
                description: description,
                latitude: coordinates[0],
                longitude: coordinates[1]
            };

            // Simular guardado (aquí iría la llamada real al API)
            console.log('Guardando propiedad:', propertyData);

            // Agregar metadatos al layer
            layer.propertyData = propertyData;
            layer.bindPopup(`
                <div class="map-popup">
                    <h4><i class="fas fa-home"></i> ${name}</h4>
                    <p><strong>Dirección:</strong> ${address}</p>
                    <p><strong>Tipo:</strong> ${type}</p>
                    <p><strong>Descripción:</strong> ${description}</p>
                    <div class="popup-actions">
                        <button onclick="geoVigiaMaps.editProperty('${layer._leaflet_id}')" class="btn btn-sm btn-primary">
                            <i class="fas fa-edit"></i> Editar
                        </button>
                        <button onclick="geoVigiaMaps.deleteProperty('${layer._leaflet_id}')" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash"></i> Eliminar
                        </button>
                    </div>
                </div>
            `);

            GeoVigia.Components.Toast.show('success', 'Propiedad Guardada', 'La propiedad se ha guardado correctamente');
            this.closeModal();

        } catch (error) {
            console.error('Error guardando propiedad:', error);
            GeoVigia.Components.Toast.show('error', 'Error', 'Error al guardar la propiedad');
        }
    }

    /**
     * Carga datos existentes en el mapa
     */
    async loadExistingData() {
        try {
            // Cargar propiedades existentes
            await this.loadProperties();

            // Cargar rutas existentes
            await this.loadRoutes();

            // Cargar guardias activos
            await this.loadActiveGuards();

        } catch (error) {
            console.error('Error cargando datos existentes:', error);
        }
    }

    /**
     * Carga rutas existentes
     */
    async loadRoutes() {
        try {
            // Simular carga de rutas (en producción vendría del backend)
            const routes = [
                {
                    id: 1,
                    name: "Ruta Nocturna Sector A",
                    coordinates: [
                        [19.4326, -99.1332],
                        [19.4330, -99.1335],
                        [19.4325, -99.1340],
                        [19.4320, -99.1338]
                    ],
                    guard_id: 1,
                    active: true
                }
            ];

            routes.forEach(route => {
                const polyline = L.polyline(route.coordinates, {
                    color: this.colors.route,
                    weight: 4,
                    opacity: 0.8
                }).addTo(this.map);

                polyline.bindPopup(`
                    <div class="map-popup">
                        <h4><i class="fas fa-route"></i> ${route.name}</h4>
                        <p><strong>Estado:</strong> ${route.active ? 'Activa' : 'Inactiva'}</p>
                        <div class="popup-actions">
                            <button onclick="geoVigiaMaps.editRoute(${route.id})" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i> Editar
                            </button>
                        </div>
                    </div>
                `);

                this.routes.set(route.id, { polyline, data: route });
            });

        } catch (error) {
            console.error('Error cargando rutas:', error);
        }
    }

    /**
     * Carga guardias activos
     */
    async loadActiveGuards() {
        try {
            // Simular carga de guardias activos
            const guards = [
                { id: 1, name: 'Miguel Seguridad', lat: 19.4330, lng: -99.1335, status: 'on_duty' }
            ];

            guards.forEach(guard => {
                this.updateGuardMarker(guard);
            });

        } catch (error) {
            console.error('Error cargando guardias activos:', error);
        }
    }

    /**
     * Carga propiedades en el mapa
     */
    async loadProperties() {
        try {
            const response = await GeoVigia.API.Properties.getProperties();

            // Manejar respuesta paginada o array directo, con fallback a datos simulados
            let properties = [];
            if (Array.isArray(response)) {
                properties = response;
            } else if (response && response.results && Array.isArray(response.results)) {
                properties = response.results;
            } else {
                // Usar datos simulados si no hay respuesta válida
                properties = [
                    {
                        id: 1,
                        name: "Casa Residencial A",
                        address: "Av. Reforma 123",
                        property_type: "Residencial",
                        status: "Activa",
                        latitude: 19.4326,
                        longitude: -99.1332
                    },
                    {
                        id: 2,
                        name: "Casa Residencial B",
                        address: "Av. Reforma 125",
                        property_type: "Residencial",
                        status: "Activa",
                        latitude: 19.4330,
                        longitude: -99.1335
                    }
                ];
            }

            properties.forEach(property => {
                if (property.latitude && property.longitude) {
                    // Crear marcador movible con forma de casa
                    const marker = L.marker([property.latitude, property.longitude], {
                        icon: this.createCustomIcon('property', null, true),
                        draggable: true
                    }).addTo(this.map);

                    // Eventos para el marcador
                    marker.on('dragend', (e) => {
                        this.onPropertyMoved(property.id, e.target.getLatLng());
                    });

                    marker.on('click', (e) => {
                        // Prevenir que se abra el popup al hacer clic para mover
                        if (!e.target.isDragging) {
                            marker.openPopup();
                        }
                    });

                    // Agregar referencia del ID al marcador para poder eliminarlo
                    marker.propertyId = property.id;

                    // Configurar el atributo data-property-id directamente en el HTML
                    setTimeout(() => {
                        if (marker._icon) {
                            const deleteBtn = marker._icon.querySelector('.delete-btn');
                            if (deleteBtn) {
                                deleteBtn.setAttribute('data-property-id', property.id);
                                console.log('🔧 Atributo data-property-id establecido:', property.id);
                            } else {
                                console.warn('⚠️ No se encontró botón de eliminar para:', property.id);
                            }
                        }
                    }, 200);

                    marker.bindPopup(`
                        <div class="map-popup">
                            <h4><i class="fas fa-home"></i> ${property.name}</h4>
                            <p><strong>Dirección:</strong> ${property.address}</p>
                            <p><strong>Tipo:</strong> ${property.property_type}</p>
                            <p><strong>Estado:</strong> ${property.status}</p>
                            <div class="popup-actions">
                                <button onclick="geoVigiaMaps.definePerimeter(${property.id})" class="btn btn-sm btn-primary">
                                    <i class="fas fa-draw-polygon"></i> Definir Perímetro
                                </button>
                                <button onclick="geoVigiaMaps.deleteProperty(${property.id})" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i> Eliminar
                                </button>
                            </div>
                        </div>
                    `);

                    console.log('🏠 Agregando propiedad al mapa:', property.id, property.name);
                    this.properties.set(property.id, { marker, data: property });
                }
            });

        } catch (error) {
            console.error('Error cargando propiedades:', error);
        }
    }

    /**
     * Inicia actualizaciones en tiempo real
     */
    startRealTimeUpdates() {
        // Actualizar posición de guardias cada 10 segundos
        setInterval(() => {
            this.updateGuardPositions();
        }, 10000);

        // Actualizar estado general cada 30 segundos
        setInterval(() => {
            this.updateMapData();
        }, 30000);
    }

    /**
     * Actualiza posiciones de guardias
     */
    async updateGuardPositions() {
        try {
            // Simular posiciones de guardias (en producción vendría del backend)
            const guardPositions = [
                { id: 1, name: 'Miguel Seguridad', lat: 19.4330, lng: -99.1335, status: 'on_duty' },
                { id: 2, name: 'Carlos Vigilante', lat: 19.4320, lng: -99.1340, status: 'on_duty' }
            ];

            guardPositions.forEach(guard => {
                this.updateGuardMarker(guard);
            });

        } catch (error) {
            console.error('Error actualizando posiciones de guardias:', error);
        }
    }

    /**
     * Actualiza marcador de guardia
     */
    updateGuardMarker(guard) {
        // Verificar que el mapa esté inicializado
        if (!this.map) {
            console.warn('Mapa no inicializado, no se puede agregar marcador de guardia');
            return;
        }

        let guardMarker = this.guards.get(guard.id);

        if (guardMarker) {
            // Actualizar posición existente
            guardMarker.setLatLng([guard.lat, guard.lng]);
        } else {
            try {
                // Crear nuevo marcador
                guardMarker = L.marker([guard.lat, guard.lng], {
                    icon: this.createCustomIcon('guard')
                }).addTo(this.map);

                guardMarker.bindPopup(`
                    <div class="map-popup">
                        <h4><i class="fas fa-user-shield"></i> ${guard.name}</h4>
                        <p><strong>Estado:</strong> ${guard.status === 'on_duty' ? 'En Servicio' : 'Fuera de Servicio'}</p>
                        <p><strong>Última actualización:</strong> ${new Date().toLocaleTimeString()}</p>
                    </div>
                `);

                this.guards.set(guard.id, guardMarker);
            } catch (error) {
                console.error('Error creando marcador de guardia:', error);
            }
        }
    }

    /**
     * Crea modal personalizado
     */
    createModal(title, content, buttons) {
        const modal = document.createElement('div');
        modal.className = 'modal show';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="geoVigiaMaps.closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    ${buttons.map(btn => `
                        <button class="btn ${btn.class}" onclick="(${btn.action.toString()})()">${btn.text}</button>
                    `).join('')}
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        return modal;
    }

    /**
     * Cierra modal
     */
    closeModal() {
        const modal = document.querySelector('.modal.show');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * Cambia modo de dibujo
     */
    setDrawingMode(mode) {
        this.currentMode = mode;

        // Actualizar UI según el modo
        const modeButtons = document.querySelectorAll('.map-mode-btn');
        modeButtons.forEach(btn => btn.classList.remove('active'));

        const activeButton = document.querySelector(`[data-mode="${mode}"]`);
        if (activeButton) {
            activeButton.classList.add('active');
        }
    }

    /**
     * Actualiza datos generales del mapa
     */
    updateMapData() {
        try {
            // Actualizar propiedades si es necesario
            // En producción aquí se verificarían cambios en el backend
            console.log('Actualizando datos del mapa...');
        } catch (error) {
            console.error('Error actualizando datos del mapa:', error);
        }
    }

    /**
     * Centra el mapa en todas las propiedades
     */
    centerOnProperties() {
        if (this.properties.size === 0) {
            console.log('No hay propiedades para centrar');
            return;
        }

        const group = new L.featureGroup();
        this.properties.forEach(({ marker }) => {
            group.addLayer(marker);
        });

        this.map.fitBounds(group.getBounds().pad(0.1));
    }

    /**
     * Define perímetro para una propiedad específica
     */
    definePerimeter(propertyId) {
        this.setDrawingMode('perimeter');
        this.currentProperty = propertyId;

        // Mostrar mensaje informativo
        if (window.GeoVigia && window.GeoVigia.Components && window.GeoVigia.Components.Toast) {
            window.GeoVigia.Components.Toast.show(
                'info',
                'Modo Perímetro',
                'Haga clic en el mapa para definir el perímetro de la propiedad'
            );
        }
    }

    /**
     * Edita una ruta existente
     */
    editRoute(routeId) {
        const route = this.routes.get(routeId);
        if (route) {
            console.log('Editando ruta:', route.data);
            // Aquí se implementaría la lógica de edición
        }
    }

    /**
     * Edita un perímetro existente
     */
    editPerimeter(layerId) {
        console.log('Editando perímetro:', layerId);
        // Aquí se implementaría la lógica de edición
    }

    /**
     * Elimina un perímetro
     */
    deletePerimeter(layerId) {
        if (confirm('¿Está seguro de que desea eliminar este perímetro?')) {
            // Aquí se implementaría la lógica de eliminación
            console.log('Eliminando perímetro:', layerId);
        }
    }

    /**
     * Elimina una ruta
     */
    deleteRoute(layerId) {
        if (confirm('¿Está seguro de que desea eliminar esta ruta?')) {
            // Aquí se implementaría la lógica de eliminación
            console.log('Eliminando ruta:', layerId);
        }
    }

    /**
     * Elimina un perímetro por layer ID (desde el mapa)
     */
    deletePerimeterLayer(layerId) {
        if (confirm('¿Está seguro de que desea eliminar este perímetro?')) {
            // Buscar el layer en drawnItems
            this.drawnItems.eachLayer((layer) => {
                if (layer._leaflet_id == layerId) {
                    // Remover del mapa
                    this.drawnItems.removeLayer(layer);

                    // Mostrar notificación
                    if (window.GeoVigia && window.GeoVigia.Components && window.GeoVigia.Components.Toast) {
                        window.GeoVigia.Components.Toast.show('success', 'Perímetro Eliminado', 'El perímetro se ha eliminado correctamente');
                    }

                    console.log('Perímetro eliminado del mapa:', layerId);
                    return;
                }
            });
        }
    }

    /**
     * Elimina una ruta por layer ID (desde el mapa)
     */
    deleteRouteLayer(layerId) {
        if (confirm('¿Está seguro de que desea eliminar esta ruta?')) {
            // Buscar el layer en drawnItems
            this.drawnItems.eachLayer((layer) => {
                if (layer._leaflet_id == layerId) {
                    // Remover del mapa
                    this.drawnItems.removeLayer(layer);

                    // Mostrar notificación
                    if (window.GeoVigia && window.GeoVigia.Components && window.GeoVigia.Components.Toast) {
                        window.GeoVigia.Components.Toast.show('success', 'Ruta Eliminada', 'La ruta se ha eliminado correctamente');
                    }

                    console.log('Ruta eliminada del mapa:', layerId);
                    return;
                }
            });
        }
    }

    /**
     * Activa una ruta
     */
    activateRoute(layerId) {
        console.log('Activando ruta:', layerId);
        // Aquí se implementaría la lógica de activación
    }

    /**
     * Edita una propiedad
     */
    editProperty(layerId) {
        console.log('Editando propiedad:', layerId);
        // Aquí se implementaría la lógica de edición
    }

    /**
     * Elimina una propiedad
     */
    deleteProperty(propertyId) {
        console.log('🗑️ Intentando eliminar propiedad:', propertyId);
        console.log('🗑️ Propiedades disponibles:', Array.from(this.properties.keys()));

        if (confirm('¿Está seguro de que desea eliminar esta propiedad?')) {
            const propertyData = this.properties.get(propertyId);
            console.log('🗑️ Datos de propiedad encontrados:', propertyData);

            if (propertyData && propertyData.marker) {
                try {
                    // Cerrar popup si está abierto
                    if (propertyData.marker.isPopupOpen()) {
                        propertyData.marker.closePopup();
                    }

                    // Remover del mapa
                    this.map.removeLayer(propertyData.marker);

                    // Remover de la colección
                    this.properties.delete(propertyId);

                    // Aquí se haría la llamada al backend para eliminar
                    console.log('✅ Propiedad eliminada del mapa:', propertyId);

                    // Mostrar notificación
                    if (window.GeoVigia && window.GeoVigia.Components && window.GeoVigia.Components.Toast) {
                        window.GeoVigia.Components.Toast.show('success', 'Propiedad Eliminada', 'La propiedad se ha eliminado correctamente del mapa');
                    }
                } catch (error) {
                    console.error('❌ Error eliminando propiedad:', error);
                    if (window.GeoVigia && window.GeoVigia.Components && window.GeoVigia.Components.Toast) {
                        window.GeoVigia.Components.Toast.show('error', 'Error', 'No se pudo eliminar la propiedad');
                    }
                }
            } else {
                console.warn('⚠️ Propiedad no encontrada en colección:', propertyId);
                console.log('🔍 Todas las propiedades:', this.properties);

                // Intentar buscar por todos los marcadores en el mapa
                let found = false;
                this.properties.forEach((data, id) => {
                    console.log(`🔍 Comparando ${id} con ${propertyId}`);
                    if (String(id) === String(propertyId)) {
                        console.log('✅ Encontrado por string comparison');
                        this.deleteProperty(id);
                        found = true;
                        return;
                    }
                });

                if (!found) {
                    alert('No se pudo encontrar la propiedad para eliminar');
                }
            }
        }
    }

    /**
     * Maneja el movimiento de una propiedad
     */
    onPropertyMoved(propertyId, newLatLng) {
        const propertyData = this.properties.get(propertyId);
        if (propertyData) {
            // Actualizar datos
            propertyData.data.latitude = newLatLng.lat;
            propertyData.data.longitude = newLatLng.lng;

            // Aquí se haría la llamada al backend para actualizar la posición
            console.log('Propiedad movida:', {
                id: propertyId,
                newPosition: { lat: newLatLng.lat, lng: newLatLng.lng }
            });

            // Mostrar notificación
            if (window.GeoVigia && window.GeoVigia.Components && window.GeoVigia.Components.Toast) {
                window.GeoVigia.Components.Toast.show('info', 'Posición Actualizada', 'La posición de la propiedad se ha actualizado');
            }
        }
    }



    /**
     * Configura delegación de eventos global para botones de eliminar
     */
    setupGlobalEventDelegation() {
        // Agregar event listener al contenedor del mapa
        document.addEventListener('click', (e) => {
            // Verificar si el clic fue en un botón de eliminar
            if (e.target.closest('.delete-btn')) {
                e.stopPropagation();
                e.preventDefault();

                const deleteBtn = e.target.closest('.delete-btn');
                let propertyId = deleteBtn.getAttribute('data-property-id');

                console.log('🖱️ Clic global en botón eliminar, ID:', propertyId);

                // Si no hay ID en el atributo, intentar encontrarlo por el marcador
                if (!propertyId) {
                    console.log('🔍 Buscando ID por marcador...');

                    // Buscar el marcador padre
                    const markerIcon = deleteBtn.closest('.custom-house-icon');
                    if (markerIcon) {
                        // Buscar en todos los marcadores para encontrar el que coincida
                        this.properties.forEach((data, id) => {
                            if (data.marker._icon === markerIcon) {
                                propertyId = id;
                                console.log('✅ ID encontrado por marcador:', propertyId);
                                return;
                            }
                        });
                    }
                }

                if (propertyId) {
                    this.deletePropertyDirectly(propertyId);
                } else {
                    // Último recurso: eliminar por elemento DOM
                    console.log('🔧 Último recurso: eliminar por elemento DOM');
                    this.deletePropertyByElement(deleteBtn);
                }
            }
        });
    }

    /**
     * Elimina una propiedad directamente por ID
     */
    deletePropertyDirectly(propertyId) {
        console.log('🗑️ Eliminación directa de propiedad:', propertyId);

        if (confirm('¿Está seguro de que desea eliminar esta propiedad?')) {
            // Buscar la propiedad en la colección
            const propertyData = this.properties.get(propertyId);

            if (propertyData && propertyData.marker) {
                try {
                    // Remover del mapa
                    this.map.removeLayer(propertyData.marker);

                    // Remover de la colección
                    this.properties.delete(propertyId);

                    console.log('✅ Propiedad eliminada exitosamente:', propertyId);

                    // Mostrar notificación
                    if (window.GeoVigia && window.GeoVigia.Components && window.GeoVigia.Components.Toast) {
                        window.GeoVigia.Components.Toast.show('success', 'Propiedad Eliminada', 'La propiedad se ha eliminado correctamente');
                    }
                } catch (error) {
                    console.error('❌ Error eliminando propiedad:', error);
                    alert('Error al eliminar la propiedad');
                }
            } else {
                // Buscar por todos los marcadores si no se encuentra en la colección
                console.log('🔍 Buscando propiedad en todos los marcadores...');
                let found = false;

                this.properties.forEach((data, id) => {
                    if (String(id) === String(propertyId)) {
                        try {
                            this.map.removeLayer(data.marker);
                            this.properties.delete(id);
                            found = true;
                            console.log('✅ Propiedad encontrada y eliminada:', id);

                            if (window.GeoVigia && window.GeoVigia.Components && window.GeoVigia.Components.Toast) {
                                window.GeoVigia.Components.Toast.show('success', 'Propiedad Eliminada', 'La propiedad se ha eliminado correctamente');
                            }
                        } catch (error) {
                            console.error('❌ Error eliminando propiedad encontrada:', error);
                        }
                        return;
                    }
                });

                if (!found) {
                    console.error('❌ No se pudo encontrar la propiedad para eliminar:', propertyId);
                    alert('No se pudo encontrar la propiedad para eliminar');
                }
            }
        }
    }

    /**
     * Elimina una propiedad por elemento DOM (último recurso)
     */
    deletePropertyByElement(deleteBtn) {
        if (confirm('¿Está seguro de que desea eliminar esta propiedad?')) {
            try {
                // Buscar el marcador padre
                const markerIcon = deleteBtn.closest('.custom-house-icon');
                if (markerIcon) {
                    console.log('🔍 Buscando marcador por elemento DOM...');

                    // Buscar en todos los marcadores
                    let foundMarker = null;
                    let foundId = null;

                    this.properties.forEach((data, id) => {
                        if (data.marker._icon === markerIcon) {
                            foundMarker = data.marker;
                            foundId = id;
                            console.log('✅ Marcador encontrado por DOM:', id);
                            return;
                        }
                    });

                    if (foundMarker && foundId) {
                        // Eliminar del mapa
                        this.map.removeLayer(foundMarker);

                        // Eliminar de la colección
                        this.properties.delete(foundId);

                        console.log('✅ Propiedad eliminada por DOM:', foundId);

                        // Mostrar notificación
                        if (window.GeoVigia && window.GeoVigia.Components && window.GeoVigia.Components.Toast) {
                            window.GeoVigia.Components.Toast.show('success', 'Propiedad Eliminada', 'La propiedad se ha eliminado correctamente');
                        }
                    } else {
                        console.error('❌ No se pudo encontrar el marcador');
                        alert('No se pudo eliminar la propiedad');
                    }
                } else {
                    console.error('❌ No se encontró el elemento del marcador');
                    alert('No se pudo identificar la propiedad');
                }
            } catch (error) {
                console.error('❌ Error eliminando por elemento DOM:', error);
                alert('Error al eliminar la propiedad');
            }
        }
    }

    /**
     * Agrega una nueva propiedad al mapa (solo para uso interno)
     */
    addPropertyToMap(propertyData) {
        if (propertyData.latitude && propertyData.longitude) {
            const marker = L.marker([propertyData.latitude, propertyData.longitude], {
                icon: this.createCustomIcon('property', null, true),
                draggable: true
            }).addTo(this.map);

            // Eventos para el marcador
            marker.on('dragend', (e) => {
                this.onPropertyMoved(propertyData.id, e.target.getLatLng());
            });

            // Agregar event listener para el botón de eliminar
            marker.on('add', () => {
                setTimeout(() => {
                    const deleteBtn = marker._icon.querySelector('.delete-btn');
                    if (deleteBtn) {
                        deleteBtn.addEventListener('click', (e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            this.deleteProperty(propertyData.id);
                        });
                    }
                }, 100);
            });

            marker.bindPopup(`
                <div class="map-popup">
                    <h4><i class="fas fa-home"></i> ${propertyData.name}</h4>
                    <p><strong>Dirección:</strong> ${propertyData.address}</p>
                    <p><strong>Tipo:</strong> ${propertyData.property_type}</p>
                    <div class="popup-actions">
                        <button onclick="geoVigiaMaps.definePerimeter(${propertyData.id})" class="btn btn-sm btn-primary">
                            <i class="fas fa-draw-polygon"></i> Definir Perímetro
                        </button>
                        <button onclick="geoVigiaMaps.deleteProperty(${propertyData.id})" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash"></i> Eliminar
                        </button>
                    </div>
                </div>
            `);

            this.properties.set(propertyData.id, { marker, data: propertyData });
            return marker;
        }
        return null;
    }
}

// Instancia global
let geoVigiaMaps = null;

// Función para inicializar mapas cuando se navega a la página
function initializeMaps() {
    if (!geoVigiaMaps && document.getElementById('map-container')) {
        geoVigiaMaps = new GeoVigiaMaps();
        geoVigiaMaps.init('map-container');

        // Configurar event listeners para botones de modo
        document.querySelectorAll('.map-mode-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const mode = e.target.dataset.mode;
                geoVigiaMaps.setDrawingMode(mode);

                // Actualizar botones activos
                document.querySelectorAll('.map-mode-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });
    }
}

// Función para limpiar mapas cuando se sale de la página
function cleanupMaps() {
    if (geoVigiaMaps && geoVigiaMaps.map) {
        geoVigiaMaps.map.remove();
        geoVigiaMaps = null;
    }
}

// Agregar al objeto global GeoVigia para que sea accesible desde app.js
if (typeof window.GeoVigia === 'undefined') {
    window.GeoVigia = {};
}

window.GeoVigia.Maps = {
    initialize: initializeMaps,
    cleanup: cleanupMaps,
    getInstance: () => geoVigiaMaps
};

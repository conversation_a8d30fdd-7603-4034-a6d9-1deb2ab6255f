# 🔧 Cambio de Puertos del Sistema GeoVigia

## 📋 Resumen de Cambios

Se han modificado los puertos del sistema GeoVigia según la solicitud del usuario:

### **Puertos Anteriores:**
- **Backend Django**: 8000
- **Frontend Web**: 3000
- **Redis**: 6379

### **Puertos Nuevos:**
- **Backend Django**: 5021
- **Frontend Web**: 5022
- **Redis**: 5023

## 📁 Archivos Modificados

### **1. <PERSON>ript Principal**
- ✅ `run_geovigia.py`
  - Línea 36: `BACKEND_PORT = 5021`
  - L<PERSON>ea 37: `FRONTEND_PORT = 5022`

### **2. Configuración del Frontend**
- ✅ `geovigia-frontend/js/config.js`
  - Línea 7: `BASE_URL: 'http://127.0.0.1:5021/api'`

- ✅ `geovigia-frontend/js/notifications.js`
  - <PERSON><PERSON><PERSON> 59: `return \`\${protocol}//\${hostname}:5021/ws/notifications/\`;`

### **3. Configuración del Backend**
- ✅ `geovigia_backend/geovigia_backend/settings.py`
  - Líneas 157-159: CORS_ALLOWED_ORIGINS actualizado para puerto 5022
  - Líneas 167-175: CHANNEL_LAYERS actualizado para Redis puerto 5023

### **4. Documentación**
- ✅ `README.md` - URLs y puertos actualizados
- ✅ `README_SISTEMA_COMPLETO.md` - Documentación completa actualizada

## 🌐 URLs Actualizadas

### **Acceso al Sistema:**
- **🖥️ Sistema Principal**: http://localhost:5022
- **⚙️ Backend API**: http://127.0.0.1:5021
- **🔧 Admin Django**: http://127.0.0.1:5021/admin

### **WebSockets:**
- **🔌 Notificaciones**: ws://localhost:5021/ws/notifications/

## 🔑 Credenciales (Sin Cambios)

- **👨‍💼 Operador**: `operador` / `operador123`
- **👮‍♂️ Guardia**: `guardia` / `guardia123`
- **👤 Cliente**: `cliente` / `cliente123`
- **🔧 Admin Django**: `admin` / `admin123`

## 🚀 Comando de Ejecución (Sin Cambios)

```bash
python run_geovigia.py
```

## ⚠️ Servicios Externos

**¡Novedad!**: El sistema ahora **ejecuta Redis automáticamente** en el puerto **5023**. Ya no necesitas iniciar Redis manualmente.

### **Redis Automático:**
```bash
# ✅ Redis se inicia automáticamente con el script
python run_geovigia.py

# ❌ Ya NO necesitas ejecutar esto manualmente:
# redis-server
```

## 🔧 Configuración CORS

El backend ahora acepta conexiones desde:
- `http://localhost:5022`
- `http://127.0.0.1:5022`

## 📊 Puertos Utilizados por el Sistema

| Servicio | Puerto | Descripción |
|----------|--------|-------------|
| **Backend Django** | 5021 | APIs REST + WebSockets |
| **Frontend Web** | 5022 | Interfaz de usuario |
| **Redis** | 5023 | Cache y WebSockets (automático) |

## ✅ Verificación de Cambios

Para verificar que los cambios funcionan correctamente:

1. **Ejecutar el sistema:**
   ```bash
   python run_geovigia.py
   ```

2. **Verificar URLs:**
   - Frontend: http://localhost:5022
   - Backend: http://127.0.0.1:5021
   - Admin: http://127.0.0.1:5021/admin

3. **Verificar WebSockets:**
   - Abrir consola del navegador (F12)
   - Buscar mensajes de conexión WebSocket
   - Debe mostrar: "✅ WebSocket conectado"

4. **Verificar APIs:**
   - Hacer login en el sistema
   - Verificar que las APIs respondan correctamente
   - Comprobar que las notificaciones funcionen

## 🛠️ Solución de Problemas

### **Error: Puerto en uso**
```
❌ Error: Puerto 5021 ya está en uso
```
**Solución**: Detener cualquier proceso que use los puertos 5021, 5022 o 5023

### **Error: WebSocket no conecta**
```
❌ Error en WebSocket: Connection failed
```
**Solución**:
1. Verificar que el backend esté ejecutándose en puerto 5021
2. Verificar que Redis esté ejecutándose en puerto 5023 (automático)
3. Verificar configuración CORS

### **Error: APIs no responden**
```
❌ Error de conexión. Verifique su conexión a internet.
```
**Solución**:
1. Verificar que el backend esté ejecutándose
2. Verificar URL en `geovigia-frontend/js/config.js`
3. Verificar configuración CORS

## 📝 Notas Técnicas

### **Compatibilidad:**
- ✅ Todos los navegadores modernos
- ✅ Windows, Mac, Linux
- ✅ Dispositivos móviles

### **Seguridad:**
- ✅ CORS configurado correctamente
- ✅ WebSockets seguros
- ✅ Autenticación por tokens

### **Performance:**
- ✅ Sin impacto en rendimiento
- ✅ Misma funcionalidad
- ✅ Misma velocidad

## 🎯 Próximos Pasos

1. **Probar el sistema** con los nuevos puertos
2. **Verificar todas las funcionalidades**:
   - Login/logout
   - Gestión de usuarios
   - Mapas interactivos
   - Notificaciones en tiempo real
   - Analytics y reportes
3. **Actualizar cualquier documentación adicional** si es necesario

---

**✅ Cambio de puertos completado exitosamente**
*El sistema GeoVigia ahora utiliza los puertos 5021 (backend), 5022 (frontend) y 5023 (Redis automático)*

# 🗺️ Integración de Mapas en GeoVigia

## Descripción General

GeoVigia ahora incluye **integración completa con OpenStreetMap** usando la biblioteca **Leaflet.js**. Los mapas interactivos están disponibles en todas las interfaces del sistema.

## 🚀 Características Implementadas

### ✅ **Mapas Base**
- **OpenStreetMap**: Tiles de alta calidad
- **Leaflet.js**: Framework JavaScript para mapas interactivos
- **Leaflet Draw**: Herramientas de dibujo para perímetros y rutas

### ✅ **Funcionalidades Avanzadas**
- 🏠 **Marcadores de Propiedades**: Iconos personalizados con forma de casa
- 👮 **Marcadores de Guardias**: Posición en tiempo real
- 🛡️ **Perímetros de Seguridad**: Dibujo y edición de zonas
- 🛣️ **Rutas de Patrullaje**: Definición de recorridos
- 📍 **Popups Informativos**: Detalles de cada elemento
- 🎯 **Controles de Navegación**: Zoom, centrado, etc.

## 📱 Interfaces Actualizadas

### 1. **Dashboard Principal** (`index.html`)
- Sección dedicada de mapas interactivos
- Herramientas de dibujo para operadores
- Vista completa del sistema

### 2. **Cliente Dashboard** (`cliente-dashboard.html`)
- Mapa de ubicación de la propiedad del cliente
- Visualización de guardia asignado
- Leyenda informativa

### 3. **Guardia Dashboard** (`guardia-dashboard.html`)
- Mapa de recorrido asignado
- Visualización de ruta en tiempo real
- Controles de navegación

## 🛠️ Librerías Incluidas

### **CDN Links Agregados:**
```html
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" 
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
      crossorigin=""/>

<!-- Leaflet Draw CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css"/>

<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" 
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" 
        crossorigin=""></script>

<!-- Leaflet Draw JS -->
<script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>
```

## 🎨 Estilos Personalizados

### **Nuevos Estilos CSS Agregados:**
- Contenedores de mapas con bordes redondeados
- Leyendas informativas
- Controles de mapa estilizados
- Popups personalizados
- Responsive design para móviles

## 🔧 Configuración Técnica

### **Coordenadas por Defecto:**
- **Centro**: Ciudad de México (19.4326, -99.1332)
- **Zoom inicial**: 13
- **Zoom máximo**: 18
- **Zoom mínimo**: 8

### **Colores del Sistema:**
- 🏠 **Propiedades**: #ffa502 (naranja)
- 👮 **Guardias**: #ff6b6b (rojo)
- 🛡️ **Perímetros**: #667eea (azul)
- 🛣️ **Rutas**: #48bb78 (verde)

## 📋 Funciones Disponibles

### **Para Operadores (index.html):**
- `initializeMaps()`: Inicializa mapas en sección principal
- `navigateToSection('maps')`: Navega a sección de mapas
- Herramientas de dibujo completas

### **Para Clientes (cliente-dashboard.html):**
- `initializeClientMap()`: Inicializa mapa de propiedad
- Vista centrada en la propiedad del cliente
- Visualización de guardia asignado

### **Para Guardias (guardia-dashboard.html):**
- `initializeGuardMap()`: Inicializa mapa de ruta
- `centerMap()`: Centra mapa en ruta asignada
- `toggleSatellite()`: Cambia vista satelital (pendiente)

## 🚀 Cómo Usar

### **1. Iniciar el Sistema:**
```bash
python run_geovigia.py
```

### **2. Acceder a los Mapas:**
- **Operadores**: http://localhost:3000 → Sección "Mapas"
- **Clientes**: http://localhost:3000/cliente-dashboard.html
- **Guardias**: http://localhost:3000/guardia-dashboard.html

### **3. Funcionalidades por Rol:**

#### **Operadores:**
- ✏️ Dibujar perímetros de seguridad
- 🛣️ Definir rutas de patrullaje
- 🏠 Agregar nuevas propiedades
- 👁️ Monitorear guardias en tiempo real

#### **Clientes:**
- 📍 Ver ubicación de su propiedad
- 👮 Seguir guardia asignado
- 📊 Revisar estadísticas de patrullaje

#### **Guardias:**
- 🗺️ Ver ruta asignada
- 📍 Seguir recorrido
- 🎯 Centrar mapa en ubicación actual

## 🔄 Actualizaciones en Tiempo Real

Los mapas se actualizan automáticamente cada:
- **Posiciones de guardias**: 10 segundos
- **Estado general**: 30 segundos
- **Datos del sistema**: 1 minuto

## 📱 Responsive Design

Los mapas están optimizados para:
- 💻 **Desktop**: Vista completa con todas las herramientas
- 📱 **Móvil**: Controles adaptados y mapas redimensionados
- 📟 **Tablet**: Experiencia intermedia optimizada

## 🛡️ Seguridad

- ✅ **HTTPS**: Todas las librerías se cargan con integridad verificada
- ✅ **CORS**: Configurado para desarrollo local
- ✅ **Validación**: Datos de mapas validados antes de mostrar

## 🔮 Próximas Mejoras

- 🛰️ **Vista Satelital**: Integración con tiles satelitales
- 📱 **Geolocalización**: GPS en tiempo real para guardias
- 🚨 **Alertas Geográficas**: Notificaciones por ubicación
- 📊 **Heatmaps**: Mapas de calor de actividad
- 🌙 **Modo Nocturno**: Tema oscuro para mapas

## 🐛 Solución de Problemas

### **Mapa no se muestra:**
1. Verificar conexión a internet
2. Revisar consola del navegador
3. Confirmar que las librerías se cargaron

### **Marcadores no aparecen:**
1. Verificar datos en el backend
2. Revisar coordenadas válidas
3. Confirmar inicialización del mapa

### **Herramientas de dibujo no funcionan:**
1. Verificar que Leaflet Draw se cargó
2. Revisar permisos de usuario
3. Confirmar modo de dibujo activo

---

**¡Los mapas de GeoVigia están listos para usar! 🎉**

/* Reset y Variables CSS */
:root {
    /* Colores principales */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;

    /* Colores secundarios */
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #06b6d4;

    /* Colores de fondo */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #0f172a;
    --bg-sidebar: #1e293b;

    /* Colores de texto */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #94a3b8;
    --text-white: #ffffff;

    /* Bordes */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;

    /* Sombras */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Espaciado */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Bordes redondeados */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;

    /* Transiciones */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* Sidebar */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;

    /* Header */
    --header-height: 70px;
}

/* Reset básico */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    overflow-x: hidden;
}

/* Utilidades generales */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: var(--text-white);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--text-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Login Screen */
.login-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.login-container {
    background: var(--bg-primary);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 400px;
    margin: var(--spacing-md);
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.login-header .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.login-header .logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.login-header h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
}

.login-header p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Main App Layout */
.main-app {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-sidebar);
    color: var(--text-white);
    display: flex;
    flex-direction: column;
    transition: width var(--transition-normal);
    position: relative;
    z-index: 100;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: var(--header-height);
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.25rem;
    font-weight: 600;
}

.sidebar-header .logo i {
    font-size: 1.5rem;
    color: var(--primary-light);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-white);
    font-size: 1.125rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-menu {
    flex: 1;
    padding: var(--spacing-lg) 0;
    overflow-y: auto;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all var(--transition-fast);
    border-radius: 0;
    position: relative;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
}

.nav-item.active .nav-link {
    background: var(--primary-color);
    color: var(--text-white);
}

.nav-item.active .nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-light);
}

.nav-link i {
    font-size: 1.125rem;
    width: 20px;
    text-align: center;
}

.sidebar.collapsed .nav-link span {
    display: none;
}

.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.user-avatar i {
    font-size: 2rem;
    color: var(--primary-light);
}

.user-details {
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.user-name {
    font-weight: 500;
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
}

.sidebar.collapsed .user-details {
    display: none;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header */
.header {
    height: var(--header-height);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 0 var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
}

.header-left h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.breadcrumb {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.current-time {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Content Container */
.content-container {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-xl);
}

.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

/* Dashboard Styles */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--text-white);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-content p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.dashboard-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-content {
    padding: var(--spacing-lg);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -100%;
        z-index: 1000;
        transition: left var(--transition-normal);
    }

    .sidebar.open {
        left: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .header {
        padding: 0 var(--spacing-md);
    }

    .content-container {
        padding: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: var(--spacing-lg);
        margin: var(--spacing-sm);
    }

    .header-left h1 {
        font-size: 1.25rem;
    }

    .stat-card {
        padding: var(--spacing-md);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .stat-content h3 {
        font-size: 1.5rem;
    }
}

/* ===== ESTILOS ESPECÍFICOS POR ROL ===== */

/* Operador - Tema Púrpura */
.role-operador .sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.role-operador .stat-card {
    border-left: 4px solid #764ba2;
}

.role-operador .logo i {
    color: #764ba2;
}

.role-operador .nav-link.active,
.role-operador .nav-link:hover {
    background: rgba(118, 75, 162, 0.1);
    border-left: 3px solid #764ba2;
}

/* Guardia - Tema Verde */
.role-guardia .sidebar {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.role-guardia .stat-card {
    border-left: 4px solid #11998e;
}

.role-guardia .logo i {
    color: #11998e;
}

.role-guardia .nav-link.active,
.role-guardia .nav-link:hover {
    background: rgba(17, 153, 142, 0.1);
    border-left: 3px solid #11998e;
}

/* Cliente - Tema Azul */
.role-cliente .sidebar {
    background: linear-gradient(135deg, #667eea 0%, #f093fb 100%);
}

.role-cliente .stat-card {
    border-left: 4px solid #667eea;
}

.role-cliente .logo i {
    color: #667eea;
}

.role-cliente .nav-link.active,
.role-cliente .nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    border-left: 3px solid #667eea;
}

/* Personalización de botones por rol */
.role-operador .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.role-guardia .btn-primary {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.role-cliente .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #f093fb 100%);
}

/* Animaciones suaves para transiciones de rol */
.sidebar, .stat-card, .logo i, .nav-link {
    transition: all var(--transition-normal);
}

/* ===== MAPS PAGE STYLES ===== */

.maps-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.maps-title h2 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.maps-title p {
    margin: 0;
    color: var(--text-secondary);
}

.maps-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.map-mode-buttons {
    display: flex;
    gap: 0.5rem;
}

.map-mode-btn {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    background: white;
    color: var(--text-secondary);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.map-mode-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.map-mode-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.maps-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
}

.map-container-wrapper {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.map-sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.map-legend, .map-instructions {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.map-legend h4, .map-instructions h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.legend-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.legend-icon.property {
    background: #ffa502;
}

.legend-icon.guard {
    background: #ff6b6b;
}

.legend-line {
    width: 30px;
    height: 4px;
    border-radius: 2px;
}

.legend-line.perimeter {
    background: #667eea;
}

.legend-line.route {
    background: #48bb78;
}

.instruction-steps {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.step {
    padding: 0.75rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    font-size: 0.875rem;
    line-height: 1.4;
}

/* Estilos para marcadores de casa */
.custom-house-icon {
    position: relative;
}

.custom-house-icon:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

/* Botón de eliminar en marcadores */
.custom-house-icon .delete-btn {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #ff4757;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1001 !important;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    pointer-events: auto !important;
}

.custom-house-icon:hover .delete-btn {
    opacity: 1;
    transform: scale(1.1);
    pointer-events: auto !important;
}

.custom-house-icon .delete-btn:hover {
    background: #ff3742 !important;
    transform: scale(1.2) !important;
    opacity: 1 !important;
}

/* Asegurar que el marcador permita eventos en el botón */
.custom-house-icon[data-movable="true"] {
    pointer-events: auto;
}

.custom-house-icon[data-movable="true"] .delete-btn {
    pointer-events: auto;
}

/* Cursor para elementos movibles */
.leaflet-marker-draggable {
    cursor: move !important;
}

/* Animación para marcadores movidos */
.property-moved {
    animation: propertyMoved 0.5s ease;
}

@keyframes propertyMoved {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Estilos para notificaciones de mapa */
.map-notification {
    position: fixed;
    top: 80px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    max-width: 300px;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.map-notification.success {
    border-left: 4px solid #48bb78;
}

.map-notification.info {
    border-left: 4px solid #3182ce;
}

.map-notification.warning {
    border-left: 4px solid #ffa502;
}

/* Responsive para mapas */
@media (max-width: 768px) {
    .maps-content {
        grid-template-columns: 1fr;
    }

    .maps-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .maps-controls {
        justify-content: center;
    }

    .custom-house-icon {
        transform: scale(0.9);
    }

    .map-notification {
        right: 10px;
        max-width: calc(100vw - 20px);
    }
}

/* ===== ESTILOS ADICIONALES PARA MAPAS ===== */

/* Contenedor principal del mapa */
.map-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

/* Mapa de propiedad en cliente dashboard */
.property-map {
    margin: 2rem 0;
}

.property-map h2 {
    color: #333;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.property-map-view {
    border-radius: 8px 8px 0 0;
}

/* Información del mapa y leyenda */
.map-info {
    padding: 1rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.map-legend {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Estilos para el mapa de rutas en guardia dashboard */
.route-map {
    border-radius: 8px 8px 0 0;
}

.map-controls {
    padding: 1rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.map-btn {
    padding: 0.5rem 1rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.map-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.map-btn:active {
    transform: translateY(0);
}

/* Asegurar que los mapas de Leaflet se muestren correctamente */
.leaflet-container {
    font-family: 'Inter', sans-serif;
}

/* Estilos para popups de Leaflet */
.leaflet-popup-content-wrapper {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.leaflet-popup-content {
    margin: 0;
    padding: 0;
}

.map-popup {
    padding: 1rem;
    min-width: 200px;
}

.map-popup h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.map-popup p {
    margin: 0.25rem 0;
    color: #666;
    font-size: 0.9rem;
}

.popup-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.popup-actions .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    border-radius: 4px;
}

/* Responsive para mapas */
@media (max-width: 768px) {
    .map-legend {
        flex-direction: column;
        gap: 0.75rem;
    }

    .map-controls {
        justify-content: center;
    }

    .map-btn {
        flex: 1;
        justify-content: center;
        min-width: 120px;
    }

    .property-map-view,
    .route-map {
        height: 250px !important;
    }
}

/* ===== ESTILOS PARA NOTIFICACIONES ===== */

/* Dashboard de Notificaciones */
.notifications-dashboard {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.notifications-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon { background: #667eea; }
.stat-card:nth-child(2) .stat-icon { background: #f093fb; }
.stat-card:nth-child(3) .stat-icon { background: #ffeaa7; color: #333; }
.stat-card:nth-child(4) .stat-icon { background: #55a3ff; }

.stat-content h3 {
    margin: 0 0 0.5rem 0;
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

/* Filtros de Notificaciones */
.notifications-filters {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.filter-group select {
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: #667eea;
}

/* Lista de Notificaciones */
.notifications-list-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.notifications-list {
    max-height: 600px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #f0f8ff;
    border-left: 4px solid #667eea;
}

.notification-item.critical {
    border-left: 4px solid #ff4757;
}

.notification-item.high {
    border-left: 4px solid #ffa502;
}

.notification-item .notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f1f3f4;
    color: #666;
    flex-shrink: 0;
}

.notification-item.unread .notification-icon {
    background: #667eea;
    color: white;
}

.notification-content {
    flex: 1;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.notification-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #333;
}

.notification-time {
    font-size: 0.8rem;
    color: #999;
    white-space: nowrap;
}

.notification-message {
    margin: 0 0 0.5rem 0;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

.notification-sender {
    color: #999;
    font-size: 0.8rem;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.btn-mark-read,
.btn-delete {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.btn-mark-read {
    background: #48bb78;
    color: white;
}

.btn-mark-read:hover {
    background: #38a169;
}

.btn-delete {
    background: #f56565;
    color: white;
}

.btn-delete:hover {
    background: #e53e3e;
}

.no-notifications {
    text-align: center;
    padding: 3rem;
    color: #999;
    font-style: italic;
}

.notifications-pagination {
    padding: 1rem;
    text-align: center;
    border-top: 1px solid #f1f3f4;
}

/* Controles de Notificaciones */
.notifications-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Estados de Conexión */
.connection-status {
    position: fixed;
    bottom: 20px;
    left: 20px;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.connection-status.disconnected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.connection-status.connecting {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Responsive para notificaciones */
@media (max-width: 768px) {
    .notifications-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .notifications-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .notification-item {
        padding: 1rem;
    }

    .notification-header {
        flex-direction: column;
        gap: 0.5rem;
    }

    .notification-actions {
        margin-top: 0.5rem;
    }

    .notifications-controls {
        flex-direction: column;
        align-items: stretch;
    }
}

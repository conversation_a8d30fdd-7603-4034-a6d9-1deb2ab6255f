from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Q
from django.shortcuts import get_object_or_404
from django.utils import timezone

from .models import Route, RouteProperty, GuardRoute, RouteCheckpoint, RouteExecution
from .serializers import (
    RouteSerializer, RouteDetailSerializer, RouteCreateSerializer,
    RoutePropertySerializer, GuardRouteSerializer, RouteCheckpointSerializer,
    RouteExecutionSerializer
)
from users.models import CustomUser


class IsOperadorOrGuardiaOwn(permissions.BasePermission):
    """
    Permiso personalizado que permite a los operadores gestionar todo
    y a los guardias ver solo sus rutas asignadas
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and (
            request.user.is_operador or request.user.is_guardia
        )

    def has_object_permission(self, request, view, obj):
        # Los operadores pueden gestionar todo
        if request.user.is_operador:
            return True

        # Los guardias solo pueden ver sus rutas asignadas
        if request.user.is_guardia:
            if isinstance(obj, Route):
                return obj.assigned_guards.filter(
                    guard=request.user,
                    status='activa'
                ).exists()
            elif isinstance(obj, GuardRoute):
                return obj.guard == request.user
            elif isinstance(obj, RouteExecution):
                return obj.guard == request.user
            elif hasattr(obj, 'route'):
                return obj.route.assigned_guards.filter(
                    guard=request.user,
                    status='activa'
                ).exists()

        return False


class RouteListCreateView(generics.ListCreateAPIView):
    """
    Vista para listar y crear rutas
    """
    permission_classes = [IsOperadorOrGuardiaOwn]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return RouteCreateSerializer
        return RouteSerializer

    def get_queryset(self):
        user = self.request.user

        if user.is_operador:
            # Los operadores pueden ver todas las rutas
            return Route.objects.all()
        elif user.is_guardia:
            # Los guardias solo ven sus rutas asignadas
            return Route.objects.filter(
                assigned_guards__guard=user,
                assigned_guards__status='activa'
            ).distinct()

        return Route.objects.none()

    def perform_create(self, serializer):
        # Solo los operadores pueden crear rutas
        if not self.request.user.is_operador:
            raise permissions.PermissionDenied(
                "Solo los operadores pueden crear rutas"
            )
        serializer.save()


class RouteDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Vista para ver, actualizar o eliminar una ruta específica
    """
    serializer_class = RouteDetailSerializer
    permission_classes = [IsOperadorOrGuardiaOwn]
    queryset = Route.objects.all()

    def perform_update(self, serializer):
        # Solo los operadores pueden actualizar rutas
        if not self.request.user.is_operador:
            raise permissions.PermissionDenied(
                "Solo los operadores pueden actualizar rutas"
            )
        serializer.save()

    def perform_destroy(self, instance):
        # Solo los operadores pueden eliminar rutas
        if not self.request.user.is_operador:
            raise permissions.PermissionDenied(
                "Solo los operadores pueden eliminar rutas"
            )
        instance.delete()


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def my_routes(request):
    """
    Vista para que los guardias obtengan sus rutas asignadas
    """
    if not request.user.is_guardia:
        return Response({
            'error': 'Solo los guardias pueden acceder a esta función'
        }, status=status.HTTP_403_FORBIDDEN)

    routes = Route.objects.filter(
        assigned_guards__guard=request.user,
        assigned_guards__status='activa'
    ).distinct()

    serializer = RouteSerializer(routes, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def active_routes(request):
    """
    Vista para obtener rutas activas
    """
    user = request.user

    if user.is_operador:
        routes = Route.objects.filter(status='activa')
    elif user.is_guardia:
        routes = Route.objects.filter(
            status='activa',
            assigned_guards__guard=user,
            assigned_guards__status='activa'
        ).distinct()
    else:
        return Response({
            'error': 'No autorizado'
        }, status=status.HTTP_403_FORBIDDEN)

    serializer = RouteSerializer(routes, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def routes_today(request):
    """
    Vista para obtener rutas activas para hoy
    """
    user = request.user

    if user.is_operador:
        routes = Route.objects.filter(status='activa')
    elif user.is_guardia:
        routes = Route.objects.filter(
            status='activa',
            assigned_guards__guard=user,
            assigned_guards__status='activa'
        ).distinct()
    else:
        return Response({
            'error': 'No autorizado'
        }, status=status.HTTP_403_FORBIDDEN)

    # Filtrar rutas activas para hoy
    today_routes = [route for route in routes if route.is_active_today()]

    serializer = RouteSerializer(today_routes, many=True)
    return Response(serializer.data)


# Vistas para gestión de asignaciones de guardias
class GuardRouteListCreateView(generics.ListCreateAPIView):
    """
    Vista para listar y crear asignaciones de guardias a rutas
    """
    serializer_class = GuardRouteSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        if user.is_operador:
            return GuardRoute.objects.all()
        elif user.is_guardia:
            return GuardRoute.objects.filter(guard=user)

        return GuardRoute.objects.none()

    def perform_create(self, serializer):
        # Solo los operadores pueden crear asignaciones
        if not self.request.user.is_operador:
            raise permissions.PermissionDenied(
                "Solo los operadores pueden crear asignaciones"
            )
        serializer.save(assigned_by=self.request.user)


class GuardRouteDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Vista para gestionar asignaciones específicas de guardias
    """
    serializer_class = GuardRouteSerializer
    permission_classes = [IsOperadorOrGuardiaOwn]
    queryset = GuardRoute.objects.all()


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def assign_guard_to_route(request, route_id):
    """
    Vista para asignar un guardia a una ruta específica
    """
    if not request.user.is_operador:
        return Response({
            'error': 'Solo los operadores pueden asignar guardias'
        }, status=status.HTTP_403_FORBIDDEN)

    route = get_object_or_404(Route, id=route_id)
    guard_id = request.data.get('guard_id')
    start_date = request.data.get('start_date')
    end_date = request.data.get('end_date')
    is_primary = request.data.get('is_primary_guard', False)

    if not guard_id or not start_date:
        return Response({
            'error': 'Se requieren guard_id y start_date'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        guard = CustomUser.objects.get(id=guard_id, user_type='guardia')
    except CustomUser.DoesNotExist:
        return Response({
            'error': 'Guardia no encontrado'
        }, status=status.HTTP_404_NOT_FOUND)

    # Verificar si ya existe una asignación activa
    existing = GuardRoute.objects.filter(
        guard=guard,
        route=route,
        status='activa'
    ).exists()

    if existing:
        return Response({
            'error': 'El guardia ya está asignado a esta ruta'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Convertir fechas de string a objetos date si es necesario
    from datetime import datetime
    if isinstance(start_date, str):
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
    if isinstance(end_date, str):
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    assignment = GuardRoute.objects.create(
        guard=guard,
        route=route,
        start_date=start_date,
        end_date=end_date,
        is_primary_guard=is_primary,
        assigned_by=request.user
    )

    serializer = GuardRouteSerializer(assignment)
    return Response({
        'message': 'Guardia asignado exitosamente',
        'assignment': serializer.data
    }, status=status.HTTP_201_CREATED)


# Vistas para gestión de puntos de control
class RouteCheckpointListCreateView(generics.ListCreateAPIView):
    """
    Vista para listar y crear puntos de control de rutas
    """
    serializer_class = RouteCheckpointSerializer
    permission_classes = [IsOperadorOrGuardiaOwn]

    def get_queryset(self):
        user = self.request.user

        if user.is_operador:
            return RouteCheckpoint.objects.all()
        elif user.is_guardia:
            return RouteCheckpoint.objects.filter(
                route__assigned_guards__guard=user,
                route__assigned_guards__status='activa'
            ).distinct()

        return RouteCheckpoint.objects.none()

    def perform_create(self, serializer):
        # Solo los operadores pueden crear puntos de control
        if not self.request.user.is_operador:
            raise permissions.PermissionDenied(
                "Solo los operadores pueden crear puntos de control"
            )
        serializer.save()


class RouteCheckpointDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Vista para gestionar puntos de control específicos
    """
    serializer_class = RouteCheckpointSerializer
    permission_classes = [IsOperadorOrGuardiaOwn]
    queryset = RouteCheckpoint.objects.all()


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def route_checkpoints(request, route_id):
    """
    Vista para obtener puntos de control de una ruta específica
    """
    route = get_object_or_404(Route, id=route_id)

    # Verificar permisos
    user = request.user
    if not user.is_operador:
        if user.is_guardia:
            if not route.assigned_guards.filter(
                guard=user, status='activa'
            ).exists():
                return Response({
                    'error': 'No tienes permisos para ver esta ruta'
                }, status=status.HTTP_403_FORBIDDEN)
        else:
            return Response({
                'error': 'No autorizado'
            }, status=status.HTTP_403_FORBIDDEN)

    checkpoints = route.checkpoints.all().order_by('order')
    serializer = RouteCheckpointSerializer(checkpoints, many=True)
    return Response(serializer.data)


# Vistas para ejecución de rutas
class RouteExecutionListCreateView(generics.ListCreateAPIView):
    """
    Vista para listar y crear ejecuciones de rutas
    """
    serializer_class = RouteExecutionSerializer
    permission_classes = [IsOperadorOrGuardiaOwn]

    def get_queryset(self):
        user = self.request.user

        if user.is_operador:
            return RouteExecution.objects.all()
        elif user.is_guardia:
            return RouteExecution.objects.filter(guard=user)

        return RouteExecution.objects.none()

    def perform_create(self, serializer):
        # Los guardias pueden crear sus propias ejecuciones
        if self.request.user.is_guardia:
            serializer.save(guard=self.request.user)
        elif self.request.user.is_operador:
            serializer.save()
        else:
            raise permissions.PermissionDenied(
                "No tienes permisos para crear ejecuciones de rutas"
            )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def start_route_execution(request, route_id):
    """
    Vista para que un guardia inicie la ejecución de una ruta
    """
    if not request.user.is_guardia:
        return Response({
            'error': 'Solo los guardias pueden iniciar ejecuciones de rutas'
        }, status=status.HTTP_403_FORBIDDEN)

    route = get_object_or_404(Route, id=route_id)

    # Verificar que el guardia esté asignado a la ruta
    if not route.assigned_guards.filter(
        guard=request.user, status='activa'
    ).exists():
        return Response({
            'error': 'No estás asignado a esta ruta'
        }, status=status.HTTP_403_FORBIDDEN)

    # Verificar que no haya una ejecución activa
    active_execution = RouteExecution.objects.filter(
        guard=request.user,
        status__in=['iniciada', 'en_progreso']
    ).first()

    if active_execution:
        return Response({
            'error': 'Ya tienes una ejecución de ruta activa'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Crear nueva ejecución
    now = timezone.now()
    planned_duration = timezone.timedelta(minutes=route.estimated_duration_minutes)

    execution = RouteExecution.objects.create(
        route=route,
        guard=request.user,
        start_time=now,
        planned_start_time=now,
        planned_end_time=now + planned_duration,
        checkpoints_total=route.get_checkpoint_count(),
        properties_total=route.get_total_properties()
    )

    serializer = RouteExecutionSerializer(execution)
    return Response({
        'message': 'Ejecución de ruta iniciada',
        'execution': serializer.data
    }, status=status.HTTP_201_CREATED)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def complete_route_execution(request, execution_id):
    """
    Vista para completar una ejecución de ruta
    """
    execution = get_object_or_404(RouteExecution, id=execution_id)

    # Verificar permisos
    if not request.user.is_guardia or execution.guard != request.user:
        return Response({
            'error': 'No tienes permisos para completar esta ejecución'
        }, status=status.HTTP_403_FORBIDDEN)

    if execution.status not in ['iniciada', 'en_progreso']:
        return Response({
            'error': 'Esta ejecución no puede ser completada'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Actualizar ejecución
    execution.end_time = timezone.now()
    execution.status = 'completada'
    execution.notes = request.data.get('notes', execution.notes)
    execution.incidents_reported = request.data.get('incidents_reported', 0)
    execution.save()

    serializer = RouteExecutionSerializer(execution)
    return Response({
        'message': 'Ejecución completada exitosamente',
        'execution': serializer.data
    })

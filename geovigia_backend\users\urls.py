from django.urls import path
from . import views

app_name = 'users'

urlpatterns = [
    # Autenticación
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('register/', views.register_user, name='register'),
    
    # Perfil de usuario
    path('profile/', views.user_profile, name='user_profile'),
    path('profile/update/', views.update_profile, name='update_profile'),
    
    # Gestión de usuarios (para operadores)
    path('list/', views.UserListView.as_view(), name='user_list'),
    path('<int:pk>/', views.UserDetailView.as_view(), name='user_detail'),
    
    # Funciones específicas para guardias
    path('guardia/location/', views.update_guardia_location, name='update_location'),
    path('guardia/duty/', views.toggle_duty_status, name='toggle_duty'),
]

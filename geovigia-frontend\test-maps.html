<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Mapas GeoVigia</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" 
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
          crossorigin=""/>
    
    <!-- Leaflet Draw CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css"/>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2rem;
        }
        
        .header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .test-section {
            margin-bottom: 3rem;
        }
        
        .test-section h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .map-container {
            height: 400px;
            width: 100%;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .controls {
            margin: 1rem 0;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .status {
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 6px;
            margin: 1rem 0;
            border-left: 4px solid #667eea;
        }
        
        .status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        
        .info-card h4 {
            margin: 0 0 0.5rem 0;
            color: #667eea;
        }
        
        .info-card p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-map"></i> Test de Mapas GeoVigia</h1>
            <p>Verificación de integración con OpenStreetMap y Leaflet.js</p>
        </div>
        
        <div class="content">
            <!-- Test 1: Mapa Básico -->
            <div class="test-section">
                <h2>🗺️ Test 1: Mapa Básico de OpenStreetMap</h2>
                <div id="basic-map" class="map-container"></div>
                <div class="controls">
                    <button class="btn" onclick="initBasicMap()">
                        <i class="fas fa-play"></i> Inicializar Mapa
                    </button>
                    <button class="btn" onclick="addTestMarker()">
                        <i class="fas fa-map-marker-alt"></i> Agregar Marcador
                    </button>
                </div>
                <div id="basic-status" class="status">
                    Presiona "Inicializar Mapa" para comenzar el test
                </div>
            </div>
            
            <!-- Test 2: Mapa con Herramientas de Dibujo -->
            <div class="test-section">
                <h2>✏️ Test 2: Herramientas de Dibujo</h2>
                <div id="draw-map" class="map-container"></div>
                <div class="controls">
                    <button class="btn" onclick="initDrawMap()">
                        <i class="fas fa-pencil-alt"></i> Inicializar con Dibujo
                    </button>
                    <button class="btn" onclick="clearDrawings()">
                        <i class="fas fa-trash"></i> Limpiar Dibujos
                    </button>
                </div>
                <div id="draw-status" class="status">
                    Presiona "Inicializar con Dibujo" para probar las herramientas
                </div>
            </div>
            
            <!-- Test 3: Integración GeoVigia -->
            <div class="test-section">
                <h2>🏠 Test 3: Integración GeoVigia</h2>
                <div id="geovigia-map" class="map-container"></div>
                <div class="controls">
                    <button class="btn" onclick="initGeoVigiaMap()">
                        <i class="fas fa-shield-alt"></i> Inicializar GeoVigia
                    </button>
                    <button class="btn" onclick="loadTestData()">
                        <i class="fas fa-database"></i> Cargar Datos de Prueba
                    </button>
                </div>
                <div id="geovigia-status" class="status">
                    Presiona "Inicializar GeoVigia" para probar la integración completa
                </div>
            </div>
            
            <!-- Información del Sistema -->
            <div class="test-section">
                <h2>📊 Información del Sistema</h2>
                <div class="info-grid">
                    <div class="info-card">
                        <h4><i class="fas fa-globe"></i> OpenStreetMap</h4>
                        <p>Tiles servidos desde: https://tile.openstreetmap.org/</p>
                    </div>
                    <div class="info-card">
                        <h4><i class="fas fa-code"></i> Leaflet.js</h4>
                        <p>Versión: 1.9.4 (CDN)</p>
                    </div>
                    <div class="info-card">
                        <h4><i class="fas fa-pencil-alt"></i> Leaflet Draw</h4>
                        <p>Versión: 1.0.4 (CDN)</p>
                    </div>
                    <div class="info-card">
                        <h4><i class="fas fa-map-marker-alt"></i> Coordenadas</h4>
                        <p>Centro: Ciudad de México (19.4326, -99.1332)</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" 
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" 
            crossorigin=""></script>
    
    <!-- Leaflet Draw JS -->
    <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>
    
    <!-- GeoVigia Scripts -->
    <script src="js/config.js"></script>
    <script src="js/components.js"></script>
    <script src="js/maps.js"></script>
    
    <script>
        let basicMap = null;
        let drawMap = null;
        let geoVigiaMap = null;
        let drawnItems = null;

        // Test 1: Mapa Básico
        function initBasicMap() {
            try {
                if (basicMap) {
                    basicMap.remove();
                }
                
                basicMap = L.map('basic-map').setView([19.4326, -99.1332], 13);
                
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 18
                }).addTo(basicMap);
                
                updateStatus('basic-status', 'success', '✅ Mapa básico inicializado correctamente');
            } catch (error) {
                updateStatus('basic-status', 'error', '❌ Error: ' + error.message);
            }
        }

        function addTestMarker() {
            if (!basicMap) {
                updateStatus('basic-status', 'error', '❌ Primero inicializa el mapa');
                return;
            }
            
            const marker = L.marker([19.4326, -99.1332]).addTo(basicMap);
            marker.bindPopup('<b>¡Hola!</b><br>Este es un marcador de prueba.').openPopup();
            updateStatus('basic-status', 'success', '✅ Marcador agregado correctamente');
        }

        // Test 2: Mapa con Dibujo
        function initDrawMap() {
            try {
                if (drawMap) {
                    drawMap.remove();
                }
                
                drawMap = L.map('draw-map').setView([19.4326, -99.1332], 13);
                
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 18
                }).addTo(drawMap);
                
                // Agregar herramientas de dibujo
                drawnItems = new L.FeatureGroup();
                drawMap.addLayer(drawnItems);
                
                const drawControl = new L.Control.Draw({
                    edit: {
                        featureGroup: drawnItems
                    }
                });
                drawMap.addControl(drawControl);
                
                updateStatus('draw-status', 'success', '✅ Mapa con herramientas de dibujo inicializado');
            } catch (error) {
                updateStatus('draw-status', 'error', '❌ Error: ' + error.message);
            }
        }

        function clearDrawings() {
            if (drawnItems) {
                drawnItems.clearLayers();
                updateStatus('draw-status', 'success', '✅ Dibujos limpiados');
            }
        }

        // Test 3: GeoVigia
        function initGeoVigiaMap() {
            try {
                if (typeof GeoVigiaMaps !== 'undefined') {
                    geoVigiaMap = new GeoVigiaMaps();
                    geoVigiaMap.init('geovigia-map');
                    updateStatus('geovigia-status', 'success', '✅ Mapa GeoVigia inicializado correctamente');
                } else {
                    updateStatus('geovigia-status', 'error', '❌ Clase GeoVigiaMaps no encontrada');
                }
            } catch (error) {
                updateStatus('geovigia-status', 'error', '❌ Error: ' + error.message);
            }
        }

        function loadTestData() {
            if (!geoVigiaMap) {
                updateStatus('geovigia-status', 'error', '❌ Primero inicializa el mapa GeoVigia');
                return;
            }
            
            // Simular carga de datos
            setTimeout(() => {
                updateStatus('geovigia-status', 'success', '✅ Datos de prueba cargados (simulado)');
            }, 1000);
        }

        function updateStatus(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
        }

        // Inicialización automática
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Página de test de mapas cargada');
            
            // Verificar que Leaflet esté disponible
            if (typeof L !== 'undefined') {
                console.log('✅ Leaflet.js cargado correctamente');
            } else {
                console.error('❌ Leaflet.js no está disponible');
            }
        });
    </script>
</body>
</html>

#!/usr/bin/env python3
"""
Script para crear usuarios principales del sistema GeoVigia
Crea los usuarios: operador, guardia, cliente, admin
"""

import os
import sys
import django

# Configurar Django
sys.path.append('geovigia_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'geovigia_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import make_password
from users.models import OperadorProfile, GuardiaProfile, ClienteProfile

User = get_user_model()

def create_main_users():
    """Crear usuarios principales del sistema"""
    print("👥 Creando usuarios principales del sistema GeoVigia...")
    
    # 1. ADMIN (Superusuario)
    admin, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Administrador',
            'last_name': '<PERSON>stema',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True,
            'password': make_password('admin123')
        }
    )
    if created:
        print(f"✅ Superusuario creado: {admin.username}")
    else:
        print(f"ℹ️ Superusuario ya existe: {admin.username}")
    
    # 2. OPERADOR
    operador, created = User.objects.get_or_create(
        username='operador',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Carlos',
            'last_name': 'Operador',
            'user_type': 'operador',
            'is_active': True,
            'password': make_password('operador123')
        }
    )
    
    if created:
        OperadorProfile.objects.get_or_create(
            user=operador,
            defaults={
                'employee_id': 'OP001',
                'department': 'Operaciones',
                'access_level': 'full'
            }
        )
        print(f"✅ Usuario operador creado: {operador.username}")
    else:
        print(f"ℹ️ Usuario operador ya existe: {operador.username}")
    
    # 3. GUARDIA
    guardia, created = User.objects.get_or_create(
        username='guardia',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Miguel',
            'last_name': 'Guardia',
            'user_type': 'guardia',
            'is_active': True,
            'password': make_password('guardia123')
        }
    )
    
    if created:
        GuardiaProfile.objects.get_or_create(
            user=guardia,
            defaults={
                'badge_number': 'G001',
                'shift': 'diurno',
                'phone': '+52 55 1234 5678',
                'emergency_contact': '+52 55 8765 4321',
                'is_available': True,
                'is_on_duty': True,
                'current_latitude': 19.4326,
                'current_longitude': -99.1332
            }
        )
        print(f"✅ Usuario guardia creado: {guardia.username}")
    else:
        print(f"ℹ️ Usuario guardia ya existe: {guardia.username}")
    
    # 4. CLIENTE
    cliente, created = User.objects.get_or_create(
        username='cliente',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'María',
            'last_name': 'Cliente',
            'user_type': 'cliente',
            'is_active': True,
            'password': make_password('cliente123')
        }
    )
    
    if created:
        ClienteProfile.objects.get_or_create(
            user=cliente,
            defaults={
                'company_name': 'Empresa Cliente S.A.',
                'contact_phone': '+52 55 9876 5432',
                'address': 'Av. Principal 123, CDMX',
                'subscription_type': 'premium',
                'is_verified': True
            }
        )
        print(f"✅ Usuario cliente creado: {cliente.username}")
    else:
        print(f"ℹ️ Usuario cliente ya existe: {cliente.username}")

def verify_users():
    """Verificar que todos los usuarios estén creados correctamente"""
    print("\n🔍 Verificando usuarios creados...")
    
    users = User.objects.all()
    print(f"\n=== USUARIOS EN EL SISTEMA ({users.count()}) ===")
    
    for user in users:
        user_type = getattr(user, 'user_type', 'admin' if user.is_superuser else 'No definido')
        status = "✅ Activo" if user.is_active else "❌ Inactivo"
        print(f"👤 {user.username} | {user.first_name} {user.last_name} | Tipo: {user_type} | {status}")
    
    # Verificar usuarios específicos
    required_users = ['admin', 'operador', 'guardia', 'cliente']
    print(f"\n=== VERIFICACIÓN DE USUARIOS REQUERIDOS ===")
    
    for username in required_users:
        try:
            user = User.objects.get(username=username)
            print(f"✅ {username}: OK - {user.first_name} {user.last_name}")
        except User.DoesNotExist:
            print(f"❌ {username}: NO ENCONTRADO")

def main():
    """Función principal"""
    print("🚀 Iniciando creación de usuarios principales de GeoVigia...")
    print("=" * 60)
    
    try:
        # Crear usuarios principales
        create_main_users()
        
        # Verificar usuarios
        verify_users()
        
        print("\n" + "=" * 60)
        print("✅ ¡Usuarios principales creados/verificados exitosamente!")
        print("\n🔑 Credenciales de acceso:")
        print("   🔧 Admin: admin / admin123")
        print("   👨‍💼 Operador: operador / operador123")
        print("   👮‍♂️ Guardia: guardia / guardia123")
        print("   👤 Cliente: cliente / cliente123")
        print("\n🌐 Accede al sistema en:")
        print("   📱 Sistema Principal: http://localhost:5022")
        print("   🔧 Admin Django: http://127.0.0.1:5021/admin")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error creando usuarios: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()

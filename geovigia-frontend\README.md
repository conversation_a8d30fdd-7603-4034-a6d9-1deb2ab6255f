# GeoVigia Frontend - Interfaz Web para Operadores

## Descripción General

El **Frontend de GeoVigia** es una aplicación web moderna diseñada específicamente para operadores del sistema de vigilancia. Proporciona una interfaz intuitiva y completa para gestionar usuarios, propiedades, rutas y monitorear el sistema en tiempo real.

## Características Principales

### 🎨 Diseño Moderno y Responsivo
- **Interfaz limpia** con diseño Material Design
- **Totalmente responsivo** para desktop, tablet y móvil
- **Tema oscuro/claro** (configuración futura)
- **Animaciones suaves** y transiciones fluidas
- **Iconos Font Awesome** para mejor UX

### 🔐 Sistema de Autenticación
- **Login seguro** con validación de credenciales
- **Gestión de sesiones** con tokens JWT
- **Auto-logout** por inactividad
- **Permisos granulares** por tipo de usuario

### 📊 Dashboard Interactivo
- **Estadísticas en tiempo real** del sistema
- **Gráficos y métricas** visuales
- **Rutas activas** del día
- **Actividad reciente** del sistema
- **Actualizaciones automáticas** cada 30 segundos

### 👥 Gestión de Usuarios
- **CRUD completo** de usuarios
- **Filtros avanzados** por tipo y estado
- **Búsqueda en tiempo real**
- **Activación/desactivación** de cuentas
- **Roles diferenciados** (Operador, Guardia, Cliente)

### 🏢 Gestión de Propiedades
- **Registro de propiedades** con geolocalización
- **Tipos de propiedad** (Residencial, Comercial, Industrial, Mixto)
- **Estados configurables** (Activa, Inactiva, Mantenimiento)
- **Niveles de prioridad** (1-5)
- **Gestión de perímetros** (en desarrollo)

### 🛣️ Gestión de Rutas
- **Creación de rutas** de vigilancia
- **Asignación de guardias** a rutas específicas
- **Puntos de control** georreferenciados
- **Horarios flexibles** y frecuencias configurables
- **Rutas de emergencia** con prioridad especial

### 📡 Monitoreo en Tiempo Real
- **Estado actual** de todas las rutas activas
- **Progreso en tiempo real** de ejecuciones
- **Detección de retrasos** automática
- **Alertas visuales** para situaciones críticas
- **Métricas de cumplimiento** instantáneas

### 📈 Reportes y Analytics
- **Reportes detallados** por período
- **Estadísticas de rendimiento** por guardia
- **Análisis de rutas** y eficiencia
- **Exportación de datos** en JSON
- **Gráficos interactivos** (en desarrollo)

## Arquitectura Técnica

### 🛠️ Stack Tecnológico
- **HTML5** - Estructura semántica
- **CSS3** - Estilos modernos con variables CSS
- **JavaScript ES6+** - Lógica de aplicación
- **Fetch API** - Comunicación con backend
- **LocalStorage** - Persistencia local
- **Font Awesome** - Iconografía
- **Google Fonts** - Tipografía (Inter)

### 📁 Estructura de Archivos
```
geovigia-frontend/
├── index.html              # Página principal
├── css/
│   ├── styles.css          # Estilos principales
│   └── components.css      # Estilos de componentes
├── js/
│   ├── config.js           # Configuración global
│   ├── utils.js            # Utilidades y helpers
│   ├── api.js              # Cliente API y servicios
│   ├── auth.js             # Gestión de autenticación
│   ├── components.js       # Componentes reutilizables
│   ├── app.js              # Aplicación principal
│   ├── dashboard.js        # Módulo dashboard
│   ├── users.js            # Módulo usuarios
│   ├── properties.js       # Módulo propiedades
│   ├── routes.js           # Módulo rutas
│   ├── monitoring.js       # Módulo monitoreo
│   └── reports.js          # Módulo reportes
├── assets/                 # Recursos estáticos
└── components/             # Componentes adicionales
```

### 🔧 Componentes Principales

#### 1. **APIClient** (`api.js`)
- Cliente HTTP con reintentos automáticos
- Manejo de errores y timeouts
- Autenticación por tokens
- Servicios especializados por módulo

#### 2. **AuthManager** (`auth.js`)
- Gestión completa de autenticación
- Verificación de permisos por rol
- Renovación automática de tokens
- Middleware de protección de rutas

#### 3. **Componentes Reutilizables** (`components.js`)
- **Toast**: Notificaciones no intrusivas
- **Modal**: Diálogos y confirmaciones
- **Table**: Tablas con paginación y búsqueda
- **Loading**: Indicadores de carga

#### 4. **Utilidades** (`utils.js`)
- Formateo de fechas y números
- Validaciones de formularios
- Manipulación del DOM
- Gestión de almacenamiento local

## Configuración y Uso

### 🚀 Instalación
1. **Clonar el repositorio**
2. **Abrir `index.html`** en un navegador moderno
3. **Configurar la URL del backend** en `js/config.js`

### ⚙️ Configuración
El archivo `js/config.js` contiene toda la configuración:

```javascript
const API_CONFIG = {
    BASE_URL: 'http://127.0.0.1:8000/api',  // URL del backend
    TIMEOUT: 30000,                          // Timeout de peticiones
    RETRY_ATTEMPTS: 3,                       // Reintentos automáticos
};
```

### 🔑 Credenciales de Acceso
- **Usuario**: admin
- **Contraseña**: admin123
- **Tipo**: Operador (acceso completo)

### 📱 Compatibilidad
- **Chrome** 80+
- **Firefox** 75+
- **Safari** 13+
- **Edge** 80+
- **Dispositivos móviles** iOS 13+, Android 8+

## Funcionalidades por Módulo

### 📊 Dashboard
- Estadísticas generales del sistema
- Rutas programadas para hoy
- Actividad reciente
- Métricas en tiempo real

### 👥 Usuarios
- Lista paginada con filtros
- Formulario de creación/edición
- Activación/desactivación
- Búsqueda en tiempo real

### 🏢 Propiedades
- Gestión completa de propiedades
- Validación de coordenadas GPS
- Filtros por tipo y estado
- Integración con rutas

### 🛣️ Rutas
- Creación de rutas de vigilancia
- Asignación de guardias
- Gestión de puntos de control
- Horarios y frecuencias

### 📡 Monitoreo
- Estado en tiempo real
- Progreso de ejecuciones
- Detección de retrasos
- Alertas automáticas

### 📈 Reportes
- Estadísticas por período
- Rendimiento por guardia
- Análisis de rutas
- Exportación de datos

## Seguridad

### 🔒 Medidas Implementadas
- **Autenticación por tokens** JWT
- **Validación de permisos** por rol
- **Sanitización de datos** de entrada
- **Protección CSRF** en formularios
- **Timeout de sesión** automático

### 🛡️ Buenas Prácticas
- **Validación client-side** y server-side
- **Manejo seguro** de errores
- **Logs de actividad** (en desarrollo)
- **Encriptación** de datos sensibles

## Desarrollo y Extensión

### 🔧 Agregar Nuevos Módulos
1. Crear archivo JS en `/js/`
2. Seguir patrón de clase modular
3. Registrar en `app.js`
4. Agregar navegación en HTML

### 🎨 Personalizar Estilos
- Modificar variables CSS en `styles.css`
- Agregar componentes en `components.css`
- Mantener consistencia visual

### 📡 Integrar APIs
- Extender servicios en `api.js`
- Seguir patrón de promesas
- Manejar errores apropiadamente

## Próximas Funcionalidades

### 🗺️ Mapas Interactivos
- Visualización de propiedades
- Rutas en tiempo real
- Geofencing avanzado

### 📱 PWA (Progressive Web App)
- Instalación en dispositivos
- Funcionamiento offline
- Notificaciones push

### 📊 Gráficos Avanzados
- Charts.js o D3.js
- Dashboards interactivos
- Exportación de gráficos

### 🔔 Notificaciones en Tiempo Real
- WebSockets
- Alertas push
- Sistema de mensajería

## Soporte y Mantenimiento

### 🐛 Reporte de Errores
- Usar la consola del navegador
- Verificar logs de red
- Documentar pasos para reproducir

### 🔄 Actualizaciones
- Refrescar caché del navegador
- Verificar compatibilidad
- Revisar configuración

---

**Desarrollado para el Sistema GeoVigia**  
*Interfaz web moderna para operadores de vigilancia*

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator


class CustomUser(AbstractUser):
    """
    Usuario personalizado que extiende el modelo User de Django
    para manejar los tres tipos de usuarios del sistema GeoVigia
    """

    USER_TYPES = (
        ('cliente', 'Cliente'),
        ('guardia', 'Guardia'),
        ('operador', 'Operador'),
    )

    user_type = models.CharField(
        max_length=10,
        choices=USER_TYPES,
        default='cliente',
        verbose_name='Tipo de Usuario'
    )

    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="El número de teléfono debe estar en formato: '+999999999'. Hasta 15 dígitos permitidos."
    )
    phone_number = models.CharField(
        validators=[phone_regex],
        max_length=17,
        blank=True,
        verbose_name='Número de Teléfono'
    )

    address = models.TextField(
        blank=True,
        verbose_name='Dirección'
    )

    is_active_service = models.BooleanField(
        default=True,
        verbose_name='Servicio Activo',
        help_text='Indica si el usuario tiene el servicio activo'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Fecha de Creación'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='Última Actualización'
    )

    class Meta:
        verbose_name = 'Usuario'
        verbose_name_plural = 'Usuarios'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.username} ({self.get_user_type_display()})"

    @property
    def is_cliente(self):
        return self.user_type == 'cliente'

    @property
    def is_guardia(self):
        return self.user_type == 'guardia'

    @property
    def is_operador(self):
        return self.user_type == 'operador'


class ClienteProfile(models.Model):
    """
    Perfil específico para usuarios tipo Cliente
    """
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='cliente_profile',
        limit_choices_to={'user_type': 'cliente'}
    )

    emergency_contact_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='Nombre Contacto de Emergencia'
    )

    emergency_contact_phone = models.CharField(
        max_length=17,
        blank=True,
        verbose_name='Teléfono Contacto de Emergencia'
    )

    home_alone_frequency = models.PositiveIntegerField(
        default=0,
        verbose_name='Frecuencia Casa Sola',
        help_text='Número de veces que ha usado el botón "Casa sola"'
    )

    class Meta:
        verbose_name = 'Perfil de Cliente'
        verbose_name_plural = 'Perfiles de Clientes'

    def __str__(self):
        return f"Cliente: {self.user.username}"


class GuardiaProfile(models.Model):
    """
    Perfil específico para usuarios tipo Guardia
    """
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='guardia_profile',
        limit_choices_to={'user_type': 'guardia'}
    )

    employee_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='ID de Empleado'
    )

    shift_start = models.TimeField(
        verbose_name='Inicio de Turno'
    )

    shift_end = models.TimeField(
        verbose_name='Fin de Turno'
    )

    is_on_duty = models.BooleanField(
        default=False,
        verbose_name='En Servicio'
    )

    current_latitude = models.DecimalField(
        max_digits=10,
        decimal_places=8,
        null=True,
        blank=True,
        verbose_name='Latitud Actual'
    )

    current_longitude = models.DecimalField(
        max_digits=11,
        decimal_places=8,
        null=True,
        blank=True,
        verbose_name='Longitud Actual'
    )

    last_location_update = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Última Actualización de Ubicación'
    )

    class Meta:
        verbose_name = 'Perfil de Guardia'
        verbose_name_plural = 'Perfiles de Guardias'

    def __str__(self):
        return f"Guardia: {self.user.username} ({self.employee_id})"


class OperadorProfile(models.Model):
    """
    Perfil específico para usuarios tipo Operador
    """
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='operador_profile',
        limit_choices_to={'user_type': 'operador'}
    )

    department = models.CharField(
        max_length=50,
        default='Administración',
        verbose_name='Departamento'
    )

    can_manage_users = models.BooleanField(
        default=True,
        verbose_name='Puede Gestionar Usuarios'
    )

    can_manage_routes = models.BooleanField(
        default=True,
        verbose_name='Puede Gestionar Rutas'
    )

    can_view_reports = models.BooleanField(
        default=True,
        verbose_name='Puede Ver Reportes'
    )

    class Meta:
        verbose_name = 'Perfil de Operador'
        verbose_name_plural = 'Perfiles de Operadores'

    def __str__(self):
        return f"Operador: {self.user.username}"

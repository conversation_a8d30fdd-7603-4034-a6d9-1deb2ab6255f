from rest_framework import serializers
from django.utils import timezone
from .models import Route, Route<PERSON>roperty, GuardRoute, RouteCheckpoint, RouteExecution
from users.models import CustomUser
from properties.models import Property


class RouteSerializer(serializers.ModelSerializer):
    """
    Serializer para el modelo Route
    """
    is_active = serializers.ReadOnlyField()
    is_high_priority = serializers.ReadOnlyField()
    active_days = serializers.ReadOnlyField()
    total_properties = serializers.SerializerMethodField()
    checkpoint_count = serializers.SerializerMethodField()
    is_active_today = serializers.SerializerMethodField()

    class Meta:
        model = Route
        fields = [
            'id', 'name', 'description', 'status', 'frequency',
            'start_time', 'end_time', 'estimated_duration_minutes',
            'max_deviation_meters', 'priority_level', 'is_emergency_route',
            'monday', 'tuesday', 'wednesday', 'thursday', 'friday',
            'saturday', 'sunday', 'created_at', 'updated_at',
            'is_active', 'is_high_priority', 'active_days',
            'total_properties', 'checkpoint_count', 'is_active_today'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_total_properties(self, obj):
        return obj.get_total_properties()

    def get_checkpoint_count(self, obj):
        return obj.get_checkpoint_count()

    def get_is_active_today(self, obj):
        return obj.is_active_today()

    def validate(self, data):
        """Validación personalizada"""
        start_time = data.get('start_time')
        end_time = data.get('end_time')

        if start_time and end_time and start_time >= end_time:
            raise serializers.ValidationError(
                "La hora de fin debe ser posterior a la hora de inicio"
            )

        return data


class RoutePropertySerializer(serializers.ModelSerializer):
    """
    Serializer para el modelo RouteProperty
    """
    property_name = serializers.CharField(source='property.name', read_only=True)
    property_address = serializers.CharField(source='property.full_address', read_only=True)
    property_coordinates = serializers.ReadOnlyField(source='property.coordinates')

    class Meta:
        model = RouteProperty
        fields = [
            'id', 'route', 'property', 'order', 'estimated_time_minutes',
            'is_mandatory', 'notes', 'created_at',
            'property_name', 'property_address', 'property_coordinates'
        ]
        read_only_fields = ['created_at']

    def validate(self, data):
        """Validación personalizada"""
        route = data.get('route')
        order = data.get('order')

        # Verificar que el orden no esté duplicado en la misma ruta
        if route and order:
            existing = RouteProperty.objects.filter(
                route=route,
                order=order
            ).exclude(pk=self.instance.pk if self.instance else None)

            if existing.exists():
                raise serializers.ValidationError(
                    f"Ya existe una propiedad con orden {order} en esta ruta"
                )

        return data


class GuardRouteSerializer(serializers.ModelSerializer):
    """
    Serializer para el modelo GuardRoute
    """
    guard_username = serializers.CharField(source='guard.username', read_only=True)
    guard_name = serializers.CharField(source='guard.get_full_name', read_only=True)
    route_name = serializers.CharField(source='route.name', read_only=True)
    is_active = serializers.ReadOnlyField()

    class Meta:
        model = GuardRoute
        fields = [
            'id', 'guard', 'route', 'status', 'start_date', 'end_date',
            'is_primary_guard', 'notes', 'created_at',
            'guard_username', 'guard_name', 'route_name', 'is_active'
        ]
        read_only_fields = ['created_at']

    def validate(self, data):
        """Validación personalizada"""
        guard = data.get('guard')
        if guard and not guard.is_guardia:
            raise serializers.ValidationError(
                "Solo los usuarios tipo 'guardia' pueden ser asignados a rutas"
            )

        start_date = data.get('start_date')
        end_date = data.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError(
                "La fecha de fin debe ser posterior a la fecha de inicio"
            )

        return data


class RouteCheckpointSerializer(serializers.ModelSerializer):
    """
    Serializer para el modelo RouteCheckpoint
    """
    coordinates = serializers.ReadOnlyField()
    property_name = serializers.CharField(source='related_property.name', read_only=True)

    class Meta:
        model = RouteCheckpoint
        fields = [
            'id', 'route', 'name', 'checkpoint_type', 'latitude', 'longitude',
            'order', 'radius_meters', 'is_mandatory', 'estimated_time_minutes',
            'related_property', 'description', 'created_at',
            'coordinates', 'property_name'
        ]
        read_only_fields = ['created_at']

    def validate_latitude(self, value):
        """Validar que la latitud esté en el rango válido"""
        if not -90 <= float(value) <= 90:
            raise serializers.ValidationError(
                "La latitud debe estar entre -90 y 90 grados"
            )
        return value

    def validate_longitude(self, value):
        """Validar que la longitud esté en el rango válido"""
        if not -180 <= float(value) <= 180:
            raise serializers.ValidationError(
                "La longitud debe estar entre -180 y 180 grados"
            )
        return value

    def validate(self, data):
        """Validación personalizada"""
        route = data.get('route')
        order = data.get('order')

        # Verificar que el orden no esté duplicado en la misma ruta
        if route and order:
            existing = RouteCheckpoint.objects.filter(
                route=route,
                order=order
            ).exclude(pk=self.instance.pk if self.instance else None)

            if existing.exists():
                raise serializers.ValidationError(
                    f"Ya existe un punto de control con orden {order} en esta ruta"
                )

        return data


class RouteExecutionSerializer(serializers.ModelSerializer):
    """
    Serializer para el modelo RouteExecution
    """
    route_name = serializers.CharField(source='route.name', read_only=True)
    guard_username = serializers.CharField(source='guard.username', read_only=True)
    duration_minutes = serializers.ReadOnlyField()
    completion_percentage = serializers.ReadOnlyField()
    is_delayed = serializers.ReadOnlyField()
    is_on_time = serializers.ReadOnlyField()

    class Meta:
        model = RouteExecution
        fields = [
            'id', 'route', 'guard', 'status', 'start_time', 'end_time',
            'planned_start_time', 'planned_end_time', 'total_distance_meters',
            'checkpoints_visited', 'checkpoints_total', 'properties_visited',
            'properties_total', 'notes', 'incidents_reported', 'created_at',
            'route_name', 'guard_username', 'duration_minutes',
            'completion_percentage', 'is_delayed', 'is_on_time'
        ]
        read_only_fields = ['created_at']

    def validate(self, data):
        """Validación personalizada"""
        guard = data.get('guard')
        if guard and not guard.is_guardia:
            raise serializers.ValidationError(
                "Solo los usuarios tipo 'guardia' pueden ejecutar rutas"
            )

        start_time = data.get('start_time')
        end_time = data.get('end_time')

        if start_time and end_time and start_time >= end_time:
            raise serializers.ValidationError(
                "La hora de fin debe ser posterior a la hora de inicio"
            )

        return data


class RouteDetailSerializer(RouteSerializer):
    """
    Serializer detallado para Route que incluye relaciones
    """
    route_properties = RoutePropertySerializer(many=True, read_only=True)
    checkpoints = RouteCheckpointSerializer(many=True, read_only=True)
    assigned_guards = GuardRouteSerializer(many=True, read_only=True)

    class Meta(RouteSerializer.Meta):
        fields = RouteSerializer.Meta.fields + [
            'route_properties', 'checkpoints', 'assigned_guards'
        ]


class RouteCreateSerializer(serializers.ModelSerializer):
    """
    Serializer para crear rutas con configuración inicial
    """
    class Meta:
        model = Route
        fields = [
            'id', 'name', 'description', 'frequency', 'start_time', 'end_time',
            'estimated_duration_minutes', 'max_deviation_meters',
            'priority_level', 'is_emergency_route',
            'monday', 'tuesday', 'wednesday', 'thursday', 'friday',
            'saturday', 'sunday', 'is_active', 'is_high_priority'
        ]
        read_only_fields = ['id', 'is_active', 'is_high_priority']

    def create(self, validated_data):
        """Crear ruta y asignar creador"""
        request = self.context.get('request')
        if request and request.user.is_operador:
            validated_data['created_by'] = request.user

        return super().create(validated_data)

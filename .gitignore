# Entorno virtual de Python
/geo/

# Archivos de Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# Archivos del sistema
.DS_Store
Thumbs.db

# IDEs
.vscode/
.idea/
*.swp
*.swo

# Archivos de configuración sensibles
.env
.env.local
.env.production

# Node modules (para frontend futuro)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
/geovigia_backend/users/migrations/__pycache__
/geovigia_backend/users/migrations/__pycache__
/geovigia_backend/users/migrations/__pycache__

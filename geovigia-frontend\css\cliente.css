/* ===== CLIENTE DASHBOARD STYLES ===== */

.cliente-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #f093fb 100%);
    min-height: 100vh;
    font-family: 'Inter', sans-serif;
}

/* Header */
.cliente-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logout-btn {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: #ff5252;
    transform: translateY(-2px);
}

/* Main Content */
.cliente-main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

/* Property Status */
.property-status {
    grid-column: 1 / -1;
}

.property-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.property-info h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #48bb78;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Guard Tracking */
.guard-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.guard-info {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.guard-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.guard-details h3 {
    margin: 0 0 0.5rem 0;
    color: #2d3748;
}

.guard-location {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    font-weight: 500;
}

.patrol-stats {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.stat-label {
    font-size: 0.875rem;
    color: #718096;
}

/* Quick Actions */
.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.action-btn {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.action-btn:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.action-btn i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.action-btn span {
    font-weight: 600;
    font-size: 1.1rem;
}

.action-btn small {
    color: #718096;
    font-size: 0.875rem;
}

/* Action Button Colors */
.casa-sola {
    border-color: #3182ce;
    color: #3182ce;
}

.casa-sola:hover {
    background: #3182ce;
    color: white;
}

.sospechoso {
    border-color: #f56565;
    color: #f56565;
}

.sospechoso:hover {
    background: #f56565;
    color: white;
}

.ayuda {
    border-color: #ff6b6b;
    color: #ff6b6b;
}

.ayuda:hover {
    background: #ff6b6b;
    color: white;
}

.llegando {
    border-color: #48bb78;
    color: #48bb78;
}

.llegando:hover {
    background: #48bb78;
    color: white;
}

/* Recent Activity */
.activity-list {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.activity-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.activity-content p {
    margin: 0 0 0.25rem 0;
    font-weight: 500;
}

.activity-time {
    font-size: 0.875rem;
    color: #718096;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    max-width: 400px;
    width: 90%;
    text-align: center;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.btn-cancel, .btn-confirm {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
}

.btn-cancel {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-confirm {
    background: #667eea;
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .cliente-main {
        grid-template-columns: 1fr;
        padding: 1rem;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .header-content {
        padding: 0 1rem;
    }
}

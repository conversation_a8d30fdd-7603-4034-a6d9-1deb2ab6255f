#!/usr/bin/env python
"""
Script de prueba para las APIs del módulo de Propiedades del sistema GeoVigia
Ejecutar con: python test_properties_apis.py
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def get_auth_tokens():
    """Obtener tokens de autenticación para diferentes usuarios"""
    tokens = {}

    # Login como operador (admin)
    response = requests.post(f"{BASE_URL}/users/login/", json={
        "username": "admin",
        "password": "admin123"
    })
    if response.status_code == 200:
        tokens['operador'] = response.json()['token']
        print("✅ Token de operador obtenido")

    # Login como cliente (si existe)
    response = requests.post(f"{BASE_URL}/users/login/", json={
        "username": "cliente_test",
        "password": "testpass123"
    })
    if response.status_code == 200:
        tokens['cliente'] = response.json()['token']
        print("✅ Token de cliente obtenido")

    return tokens

def test_create_properties(tokens):
    """Prueba la creación de propiedades"""
    print("\n=== Probando Creación de Propiedades ===")

    if 'operador' not in tokens:
        print("❌ No hay token de operador disponible")
        return []

    properties_data = [
        {
            "name": "Casa Principal",
            "property_type": "casa",
            "address": "Calle 123, Colonia Centro",
            "neighborhood": "Centro",
            "city": "Ciudad de México",
            "postal_code": "01000",
            "latitude": "19.4326",
            "longitude": "-99.1332",
            "description": "Casa principal de la familia",
            "area_size": 200,
            "floors": 2,
            "priority_level": 3,
            "emergency_contact_name": "Juan Pérez",
            "emergency_contact_phone": "+5215551234567"
        },
        {
            "name": "Oficina Corporativa",
            "property_type": "oficina",
            "address": "Av. Reforma 456, Piso 10",
            "neighborhood": "Polanco",
            "city": "Ciudad de México",
            "postal_code": "11560",
            "latitude": "19.4285",
            "longitude": "-99.1277",
            "description": "Oficina principal de la empresa",
            "area_size": 500,
            "floors": 1,
            "priority_level": 5,
            "emergency_contact_name": "Ana López",
            "emergency_contact_phone": "+5215559876543"
        }
    ]

    created_properties = []
    headers = {"Authorization": f"Token {tokens['operador']}"}

    for prop_data in properties_data:
        try:
            response = requests.post(
                f"{BASE_URL}/properties/",
                json=prop_data,
                headers=headers
            )

            if response.status_code == 201:
                data = response.json()
                created_properties.append(data)
                print(f"✅ Propiedad '{prop_data['name']}' creada exitosamente")
                print(f"   ID: {data['id']}, Coordenadas: {data['coordinates']}")
            else:
                print(f"❌ Error creando '{prop_data['name']}': {response.status_code}")
                print(f"   {response.text}")
        except Exception as e:
            print(f"❌ Excepción creando '{prop_data['name']}': {e}")

    return created_properties

def test_property_assignments(tokens, properties):
    """Prueba las asignaciones de propiedades a clientes"""
    print("\n=== Probando Asignaciones de Propiedades ===")

    if 'operador' not in tokens or not properties:
        print("❌ No hay token de operador o propiedades disponibles")
        return

    headers = {"Authorization": f"Token {tokens['operador']}"}

    # Primero, obtener lista de clientes
    response = requests.get(f"{BASE_URL}/users/list/", headers=headers)
    if response.status_code != 200:
        print("❌ No se pudo obtener lista de usuarios")
        return

    users_data = response.json()
    # Verificar si la respuesta es una lista o un objeto con resultados paginados
    if isinstance(users_data, dict) and 'results' in users_data:
        users = users_data['results']
    else:
        users = users_data

    clientes = [user for user in users if user.get('user_type') == 'cliente']

    if not clientes:
        print("❌ No hay clientes disponibles para asignar")
        return

    # Asignar primera propiedad al primer cliente
    property_id = properties[0]['id']
    client_id = clientes[0]['id']

    assignment_data = {
        "client_id": client_id,
        "ownership_type": "propietario",
        "is_primary": True
    }

    try:
        response = requests.post(
            f"{BASE_URL}/properties/{property_id}/assign/",
            json=assignment_data,
            headers=headers
        )

        if response.status_code == 201:
            data = response.json()
            print(f"✅ Propiedad asignada exitosamente")
            print(f"   Cliente: {data['assignment']['client_username']}")
            print(f"   Propiedad: {data['assignment']['property_name']}")
        else:
            print(f"❌ Error asignando propiedad: {response.status_code}")
            print(f"   {response.text}")
    except Exception as e:
        print(f"❌ Excepción asignando propiedad: {e}")

def test_property_perimeters(tokens, properties):
    """Prueba la creación de perímetros para propiedades"""
    print("\n=== Probando Perímetros de Propiedades ===")

    if 'operador' not in tokens or not properties:
        print("❌ No hay token de operador o propiedades disponibles")
        return

    headers = {"Authorization": f"Token {tokens['operador']}"}
    property_id = properties[0]['id']

    # Crear perímetro circular
    perimeter_data = {
        "perimeter_type": "circular",
        "radius_meters": 100,
        "is_active": True,
        "alert_on_entry": True,
        "alert_on_exit": True,
        "description": "Perímetro de seguridad estándar"
    }

    try:
        response = requests.post(
            f"{BASE_URL}/properties/{property_id}/perimeter/create/",
            json=perimeter_data,
            headers=headers
        )

        if response.status_code == 201:
            data = response.json()
            print(f"✅ Perímetro creado exitosamente")
            print(f"   Tipo: {data['perimeter']['perimeter_type']}")
            print(f"   Radio: {data['perimeter']['radius_meters']} metros")
            print(f"   Área: {data['perimeter']['area_coverage']} m²")
        else:
            print(f"❌ Error creando perímetro: {response.status_code}")
            print(f"   {response.text}")
    except Exception as e:
        print(f"❌ Excepción creando perímetro: {e}")

def test_property_locations(tokens):
    """Prueba la obtención de ubicaciones de propiedades"""
    print("\n=== Probando Ubicaciones de Propiedades ===")

    if 'operador' not in tokens:
        print("❌ No hay token de operador disponible")
        return

    headers = {"Authorization": f"Token {tokens['operador']}"}

    try:
        response = requests.get(f"{BASE_URL}/properties/locations/", headers=headers)

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Ubicaciones obtenidas exitosamente")
            print(f"   Total de propiedades: {len(data)}")
            for prop in data:
                print(f"   - {prop['name']}: {prop['coordinates']}")
        else:
            print(f"❌ Error obteniendo ubicaciones: {response.status_code}")
    except Exception as e:
        print(f"❌ Excepción obteniendo ubicaciones: {e}")

def test_properties_near_location(tokens):
    """Prueba la búsqueda de propiedades cerca de una ubicación"""
    print("\n=== Probando Búsqueda de Propiedades Cercanas ===")

    if 'operador' not in tokens:
        print("❌ No hay token de operador disponible")
        return

    headers = {"Authorization": f"Token {tokens['operador']}"}

    # Buscar propiedades cerca del Zócalo de la CDMX
    params = {
        "latitude": "19.4326",
        "longitude": "-99.1332",
        "radius_km": "10"
    }

    try:
        response = requests.get(
            f"{BASE_URL}/properties/near/",
            params=params,
            headers=headers
        )

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Búsqueda de propiedades cercanas exitosa")
            print(f"   Centro: {data['center']}")
            print(f"   Radio: {data['radius_km']} km")
            print(f"   Propiedades encontradas: {len(data['properties'])}")
        else:
            print(f"❌ Error en búsqueda: {response.status_code}")
    except Exception as e:
        print(f"❌ Excepción en búsqueda: {e}")

def main():
    """Función principal que ejecuta todas las pruebas"""
    print("🏠 Iniciando pruebas de APIs del Módulo de Propiedades")
    print("=" * 60)

    # Obtener tokens de autenticación
    tokens = get_auth_tokens()

    if not tokens:
        print("❌ No se pudieron obtener tokens de autenticación")
        return

    # Ejecutar pruebas en orden
    properties = test_create_properties(tokens)
    test_property_assignments(tokens, properties)
    test_property_perimeters(tokens, properties)
    test_property_locations(tokens)
    test_properties_near_location(tokens)

    print("\n✅ Pruebas del módulo de propiedades completadas")
    print("=" * 60)

if __name__ == "__main__":
    main()

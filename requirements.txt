# Django Framework
Django>=5.2.0,<6.0.0

# Django REST Framework para APIs
djangorestframework>=3.14.0

# CORS headers para permitir requests desde el frontend
django-cors-headers>=4.0.0

# Django Channels para WebSockets y notificaciones en tiempo real
channels>=4.0.0
channels-redis>=4.1.0

# Redis para el backend de canales (requerido por channels-redis)
redis>=4.5.0

# Base de datos (SQLite viene incluido con Python)
# Para PostgreSQL en producción, descomentar:
# psycopg2-binary>=2.9.0

# Para MySQL en producción, descomentar:
# mysqlclient>=2.1.0

# Utilidades adicionales (opcionales pero recomendadas)
python-decouple>=3.6  # Para manejo de variables de entorno
Pillow>=9.0.0         # Para manejo de imágenes si se necesita
pytz>=2023.3          # Para manejo de zonas horarias

# Herramientas de desarrollo (opcionales)
# django-debug-toolbar>=4.0.0  # Para debugging en desarrollo
# django-extensions>=3.2.0     # Extensiones útiles para Django

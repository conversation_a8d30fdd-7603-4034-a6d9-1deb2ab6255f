<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GeoVigia Cliente - Mi Propiedad</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/cliente.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <!-- Leaflet Draw CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css"/>
</head>
<body class="cliente-dashboard">
    <!-- Header -->
    <header class="cliente-header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-shield-alt"></i>
                <span>GeoVigia Cliente</span>
            </div>
            <div class="user-info">
                <span id="user-name">Ana Propietaria</span>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="cliente-main">
        <!-- Property Status -->
        <section class="property-status">
            <h2><i class="fas fa-home"></i> Mi Propiedad</h2>
            <div class="property-card">
                <div class="property-info">
                    <h3 id="property-name">Casa Residencial</h3>
                    <p id="property-address">Av. Reforma 123, CDMX</p>
                    <div class="status-indicator">
                        <span class="status-dot active"></span>
                        <span>Monitoreada</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Guard Tracking -->
        <section class="guard-tracking">
            <h2><i class="fas fa-user-shield"></i> Guardia Asignado</h2>
            <div class="guard-card">
                <div class="guard-info">
                    <div class="guard-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="guard-details">
                        <h3 id="guard-name">Miguel Seguridad</h3>
                        <p id="guard-status">En servicio</p>
                        <div class="guard-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span id="guard-distance">150m de tu propiedad</span>
                        </div>
                    </div>
                </div>
                <div class="patrol-stats">
                    <div class="stat">
                        <span class="stat-number" id="patrol-count">3</span>
                        <span class="stat-label">Patrullajes hoy</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number" id="last-patrol">15 min</span>
                        <span class="stat-label">Último patrullaje</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="quick-actions">
            <h2><i class="fas fa-bolt"></i> Acciones Rápidas</h2>
            <div class="action-buttons">
                <button class="action-btn casa-sola" onclick="sendAlert('casa_sola')">
                    <i class="fas fa-suitcase"></i>
                    <span>Casa Sola</span>
                    <small>Por viaje o trabajo</small>
                </button>

                <button class="action-btn sospechoso" onclick="sendAlert('actitud_sospechosa')">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Actitud Sospechosa</span>
                    <small>Reportar actividad extraña</small>
                </button>

                <button class="action-btn ayuda" onclick="sendAlert('solicitar_ayuda')">
                    <i class="fas fa-sos"></i>
                    <span>Solicitar Ayuda</span>
                    <small>Emergencia</small>
                </button>

                <button class="action-btn llegando" onclick="sendAlert('llegando_casa')">
                    <i class="fas fa-home"></i>
                    <span>Estoy Llegando</span>
                    <small>Notificar llegada</small>
                </button>
            </div>
        </section>

        <!-- Recent Activity -->
        <section class="recent-activity">
            <h2><i class="fas fa-history"></i> Actividad Reciente</h2>
            <div class="activity-list" id="activity-list">
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="activity-content">
                        <p>Patrullaje completado</p>
                        <span class="activity-time">Hace 15 minutos</span>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="activity-content">
                        <p>Guardia en perímetro</p>
                        <span class="activity-time">Hace 1 hora</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Property Map Section -->
        <section class="property-map">
            <h2><i class="fas fa-map-marker-alt"></i> Ubicación de Mi Propiedad</h2>
            <div class="map-container">
                <div id="property-map" class="property-map-view" style="height: 300px; width: 100%;">
                    <!-- Leaflet map will be initialized here -->
                </div>
                <div class="map-info">
                    <div class="map-legend">
                        <div class="legend-item">
                            <span class="legend-color" style="background-color: #ffa502;"></span>
                            <span>Mi Propiedad</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background-color: #ff6b6b;"></span>
                            <span>Guardia Activo</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background-color: #48bb78;"></span>
                            <span>Ruta de Patrullaje</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Alert Modal -->
    <div id="alert-modal" class="modal">
        <div class="modal-content">
            <h3>Confirmar Alerta</h3>
            <p id="alert-message"></p>
            <div class="modal-actions">
                <button class="btn-cancel" onclick="closeModal()">Cancelar</button>
                <button class="btn-confirm" onclick="confirmAlert()">Enviar Alerta</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Leaflet Draw JS -->
    <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>

    <!-- Application Scripts -->
    <script src="js/config.js"></script>
    <script src="js/components.js"></script>
    <script src="js/maps.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/cliente-dashboard.js"></script>

    <script>
        // Initialize client property map when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeClientMap();
        });

        function initializeClientMap() {
            if (typeof GeoVigia !== 'undefined' && GeoVigia.Maps) {
                // Initialize map in the property-map container
                if (!window.clientMap && document.getElementById('property-map')) {
                    window.clientMap = new GeoVigiaMaps();
                    window.clientMap.init('property-map');
                    console.log('✅ Mapa de cliente inicializado');

                    // Focus on client's property if available
                    setTimeout(() => {
                        if (window.clientMap) {
                            window.clientMap.centerOnProperties();
                        }
                    }, 1000);
                }
            } else {
                console.warn('⚠️ GeoVigia Maps no disponible');
            }
        }
    </script>
</body>
</html>

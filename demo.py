#!/usr/bin/env python3
"""
🚀 GeoVigia Demo Launcher
========================

Ejecuta la demo interactiva de GeoVigia con un solo comando.

Uso:
    python demo.py

Características:
- ✅ Demo frontend completa
- ✅ Sin dependencias del backend
- ✅ Abre automáticamente el navegador
- ✅ Funciona en Windows, Mac y Linux
- ✅ Puerto automático (3000 por defecto)

Autor: GeoVigia Team
Versión: 2.0
"""

import os
import sys
import http.server
import socketserver
import threading
import time
import webbrowser
import signal

# Configuración
FRONTEND_PORT = 3000
FRONTEND_DIR = "geovigia-frontend"

class ColoredOutput:
    """Colores para output en terminal"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_colored(message, color):
    """Imprimir mensaje con color"""
    print(f"{color}{message}{ColoredOutput.ENDC}")

def check_frontend():
    """Verificar que el directorio frontend existe"""
    if not os.path.exists(FRONTEND_DIR):
        print_colored(f"❌ Error: Directorio {FRONTEND_DIR} no encontrado", ColoredOutput.FAIL)
        return False
    
    demo_file = os.path.join(FRONTEND_DIR, "demo.html")
    if not os.path.exists(demo_file):
        print_colored(f"❌ Error: Archivo demo.html no encontrado en {FRONTEND_DIR}", ColoredOutput.FAIL)
        return False
    
    print_colored("✅ Demo frontend verificado correctamente", ColoredOutput.OKGREEN)
    return True

class DemoHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Handler personalizado para el servidor frontend"""
    
    def do_GET(self):
        # Si la ruta es '/', servir demo.html
        if self.path == '/':
            self.path = '/demo.html'
        # Manejar favicon.ico
        elif self.path == '/favicon.ico':
            if not os.path.exists('favicon.ico'):
                self.send_response(204)  # No Content
                self.end_headers()
                return
        return super().do_GET()
    
    def end_headers(self):
        # Agregar headers para evitar caché
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()
    
    def log_message(self, format, *args):
        # Personalizar logs del servidor frontend
        message = format % args
        if 'favicon.ico' not in message and 'GET /demo.html' not in message:
            print_colored(f"🌐 Frontend: {message}", ColoredOutput.OKBLUE)

def run_frontend_server():
    """Ejecutar el servidor para el frontend"""
    print_colored(f"🌐 Iniciando servidor demo en puerto {FRONTEND_PORT}...", ColoredOutput.OKCYAN)
    
    try:
        # Cambiar al directorio frontend
        os.chdir(FRONTEND_DIR)
        
        # Crear servidor HTTP
        with socketserver.TCPServer(("", FRONTEND_PORT), DemoHTTPRequestHandler) as httpd:
            print_colored(f"✅ Servidor demo ejecutándose en http://localhost:{FRONTEND_PORT}", ColoredOutput.OKGREEN)
            print_colored("📱 La demo se abrirá automáticamente en tu navegador", ColoredOutput.OKCYAN)
            
            # Abrir navegador después de un breve delay
            def open_browser():
                time.sleep(2)
                webbrowser.open(f'http://localhost:{FRONTEND_PORT}/demo.html')
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            # Ejecutar servidor
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 98 or e.errno == 10048:  # Puerto en uso
            print_colored(f"❌ Error: Puerto {FRONTEND_PORT} ya está en uso", ColoredOutput.FAIL)
            print_colored("💡 Solución: Cierra cualquier servidor anterior o usa otro puerto", ColoredOutput.WARNING)
        else:
            print_colored(f"❌ Error del servidor: {e}", ColoredOutput.FAIL)
        sys.exit(1)
    except KeyboardInterrupt:
        print_colored("\n🛑 Servidor detenido por el usuario", ColoredOutput.WARNING)
        sys.exit(0)

def print_banner():
    """Mostrar banner de inicio"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                     🚀 GEOVIGIA DEMO 🚀                     ║
║                                                              ║
║  Sistema de Vigilancia Inteligente - Demostración Interactiva ║
║                                                              ║
║  ✅ Demo completa sin dependencias                           ║
║  ✅ Interfaz moderna y responsive                            ║
║  ✅ Funcionalidades en tiempo real                           ║
║  ✅ Mapas interactivos con Leaflet                           ║
║  ✅ Analytics y reportes avanzados                           ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
"""
    print_colored(banner, ColoredOutput.HEADER)

def print_instructions():
    """Mostrar instrucciones de uso"""
    instructions = """
🎯 INSTRUCCIONES DE USO:

1. 🔐 Probar Login
   - Click en "Probar Login" para ver la pantalla de autenticación
   - Credenciales disponibles: operador/operador123, guardia/guardia123, cliente/cliente123

2. 👥 Gestión de Usuarios
   - Ver lista de usuarios del sistema
   - Diferentes roles: Operador, Guardia, Cliente

3. 🏠 Gestión de Propiedades
   - Mapa interactivo con propiedades marcadas
   - Información detallada de cada propiedad

4. 🛣️ Gestión de Rutas
   - Rutas de patrullaje predefinidas
   - Visualización en mapa

5. 🗺️ Mapas Interactivos
   - Mapas en tiempo real con Leaflet
   - Marcadores y controles interactivos

6. 👮 Monitoreo
   - Estado de guardias en tiempo real
   - Sistema de alertas y notificaciones

7. 📊 Analytics
   - Gráficos y estadísticas avanzadas
   - Heatmaps y visualizaciones

🔧 CONTROLES:
- Ctrl+C para detener el servidor
- F5 para recargar la demo
- F12 para abrir herramientas de desarrollador

📱 URL: http://localhost:3000/demo.html
"""
    print_colored(instructions, ColoredOutput.OKCYAN)

def signal_handler(signum, frame):
    """Manejar señal de interrupción"""
    print_colored("\n🛑 Deteniendo demo...", ColoredOutput.WARNING)
    print_colored("👋 ¡Gracias por usar GeoVigia Demo!", ColoredOutput.OKGREEN)
    sys.exit(0)

def main():
    """Función principal"""
    # Configurar manejo de señales
    signal.signal(signal.SIGINT, signal_handler)
    
    # Mostrar banner
    print_banner()
    
    # Verificar requisitos
    if not check_frontend():
        sys.exit(1)
    
    # Mostrar instrucciones
    print_instructions()
    
    # Ejecutar servidor
    try:
        run_frontend_server()
    except Exception as e:
        print_colored(f"❌ Error inesperado: {e}", ColoredOutput.FAIL)
        sys.exit(1)

if __name__ == "__main__":
    main()

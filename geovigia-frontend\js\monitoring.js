/**
 * Módulo de Monitoreo en Tiempo Real para GeoVigia Frontend
 */

class MonitoringModule {
    constructor() {
        this.activeExecutions = [];
        this.refreshInterval = null;
    }

    /**
     * Carga el módulo de monitoreo
     */
    async load() {
        try {
            await this.loadActiveExecutions();
            this.render();
            this.startRealTimeUpdates();
        } catch (error) {
            console.error('Error cargando monitoreo:', error);
            GeoVigia.Components.Toast.show(
                'error',
                'Error',
                'Error al cargar el monitoreo'
            );
        }
    }

    /**
     * Carga las ejecuciones activas
     */
    async loadActiveExecutions() {
        try {
            const response = await GeoVigia.API.Routes.getExecutions({
                status__in: 'iniciada,en_progreso'
            });
            this.activeExecutions = Array.isArray(response) ? response : (response.results || []);
        } catch (error) {
            console.error('Error cargando ejecuciones activas:', error);
            this.activeExecutions = [];
        }
    }

    /**
     * Renderiza el módulo de monitoreo
     */
    render() {
        const container = document.getElementById('monitoring-page');
        if (!container) return;

        container.innerHTML = `
            <div class="monitoring-header">
                <div class="monitoring-stats">
                    <div class="stat-item">
                        <span class="stat-value" id="active-executions-count">${this.activeExecutions.length}</span>
                        <span class="stat-label">Rutas Activas</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="on-time-count">${this.getOnTimeCount()}</span>
                        <span class="stat-label">A Tiempo</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="delayed-count">${this.getDelayedCount()}</span>
                        <span class="stat-label">Retrasadas</span>
                    </div>
                </div>
                
                <div class="monitoring-actions">
                    <button class="btn btn-ghost" id="refresh-monitoring-btn">
                        <i class="fas fa-sync-alt"></i>
                        Actualizar
                    </button>
                    <div class="auto-refresh-indicator">
                        <i class="fas fa-circle text-success"></i>
                        Actualización automática activa
                    </div>
                </div>
            </div>

            <div class="monitoring-content">
                <div id="active-executions-container">
                    ${this.renderActiveExecutions()}
                </div>
            </div>
        `;

        this.setupEventListeners();
    }

    /**
     * Renderiza las ejecuciones activas
     */
    renderActiveExecutions() {
        if (this.activeExecutions.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-eye"></i>
                    <h3>Sin rutas activas</h3>
                    <p>No hay rutas ejecutándose en este momento</p>
                </div>
            `;
        }

        return `
            <div class="executions-grid">
                ${this.activeExecutions.map(execution => this.renderExecutionCard(execution)).join('')}
            </div>
        `;
    }

    /**
     * Renderiza una tarjeta de ejecución
     */
    renderExecutionCard(execution) {
        const isDelayed = execution.is_delayed;
        const completionPercentage = execution.completion_percentage || 0;
        const duration = execution.duration_minutes || 0;

        return `
            <div class="execution-card ${isDelayed ? 'delayed' : ''}">
                <div class="execution-header">
                    <h4>${execution.route_name}</h4>
                    <span class="status-badge ${execution.status}">
                        ${GeoVigia.Utils.Format.capitalize(execution.status)}
                    </span>
                </div>
                
                <div class="execution-info">
                    <div class="info-item">
                        <i class="fas fa-user"></i>
                        <span>${execution.guard_username}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-clock"></i>
                        <span>${GeoVigia.Utils.Date.formatTime(execution.start_time)}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-route"></i>
                        <span>${execution.checkpoints_visited}/${execution.checkpoints_total} puntos</span>
                    </div>
                </div>
                
                <div class="execution-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${completionPercentage}%"></div>
                    </div>
                    <span class="progress-text">${completionPercentage.toFixed(1)}% completado</span>
                </div>
                
                <div class="execution-actions">
                    <button class="btn btn-sm btn-ghost" onclick="GeoVigia.Monitoring.viewExecution(${execution.id})">
                        <i class="fas fa-eye"></i>
                        Ver Detalles
                    </button>
                    ${isDelayed ? `
                        <button class="btn btn-sm btn-warning" onclick="GeoVigia.Monitoring.alertGuard(${execution.id})">
                            <i class="fas fa-exclamation-triangle"></i>
                            Alertar
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Configura los event listeners
     */
    setupEventListeners() {
        const refreshBtn = document.getElementById('refresh-monitoring-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refresh());
        }
    }

    /**
     * Obtiene el número de ejecuciones a tiempo
     */
    getOnTimeCount() {
        return this.activeExecutions.filter(execution => execution.is_on_time).length;
    }

    /**
     * Obtiene el número de ejecuciones retrasadas
     */
    getDelayedCount() {
        return this.activeExecutions.filter(execution => execution.is_delayed).length;
    }

    /**
     * Ve los detalles de una ejecución
     */
    async viewExecution(executionId) {
        try {
            const execution = await GeoVigia.API.Routes.getExecution(executionId);
            
            const content = `
                <div class="execution-details">
                    <div class="detail-row">
                        <strong>Ruta:</strong> ${execution.route_name}
                    </div>
                    <div class="detail-row">
                        <strong>Guardia:</strong> ${execution.guard_username}
                    </div>
                    <div class="detail-row">
                        <strong>Estado:</strong> ${GeoVigia.Utils.Format.capitalize(execution.status)}
                    </div>
                    <div class="detail-row">
                        <strong>Inicio:</strong> ${GeoVigia.Utils.Date.formatDateTime(execution.start_time)}
                    </div>
                    <div class="detail-row">
                        <strong>Fin planificado:</strong> ${GeoVigia.Utils.Date.formatDateTime(execution.planned_end_time)}
                    </div>
                    <div class="detail-row">
                        <strong>Progreso:</strong> ${execution.checkpoints_visited}/${execution.checkpoints_total} puntos (${execution.completion_percentage.toFixed(1)}%)
                    </div>
                    <div class="detail-row">
                        <strong>Propiedades:</strong> ${execution.properties_visited}/${execution.properties_total}
                    </div>
                    <div class="detail-row">
                        <strong>Duración:</strong> ${execution.duration_minutes || 0} minutos
                    </div>
                    <div class="detail-row">
                        <strong>Estado temporal:</strong> ${execution.is_delayed ? 'Retrasada' : 'A tiempo'}
                    </div>
                    ${execution.notes ? `<div class="detail-row"><strong>Notas:</strong> ${execution.notes}</div>` : ''}
                </div>
            `;

            await GeoVigia.Components.Modal.show('Detalles de Ejecución', content, {
                showCancel: false,
                confirmText: 'Cerrar'
            });
        } catch (error) {
            GeoVigia.Components.Toast.show('error', 'Error', 'Error al cargar los detalles de la ejecución');
        }
    }

    /**
     * Alerta a un guardia
     */
    async alertGuard(executionId) {
        const confirmed = await GeoVigia.Components.Modal.confirm(
            'Alertar Guardia',
            '¿Está seguro de que desea enviar una alerta al guardia sobre el retraso en la ruta?'
        );

        if (confirmed) {
            // Aquí se implementaría el sistema de alertas
            GeoVigia.Components.Toast.show(
                'success',
                'Alerta Enviada',
                'Se ha enviado una alerta al guardia'
            );
        }
    }

    /**
     * Inicia las actualizaciones en tiempo real
     */
    startRealTimeUpdates() {
        // Actualizar cada 30 segundos
        this.refreshInterval = setInterval(() => {
            this.refresh();
        }, 30000);
    }

    /**
     * Detiene las actualizaciones en tiempo real
     */
    stopRealTimeUpdates() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * Refresca los datos de monitoreo
     */
    async refresh() {
        await this.loadActiveExecutions();
        
        // Actualizar solo el contenido sin re-renderizar todo
        const container = document.getElementById('active-executions-container');
        if (container) {
            container.innerHTML = this.renderActiveExecutions();
        }

        // Actualizar estadísticas
        const activeCountElement = document.getElementById('active-executions-count');
        const onTimeCountElement = document.getElementById('on-time-count');
        const delayedCountElement = document.getElementById('delayed-count');

        if (activeCountElement) activeCountElement.textContent = this.activeExecutions.length;
        if (onTimeCountElement) onTimeCountElement.textContent = this.getOnTimeCount();
        if (delayedCountElement) delayedCountElement.textContent = this.getDelayedCount();
    }

    /**
     * Limpia recursos al salir del módulo
     */
    cleanup() {
        this.stopRealTimeUpdates();
    }
}

// Crear instancia global del módulo de monitoreo
window.GeoVigia.Monitoring = new MonitoringModule();

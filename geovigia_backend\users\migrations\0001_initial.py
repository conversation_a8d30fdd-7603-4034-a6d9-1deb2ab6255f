# Generated by Django 5.2.1 on 2025-05-28 22:46

import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('user_type', models.CharField(choices=[('cliente', 'Cliente'), ('guardia', 'Guardia'), ('operador', 'Operador')], default='cliente', max_length=10, verbose_name='Tipo de Usuario')),
                ('phone_number', models.CharField(blank=True, max_length=17, validators=[django.core.validators.RegexValidator(message="El número de teléfono debe estar en formato: '+*********'. Hasta 15 dígitos permitidos.", regex='^\\+?1?\\d{9,15}$')], verbose_name='Número de Teléfono')),
                ('address', models.TextField(blank=True, verbose_name='Dirección')),
                ('is_active_service', models.BooleanField(default=True, help_text='Indica si el usuario tiene el servicio activo', verbose_name='Servicio Activo')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Fecha de Creación')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Última Actualización')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'Usuario',
                'verbose_name_plural': 'Usuarios',
                'ordering': ['-created_at'],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='ClienteProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('emergency_contact_name', models.CharField(blank=True, max_length=100, verbose_name='Nombre Contacto de Emergencia')),
                ('emergency_contact_phone', models.CharField(blank=True, max_length=17, verbose_name='Teléfono Contacto de Emergencia')),
                ('home_alone_frequency', models.PositiveIntegerField(default=0, help_text='Número de veces que ha usado el botón "Casa sola"', verbose_name='Frecuencia Casa Sola')),
                ('user', models.OneToOneField(limit_choices_to={'user_type': 'cliente'}, on_delete=django.db.models.deletion.CASCADE, related_name='cliente_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Perfil de Cliente',
                'verbose_name_plural': 'Perfiles de Clientes',
            },
        ),
        migrations.CreateModel(
            name='GuardiaProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name='ID de Empleado')),
                ('shift_start', models.TimeField(verbose_name='Inicio de Turno')),
                ('shift_end', models.TimeField(verbose_name='Fin de Turno')),
                ('is_on_duty', models.BooleanField(default=False, verbose_name='En Servicio')),
                ('current_latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True, verbose_name='Latitud Actual')),
                ('current_longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True, verbose_name='Longitud Actual')),
                ('last_location_update', models.DateTimeField(blank=True, null=True, verbose_name='Última Actualización de Ubicación')),
                ('user', models.OneToOneField(limit_choices_to={'user_type': 'guardia'}, on_delete=django.db.models.deletion.CASCADE, related_name='guardia_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Perfil de Guardia',
                'verbose_name_plural': 'Perfiles de Guardias',
            },
        ),
        migrations.CreateModel(
            name='OperadorProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('department', models.CharField(default='Administración', max_length=50, verbose_name='Departamento')),
                ('can_manage_users', models.BooleanField(default=True, verbose_name='Puede Gestionar Usuarios')),
                ('can_manage_routes', models.BooleanField(default=True, verbose_name='Puede Gestionar Rutas')),
                ('can_view_reports', models.BooleanField(default=True, verbose_name='Puede Ver Reportes')),
                ('user', models.OneToOneField(limit_choices_to={'user_type': 'operador'}, on_delete=django.db.models.deletion.CASCADE, related_name='operador_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Perfil de Operador',
                'verbose_name_plural': 'Perfiles de Operadores',
            },
        ),
    ]

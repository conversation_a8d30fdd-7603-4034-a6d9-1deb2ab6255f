/**
 * Advanced Analytics CSS - Estilos para funcionalidades avanzadas
 * Incluye heatmaps, gráficos avanzados y widgets interactivos
 */

/* ===== SECCIÓN PRINCIPAL DE ANALYTICS ===== */
#analytics-section {
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.analytics-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.analytics-controls .btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.analytics-controls .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* ===== PLACEHOLDER DE ANALYTICS ===== */
.analytics-placeholder {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin: 20px 0;
}

.placeholder-content i {
    color: #667eea;
    margin-bottom: 20px;
}

.placeholder-content h3 {
    color: #2d3748;
    margin-bottom: 10px;
    font-size: 1.8rem;
}

.placeholder-content p {
    color: #718096;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.feature-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f7fafc;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.feature-item i {
    color: #667eea;
    font-size: 1.2rem;
}

.feature-item span {
    color: #4a5568;
    font-weight: 500;
}

/* ===== GRÁFICOS AVANZADOS ===== */
#advanced-charts-section {
    background: white;
    border-radius: 16px;
    padding: 25px;
    margin: 20px 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-top: 25px;
}

.chart-container {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e2e8f0;
}

.chart-header h3 {
    color: #2d3748;
    font-size: 1.2rem;
    margin: 0;
}

.chart-info {
    color: #718096;
    font-size: 0.9rem;
}

.chart-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.chart-controls select {
    padding: 8px 12px;
    border: 1px solid #cbd5e0;
    border-radius: 6px;
    background: white;
    color: #4a5568;
    font-size: 0.9rem;
}

/* ===== WIDGETS INTERACTIVOS ===== */
#widgets-section {
    background: white;
    border-radius: 16px;
    padding: 25px;
    margin: 20px 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.filters-panel {
    background: #f7fafc;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #e2e8f0;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.filter-group label {
    font-weight: 600;
    color: #4a5568;
    min-width: 120px;
}

.filter-group input,
.filter-group select {
    padding: 8px 12px;
    border: 1px solid #cbd5e0;
    border-radius: 6px;
    background: white;
    color: #4a5568;
    min-width: 150px;
}

.widgets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-top: 25px;
}

.widget-container {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.widget-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e2e8f0;
}

.widget-header h3 {
    color: #2d3748;
    font-size: 1.1rem;
    margin: 0;
}

.widget-actions {
    display: flex;
    gap: 8px;
}

.btn-drill-down,
.btn-export {
    background: #667eea;
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.btn-drill-down:hover,
.btn-export:hover {
    background: #5a67d8;
    transform: scale(1.05);
}

.widget-content {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== MÉTRICAS CLAVE ===== */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    width: 100%;
}

.metric-item {
    text-align: center;
    padding: 15px;
    background: white;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.metric-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 5px;
}

.metric-label {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 8px;
}

.metric-change {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
}

.metric-change.positive {
    background: #c6f6d5;
    color: #22543d;
}

.metric-change.negative {
    background: #fed7d7;
    color: #742a2a;
}

/* ===== MODALES ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 16px;
    padding: 0;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-header {
    background: #667eea;
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255,255,255,0.2);
}

.modal-body {
    padding: 25px;
    max-height: 70vh;
    overflow-y: auto;
}

/* ===== LOADING Y ESTADOS ===== */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    z-index: 10;
}

.loading-spinner {
    text-align: center;
    color: #667eea;
}

.loading-spinner i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.error {
    color: #e53e3e;
    text-align: center;
    padding: 20px;
    background: #fed7d7;
    border-radius: 8px;
    border: 1px solid #feb2b2;
}

/* ===== TOAST NOTIFICATIONS ===== */
.toast {
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    border-left: 4px solid #667eea;
    animation: slideIn 0.3s ease;
}

.toast-success {
    border-left-color: #38a169;
}

.toast-success i {
    color: #38a169;
}

.toast-error {
    border-left-color: #e53e3e;
}

.toast-error i {
    color: #e53e3e;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== TOP ZONAS ===== */
.top-zones-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
}

.zone-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.zone-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.zone-rank {
    font-size: 1.2rem;
    font-weight: 700;
    color: #667eea;
    min-width: 30px;
}

.zone-info {
    flex: 1;
}

.zone-name {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.zone-stats {
    font-size: 0.8rem;
    color: #718096;
}

.zone-activity {
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 100px;
}

.activity-bar {
    width: 60px;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.activity-fill {
    height: 100%;
    background: linear-gradient(90deg, #48bb78, #38a169);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.activity-percent {
    font-size: 0.8rem;
    font-weight: 600;
    color: #4a5568;
    min-width: 35px;
}

/* ===== GAUGE DE EFICIENCIA ===== */
.efficiency-gauge-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    width: 100%;
}

.gauge {
    position: relative;
    width: 120px;
    height: 60px;
}

.gauge-body {
    width: 120px;
    height: 60px;
    background: conic-gradient(from 180deg, #e2e8f0 0deg, #e2e8f0 180deg);
    border-radius: 120px 120px 0 0;
    position: relative;
    overflow: hidden;
}

.gauge-fill {
    position: absolute;
    top: 0;
    left: 0;
    width: 120px;
    height: 60px;
    background: conic-gradient(from 180deg, #48bb78 0deg, #38a169 90deg, #2f855a 180deg);
    border-radius: 120px 120px 0 0;
    transform-origin: 50% 100%;
    transition: transform 0.5s ease;
}

.gauge-cover {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 100px;
    height: 50px;
    background: #f8fafc;
    border-radius: 100px 100px 0 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.gauge-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
}

.gauge-label {
    font-size: 0.7rem;
    color: #718096;
    margin-top: 2px;
}

.efficiency-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.detail-label {
    font-size: 0.8rem;
    color: #718096;
}

.detail-value {
    font-size: 0.8rem;
    font-weight: 600;
    color: #2d3748;
}

/* ===== DRILL-DOWN ===== */
.drill-down-placeholder {
    text-align: center;
    padding: 40px 20px;
}

.drill-down-placeholder i {
    color: #667eea;
    margin-bottom: 20px;
}

.drill-down-placeholder h3 {
    color: #2d3748;
    margin-bottom: 10px;
}

.drill-down-placeholder p {
    color: #718096;
    margin-bottom: 20px;
}

.drill-down-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* ===== CONFIGURACIÓN ===== */
.config-section {
    margin-bottom: 25px;
}

.config-section h4 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.widget-toggles {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.widget-toggles label {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #f7fafc;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.widget-toggles label:hover {
    background: #edf2f7;
}

.widget-toggles input[type="checkbox"] {
    margin: 0;
}

.config-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .charts-grid,
    .widgets-grid {
        grid-template-columns: 1fr;
    }

    .analytics-controls {
        flex-direction: column;
    }

    .filter-group {
        flex-direction: column;
        align-items: flex-start;
    }

    .filter-group label {
        min-width: auto;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .zone-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .zone-activity {
        width: 100%;
        justify-content: space-between;
    }

    .activity-bar {
        flex: 1;
        max-width: 150px;
    }
}

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import Notification, PushSubscription, WebSocketConnection


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Admin para notificaciones"""
    
    list_display = [
        'title', 'recipient_link', 'sender_link', 'notification_type',
        'priority', 'is_read', 'is_sent', 'created_at'
    ]
    
    list_filter = [
        'notification_type', 'priority', 'is_read', 'is_sent',
        'created_at', 'recipient__user_type'
    ]
    
    search_fields = [
        'title', 'message', 'recipient__username', 'recipient__email',
        'sender__username', 'sender__email'
    ]
    
    readonly_fields = [
        'id', 'created_at', 'sent_at', 'read_at'
    ]
    
    fieldsets = (
        ('Información Básica', {
            'fields': ('id', 'title', 'message')
        }),
        ('Destinatario y Remitente', {
            'fields': ('recipient', 'sender')
        }),
        ('Configuración', {
            'fields': ('notification_type', 'priority', 'expires_at')
        }),
        ('Estado', {
            'fields': ('is_read', 'is_sent', 'created_at', 'sent_at', 'read_at')
        }),
        ('Datos Adicionales', {
            'fields': ('extra_data',),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['mark_as_read', 'mark_as_sent', 'delete_selected']
    
    def recipient_link(self, obj):
        """Link al usuario destinatario"""
        if obj.recipient:
            url = reverse('admin:users_user_change', args=[obj.recipient.pk])
            return format_html('<a href="{}">{}</a>', url, obj.recipient.username)
        return '-'
    recipient_link.short_description = 'Destinatario'
    
    def sender_link(self, obj):
        """Link al usuario remitente"""
        if obj.sender:
            url = reverse('admin:users_user_change', args=[obj.sender.pk])
            return format_html('<a href="{}">{}</a>', url, obj.sender.username)
        return 'Sistema'
    sender_link.short_description = 'Remitente'
    
    def mark_as_read(self, request, queryset):
        """Marca notificaciones como leídas"""
        updated = 0
        for notification in queryset:
            if not notification.is_read:
                notification.mark_as_read()
                updated += 1
        
        self.message_user(
            request,
            f'{updated} notificaciones marcadas como leídas.'
        )
    mark_as_read.short_description = 'Marcar como leídas'
    
    def mark_as_sent(self, request, queryset):
        """Marca notificaciones como enviadas"""
        updated = 0
        for notification in queryset:
            if not notification.is_sent:
                notification.mark_as_sent()
                updated += 1
        
        self.message_user(
            request,
            f'{updated} notificaciones marcadas como enviadas.'
        )
    mark_as_sent.short_description = 'Marcar como enviadas'


@admin.register(PushSubscription)
class PushSubscriptionAdmin(admin.ModelAdmin):
    """Admin para suscripciones push"""
    
    list_display = [
        'user_link', 'endpoint_short', 'is_active',
        'created_at', 'last_used'
    ]
    
    list_filter = [
        'is_active', 'created_at', 'last_used',
        'user__user_type'
    ]
    
    search_fields = [
        'user__username', 'user__email', 'endpoint'
    ]
    
    readonly_fields = [
        'created_at', 'last_used'
    ]
    
    fieldsets = (
        ('Usuario', {
            'fields': ('user',)
        }),
        ('Configuración Push', {
            'fields': ('endpoint', 'p256dh_key', 'auth_key')
        }),
        ('Estado', {
            'fields': ('is_active', 'created_at', 'last_used')
        }),
        ('Información Adicional', {
            'fields': ('user_agent',),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['activate_subscriptions', 'deactivate_subscriptions']
    
    def user_link(self, obj):
        """Link al usuario"""
        url = reverse('admin:users_user_change', args=[obj.user.pk])
        return format_html('<a href="{}">{}</a>', url, obj.user.username)
    user_link.short_description = 'Usuario'
    
    def endpoint_short(self, obj):
        """Endpoint acortado"""
        if len(obj.endpoint) > 50:
            return obj.endpoint[:47] + '...'
        return obj.endpoint
    endpoint_short.short_description = 'Endpoint'
    
    def activate_subscriptions(self, request, queryset):
        """Activa suscripciones"""
        updated = queryset.update(is_active=True)
        self.message_user(
            request,
            f'{updated} suscripciones activadas.'
        )
    activate_subscriptions.short_description = 'Activar suscripciones'
    
    def deactivate_subscriptions(self, request, queryset):
        """Desactiva suscripciones"""
        updated = queryset.update(is_active=False)
        self.message_user(
            request,
            f'{updated} suscripciones desactivadas.'
        )
    deactivate_subscriptions.short_description = 'Desactivar suscripciones'


@admin.register(WebSocketConnection)
class WebSocketConnectionAdmin(admin.ModelAdmin):
    """Admin para conexiones WebSocket"""
    
    list_display = [
        'user_link', 'channel_name_short', 'connected_at',
        'last_activity', 'ip_address'
    ]
    
    list_filter = [
        'connected_at', 'last_activity', 'user__user_type'
    ]
    
    search_fields = [
        'user__username', 'user__email', 'channel_name', 'ip_address'
    ]
    
    readonly_fields = [
        'user', 'channel_name', 'connected_at', 'last_activity',
        'user_agent', 'ip_address'
    ]
    
    fieldsets = (
        ('Conexión', {
            'fields': ('user', 'channel_name')
        }),
        ('Timestamps', {
            'fields': ('connected_at', 'last_activity')
        }),
        ('Información Técnica', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
    )
    
    def has_add_permission(self, request):
        """No permitir agregar conexiones manualmente"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """No permitir editar conexiones"""
        return False
    
    def user_link(self, obj):
        """Link al usuario"""
        url = reverse('admin:users_user_change', args=[obj.user.pk])
        return format_html('<a href="{}">{}</a>', url, obj.user.username)
    user_link.short_description = 'Usuario'
    
    def channel_name_short(self, obj):
        """Nombre del canal acortado"""
        if len(obj.channel_name) > 30:
            return obj.channel_name[:27] + '...'
        return obj.channel_name
    channel_name_short.short_description = 'Canal'


# Configuración del admin site
admin.site.site_header = 'GeoVigia - Administración'
admin.site.site_title = 'GeoVigia Admin'
admin.site.index_title = 'Panel de Administración'

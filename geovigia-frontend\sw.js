/**
 * Service Worker para GeoVigia
 * Maneja push notifications y funcionalidad offline
 */

const CACHE_NAME = 'geovigia-v1';
const urlsToCache = [
    '/',
    '/index.html',
    '/cliente-dashboard.html',
    '/guardia-dashboard.html',
    '/css/styles.css',
    '/css/components.css',
    '/css/cliente.css',
    '/css/guardia.css',
    '/js/config.js',
    '/js/components.js',
    '/js/maps.js',
    '/js/notifications.js',
    '/js/cliente-dashboard.js',
    '/js/guardia-dashboard.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css',
    'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js',
    'https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css',
    'https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js'
];

// Instalación del Service Worker
self.addEventListener('install', function(event) {
    console.log('🔧 Service Worker: Instalando...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                console.log('📦 Service Worker: Cache abierto');
                return cache.addAll(urlsToCache);
            })
            .catch(function(error) {
                console.error('❌ Service Worker: Error en instalación:', error);
            })
    );
});

// Activación del Service Worker
self.addEventListener('activate', function(event) {
    console.log('✅ Service Worker: Activado');
    
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        console.log('🗑️ Service Worker: Eliminando cache antiguo:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});

// Interceptar requests de red
self.addEventListener('fetch', function(event) {
    // Solo cachear requests GET
    if (event.request.method !== 'GET') {
        return;
    }
    
    // No cachear requests de API
    if (event.request.url.includes('/api/')) {
        return;
    }
    
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                // Devolver desde cache si existe
                if (response) {
                    return response;
                }
                
                // Hacer request de red
                return fetch(event.request).then(function(response) {
                    // Verificar respuesta válida
                    if (!response || response.status !== 200 || response.type !== 'basic') {
                        return response;
                    }
                    
                    // Clonar respuesta para cache
                    const responseToCache = response.clone();
                    
                    caches.open(CACHE_NAME)
                        .then(function(cache) {
                            cache.put(event.request, responseToCache);
                        });
                    
                    return response;
                });
            })
            .catch(function() {
                // Fallback para páginas offline
                if (event.request.destination === 'document') {
                    return caches.match('/index.html');
                }
            })
    );
});

// Manejar push notifications
self.addEventListener('push', function(event) {
    console.log('🔔 Service Worker: Push notification recibida');
    
    let notificationData = {};
    
    if (event.data) {
        try {
            notificationData = event.data.json();
        } catch (e) {
            notificationData = {
                title: 'GeoVigia',
                body: event.data.text() || 'Nueva notificación',
                icon: '/static/icons/geovigia-icon-192.png',
                badge: '/static/icons/geovigia-badge-72.png'
            };
        }
    } else {
        notificationData = {
            title: 'GeoVigia',
            body: 'Nueva notificación',
            icon: '/static/icons/geovigia-icon-192.png',
            badge: '/static/icons/geovigia-badge-72.png'
        };
    }
    
    const options = {
        body: notificationData.body || notificationData.message,
        icon: notificationData.icon || '/static/icons/geovigia-icon-192.png',
        badge: notificationData.badge || '/static/icons/geovigia-badge-72.png',
        tag: notificationData.tag || 'geovigia-notification',
        data: notificationData.data || {},
        actions: notificationData.actions || [
            {
                action: 'view',
                title: 'Ver',
                icon: '/static/icons/view-icon.png'
            },
            {
                action: 'close',
                title: 'Cerrar',
                icon: '/static/icons/close-icon.png'
            }
        ],
        requireInteraction: notificationData.requireInteraction || false,
        silent: notificationData.silent || false,
        vibrate: notificationData.vibrate || [200, 100, 200],
        timestamp: Date.now()
    };
    
    event.waitUntil(
        self.registration.showNotification(notificationData.title || 'GeoVigia', options)
    );
});

// Manejar clicks en notificaciones
self.addEventListener('notificationclick', function(event) {
    console.log('👆 Service Worker: Click en notificación');
    
    const notification = event.notification;
    const action = event.action;
    const data = notification.data || {};
    
    notification.close();
    
    if (action === 'close') {
        return;
    }
    
    // Determinar URL de destino
    let targetUrl = '/';
    
    if (data.url) {
        targetUrl = data.url;
    } else if (data.type) {
        switch (data.type) {
            case 'emergency':
            case 'alert':
                targetUrl = '/#alerts';
                break;
            case 'route_update':
                targetUrl = '/guardia-dashboard.html';
                break;
            case 'property_alert':
                targetUrl = '/cliente-dashboard.html';
                break;
            default:
                targetUrl = '/#notifications';
        }
    }
    
    event.waitUntil(
        clients.matchAll({
            type: 'window',
            includeUncontrolled: true
        }).then(function(clientList) {
            // Buscar ventana existente
            for (let i = 0; i < clientList.length; i++) {
                const client = clientList[i];
                if (client.url.includes(self.location.origin)) {
                    // Enfocar ventana existente y navegar
                    client.focus();
                    client.postMessage({
                        type: 'notification_click',
                        action: action,
                        data: data,
                        targetUrl: targetUrl
                    });
                    return;
                }
            }
            
            // Abrir nueva ventana
            return clients.openWindow(targetUrl);
        })
    );
});

// Manejar cierre de notificaciones
self.addEventListener('notificationclose', function(event) {
    console.log('❌ Service Worker: Notificación cerrada');
    
    const notification = event.notification;
    const data = notification.data || {};
    
    // Enviar estadística de cierre si es necesario
    if (data.notification_id) {
        // TODO: Enviar estadística al servidor
        console.log('📊 Notificación cerrada:', data.notification_id);
    }
});

// Manejar mensajes desde la aplicación principal
self.addEventListener('message', function(event) {
    console.log('💬 Service Worker: Mensaje recibido:', event.data);
    
    const data = event.data;
    
    switch (data.type) {
        case 'skip_waiting':
            self.skipWaiting();
            break;
            
        case 'get_version':
            event.ports[0].postMessage({
                version: CACHE_NAME
            });
            break;
            
        case 'clear_cache':
            caches.delete(CACHE_NAME).then(function() {
                event.ports[0].postMessage({
                    success: true
                });
            });
            break;
            
        case 'update_cache':
            caches.open(CACHE_NAME).then(function(cache) {
                return cache.addAll(urlsToCache);
            }).then(function() {
                event.ports[0].postMessage({
                    success: true
                });
            });
            break;
    }
});

// Manejar sincronización en background
self.addEventListener('sync', function(event) {
    console.log('🔄 Service Worker: Background sync:', event.tag);
    
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

// Función de sincronización en background
function doBackgroundSync() {
    return new Promise(function(resolve) {
        // TODO: Implementar sincronización de datos offline
        console.log('🔄 Ejecutando sincronización en background...');
        
        // Simular trabajo de sincronización
        setTimeout(function() {
            console.log('✅ Sincronización completada');
            resolve();
        }, 1000);
    });
}

// Manejar actualizaciones del Service Worker
self.addEventListener('updatefound', function(event) {
    console.log('🔄 Service Worker: Actualización encontrada');
    
    const newWorker = event.target.installing;
    
    newWorker.addEventListener('statechange', function() {
        if (newWorker.state === 'installed') {
            if (navigator.serviceWorker.controller) {
                console.log('🆕 Service Worker: Nueva versión disponible');
                
                // Notificar a la aplicación principal
                self.clients.matchAll().then(function(clients) {
                    clients.forEach(function(client) {
                        client.postMessage({
                            type: 'sw_update_available'
                        });
                    });
                });
            } else {
                console.log('✅ Service Worker: Instalado por primera vez');
            }
        }
    });
});

console.log('🚀 Service Worker de GeoVigia cargado');

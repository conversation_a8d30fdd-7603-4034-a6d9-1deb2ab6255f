# Módulo de Rutas - Sistema GeoVigia

## Descripción General

El **Módulo de <PERSON>utas** es el tercer componente principal del sistema GeoVigia, diseñado para gestionar rutas de vigilancia, asignaciones de guardias, puntos de control y seguimiento de ejecuciones. Este módulo permite a los operadores crear y administrar rutas de patrullaje, asignar guardias específicos y monitorear el cumplimiento de las rutas en tiempo real.

## Características Principales

### 🛣️ Gestión de Rutas
- **Creación y configuración** de rutas de vigilancia
- **Horarios flexibles** con soporte para diferentes frecuencias
- **Configuración de días** de la semana activos
- **Niveles de prioridad** (1-5, donde 5 es crítica)
- **Rutas de emergencia** con configuraciones especiales
- **Estimación de duración** y configuración de desviaciones máximas

### 👮 Asignación de Guardias
- **Asignación múltiple** de guardias a rutas
- **Guardias principales** y secundarios
- **Fechas de inicio y fin** de asignaciones
- **Estados de asignación** (activa, temporal, suspendida, finalizada)
- **Historial de asignaciones** completo

### 📍 Puntos de Control
- **Puntos georreferenciados** con coordenadas precisas
- **Tipos de puntos** (inicio, intermedio, fin, emergencia, propiedad)
- **Radio de detección** configurable
- **Orden secuencial** en la ruta
- **Puntos obligatorios** y opcionales
- **Asociación con propiedades** específicas

### 📊 Seguimiento y Ejecución
- **Inicio y finalización** de rutas por guardias
- **Métricas de cumplimiento** en tiempo real
- **Detección de retrasos** automática
- **Registro de incidentes** durante la ruta
- **Porcentaje de completitud** de puntos de control
- **Historial completo** de ejecuciones

## Modelos de Datos

### Route (Ruta)
```python
- name: Nombre de la ruta
- description: Descripción detallada
- status: Estado (activa, inactiva, mantenimiento, suspendida)
- frequency: Frecuencia (diaria, semanal, quincenal, mensual, personalizada)
- start_time/end_time: Horarios de inicio y fin
- estimated_duration_minutes: Duración estimada
- max_deviation_meters: Desviación máxima permitida
- priority_level: Nivel de prioridad (1-5)
- is_emergency_route: Indicador de ruta de emergencia
- Días de la semana: monday, tuesday, wednesday, etc.
```

### RouteProperty (Propiedades en Ruta)
```python
- route: Referencia a la ruta
- property: Referencia a la propiedad
- order: Orden de visita
- estimated_time_minutes: Tiempo estimado de inspección
- is_mandatory: Visita obligatoria
- notes: Notas específicas
```

### GuardRoute (Asignación de Guardias)
```python
- guard: Referencia al guardia
- route: Referencia a la ruta
- status: Estado de asignación
- start_date/end_date: Fechas de asignación
- is_primary_guard: Guardia principal
- assigned_by: Operador que realizó la asignación
```

### RouteCheckpoint (Puntos de Control)
```python
- route: Referencia a la ruta
- name: Nombre del punto
- checkpoint_type: Tipo de punto
- latitude/longitude: Coordenadas geográficas
- order: Orden en la ruta
- radius_meters: Radio de detección
- is_mandatory: Punto obligatorio
- related_property: Propiedad asociada (opcional)
```

### RouteExecution (Ejecución de Rutas)
```python
- route: Referencia a la ruta
- guard: Guardia ejecutor
- status: Estado de ejecución
- start_time/end_time: Tiempos de ejecución
- planned_start_time/planned_end_time: Tiempos planificados
- checkpoints_visited/total: Métricas de puntos
- properties_visited/total: Métricas de propiedades
- notes: Observaciones del guardia
- incidents_reported: Número de incidentes
```

## APIs Disponibles

### Gestión de Rutas
- `GET /api/routes/` - Listar rutas
- `POST /api/routes/` - Crear nueva ruta
- `GET /api/routes/{id}/` - Obtener ruta específica
- `PUT /api/routes/{id}/` - Actualizar ruta
- `DELETE /api/routes/{id}/` - Eliminar ruta
- `GET /api/routes/my-routes/` - Rutas del guardia autenticado
- `GET /api/routes/active/` - Rutas activas
- `GET /api/routes/today/` - Rutas activas para hoy

### Asignaciones de Guardias
- `GET /api/routes/assignments/` - Listar asignaciones
- `POST /api/routes/assignments/` - Crear asignación
- `GET /api/routes/assignments/{id}/` - Obtener asignación específica
- `PUT /api/routes/assignments/{id}/` - Actualizar asignación
- `DELETE /api/routes/assignments/{id}/` - Eliminar asignación
- `POST /api/routes/{route_id}/assign/` - Asignar guardia a ruta

### Puntos de Control
- `GET /api/routes/checkpoints/` - Listar puntos de control
- `POST /api/routes/checkpoints/` - Crear punto de control
- `GET /api/routes/checkpoints/{id}/` - Obtener punto específico
- `PUT /api/routes/checkpoints/{id}/` - Actualizar punto
- `DELETE /api/routes/checkpoints/{id}/` - Eliminar punto
- `GET /api/routes/{route_id}/checkpoints/` - Puntos de una ruta específica

### Ejecución de Rutas
- `GET /api/routes/executions/` - Listar ejecuciones
- `POST /api/routes/executions/` - Crear ejecución
- `POST /api/routes/{route_id}/start/` - Iniciar ejecución de ruta
- `POST /api/routes/executions/{execution_id}/complete/` - Completar ejecución

## Permisos y Seguridad

### Operadores
- **Acceso completo** a todas las funcionalidades
- Pueden **crear, modificar y eliminar** rutas
- Pueden **asignar guardias** a rutas
- Pueden **crear y gestionar** puntos de control
- Pueden **ver todas las ejecuciones** del sistema

### Guardias
- **Solo pueden ver** sus rutas asignadas
- Pueden **iniciar y completar** ejecuciones de sus rutas
- Pueden **ver puntos de control** de sus rutas asignadas
- **No pueden modificar** configuraciones de rutas
- Pueden **reportar incidentes** durante ejecuciones

### Clientes
- **Sin acceso** al módulo de rutas
- Las rutas pueden incluir sus propiedades para vigilancia

## Casos de Uso

### 1. Creación de Ruta Matutina
```python
# Operador crea una ruta de patrullaje matutino
ruta = {
    "name": "Ruta Matutina Centro",
    "description": "Recorrido matutino por el centro de la ciudad",
    "frequency": "diaria",
    "start_time": "08:00",
    "end_time": "12:00",
    "estimated_duration_minutes": 240,
    "priority_level": 3,
    "monday": True,
    "tuesday": True,
    "wednesday": True,
    "thursday": True,
    "friday": True,
    "saturday": False,
    "sunday": False
}
```

### 2. Asignación de Guardia
```python
# Operador asigna un guardia a la ruta
asignacion = {
    "guard_id": 123,
    "start_date": "2025-05-29",
    "end_date": "2025-06-29",
    "is_primary_guard": True
}
```

### 3. Ejecución de Ruta por Guardia
```python
# Guardia inicia la ruta
POST /api/routes/5/start/

# Guardia completa la ruta
POST /api/routes/executions/1/complete/
{
    "notes": "Recorrido completado sin incidentes",
    "incidents_reported": 0
}
```

## Pruebas

El módulo incluye un script de pruebas completo (`test_routes_apis.py`) que verifica:

- ✅ **Creación de rutas** con diferentes configuraciones
- ✅ **Asignación de guardias** a rutas específicas
- ✅ **Creación de puntos de control** georreferenciados
- ✅ **Inicio y finalización** de ejecuciones de rutas
- ✅ **Obtención de rutas activas** para el día actual
- ✅ **Permisos y autenticación** correctos

## Integración con Otros Módulos

### Con Módulo de Usuarios
- **Autenticación** de operadores y guardias
- **Permisos específicos** por tipo de usuario
- **Asignaciones** basadas en roles

### Con Módulo de Propiedades
- **Inclusión de propiedades** en rutas de vigilancia
- **Puntos de control** asociados a propiedades específicas
- **Coordinación** entre vigilancia y propiedades

## Próximas Funcionalidades

- 🗺️ **Integración con mapas** para visualización de rutas
- 📱 **Aplicación móvil** para guardias
- 🔔 **Notificaciones en tiempo real** de estado de rutas
- 📈 **Reportes y analytics** de cumplimiento
- 🚨 **Alertas automáticas** por retrasos o desviaciones
- 📍 **Tracking GPS** en tiempo real de guardias

## Estado del Desarrollo

✅ **Completado**: Modelos, APIs, Serializers, Vistas, Admin, Pruebas
🔄 **En desarrollo**: Interfaz web, aplicación móvil
📋 **Pendiente**: Integración con mapas, notificaciones push

---

**Desarrollado para el Sistema GeoVigia**  
*Módulo de gestión integral de rutas de vigilancia*

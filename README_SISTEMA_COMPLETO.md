# 🚀 Sistema Completo GeoVigia - Guía de Ejecución

## 📋 Resumen

El archivo `run_geovigia.py` es el **punto de entrada principal** para ejecutar el sistema completo de GeoVigia con un solo comando. Este script reemplaza la necesidad de ejecutar manualmente el backend y frontend por separado.

## ✅ Características del Script

### 🔧 **Funcionalidades Principales**
- ✅ **Inicio automático** del backend Django (puerto 5021)
- ✅ **Inicio automático** del frontend web (puerto 5022)
- ✅ **Verificación de requisitos** antes del inicio
- ✅ **Instalación automática** de dependencias si es necesario
- ✅ **Detección de puertos** disponibles
- ✅ **Aplicación de migraciones** automática
- ✅ **Apertura automática** del navegador
- ✅ **Logs detallados** con colores para mejor seguimiento
- ✅ **Manejo de errores** robusto
- ✅ **Detención limpia** con Ctrl+C

### 🛡️ **Verificaciones de Seguridad**
- ✅ Verificación de versión de Python (3.8+)
- ✅ Verificación de existencia de directorios
- ✅ Verificación de archivos críticos (manage.py, index.html)
- ✅ Verificación de puertos disponibles
- ✅ Verificación de dependencias instaladas

## 🚀 Uso

### **Comando Principal**
```bash
python run_geovigia.py
```

### **Requisitos Previos**
- Python 3.8 o superior
- Entorno virtual activado (recomendado)
- Archivo `requirements.txt` presente

### **Proceso de Inicio**
1. **Verificación**: El script verifica todos los requisitos
2. **Dependencias**: Instala dependencias automáticamente si es necesario
3. **Migraciones**: Aplica migraciones de base de datos
4. **Backend**: Inicia el servidor Django en puerto 5021
5. **Frontend**: Inicia el servidor web en puerto 5022
6. **Navegador**: Abre automáticamente http://localhost:5022
7. **Monitoreo**: Muestra logs de ambos servicios en tiempo real

## 🌐 URLs de Acceso

Una vez iniciado el sistema:

| Servicio | URL | Descripción |
|----------|-----|-------------|
| **Sistema Principal** | http://localhost:5022 | Interfaz principal del sistema |
| **Backend API** | http://127.0.0.1:5021 | APIs REST del sistema |
| **Admin Django** | http://127.0.0.1:5021/admin | Panel de administración |

## 🔑 Credenciales de Acceso

| Rol | Usuario | Contraseña | Descripción |
|-----|---------|------------|-------------|
| **👨‍💼 Operador** | `operador` | `operador123` | Acceso completo al sistema |
| **👮‍♂️ Guardia** | `guardia` | `guardia123` | Dashboard de guardia |
| **👤 Cliente** | `cliente` | `cliente123` | Dashboard de cliente |
| **🔧 Admin** | `admin` | `admin123` | Panel de administración Django |

## 🎯 Funcionalidades Disponibles

### **📊 Dashboard Principal**
- Estadísticas en tiempo real
- KPIs del sistema
- Acciones rápidas
- Navegación intuitiva

### **👥 Gestión de Usuarios**
- CRUD completo de usuarios
- Roles diferenciados
- Estados de servicio
- Ubicación GPS de guardias

### **🏠 Gestión de Propiedades**
- Registro de propiedades
- Geolocalización
- Perímetros de seguridad
- Asignación a clientes

### **🛣️ Sistema de Rutas**
- Creación de rutas de patrullaje
- Puntos de control
- Asignación de guardias
- Seguimiento en tiempo real

### **🗺️ Mapas Interactivos**
- Mapas con Leaflet
- Marcadores dinámicos
- Herramientas de dibujo
- Múltiples capas

### **🔔 Notificaciones**
- WebSockets en tiempo real
- Push notifications
- Diferentes tipos y prioridades
- Historial completo

### **📈 Analytics Avanzados**
- Gráficos interactivos
- Mapas de calor (heatmaps)
- Widgets personalizables
- Exportación de datos

## 🔧 Configuración

### **Puertos Configurables**
```python
BACKEND_PORT = 5021    # Puerto del servidor Django
FRONTEND_PORT = 5022   # Puerto del servidor frontend
```

### **Directorios**
```python
BACKEND_DIR = "geovigia_backend"    # Directorio del backend
FRONTEND_DIR = "geovigia-frontend"  # Directorio del frontend
```

## 🛠️ Solución de Problemas

### **Error: Puerto en uso**
```
❌ Error: Puerto 5021 ya está en uso
💡 Solución: Cierra cualquier servidor Django anterior
```
**Solución**: Detén cualquier proceso que use los puertos 5021 o 5022

### **Error: Dependencias faltantes**
```
⚠️ Django no encontrado. Instalando dependencias...
```
**Solución**: El script instala automáticamente las dependencias

### **Error: Python versión incorrecta**
```
❌ Error: Se requiere Python 3.8 o superior
```
**Solución**: Actualiza Python o usa un entorno virtual con la versión correcta

### **Error: Archivos faltantes**
```
❌ Error: Directorio geovigia_backend no encontrado
```
**Solución**: Ejecuta el script desde la raíz del proyecto GeoVigia

## 🔄 Detener el Sistema

### **Método Recomendado**
Presiona `Ctrl+C` en la terminal donde ejecutaste el script

### **Detención Limpia**
El script maneja la señal de interrupción y detiene ambos servicios correctamente:
```
🛑 Deteniendo sistema GeoVigia...
👋 ¡Gracias por usar GeoVigia!
```

## 📝 Logs del Sistema

### **Colores de Logs**
- 🔧 **Azul**: Mensajes del backend Django
- 🌐 **Azul claro**: Mensajes del frontend
- ✅ **Verde**: Operaciones exitosas
- ⚠️ **Amarillo**: Advertencias
- ❌ **Rojo**: Errores
- 🔍 **Cian**: Información del sistema

### **Ejemplo de Logs**
```
🔍 Verificando requisitos del sistema...
✅ Todos los requisitos verificados correctamente
📦 Verificando dependencias...
✅ Django 5.2.1 encontrado
🔧 Iniciando backend Django en puerto 8000...
📊 Aplicando migraciones de base de datos...
✅ Backend Django iniciado correctamente
🌐 Iniciando servidor frontend en puerto 3000...
✅ Servidor frontend ejecutándose en http://localhost:3000
🌐 Navegador abierto automáticamente
```

## 🎯 Ventajas del Script

### **Comparación con Método Manual**

| Aspecto | Método Manual | Script Automático |
|---------|---------------|-------------------|
| **Comandos** | 2 terminales separadas | 1 comando único |
| **Verificaciones** | Manual | Automáticas |
| **Dependencias** | Manual | Automáticas |
| **Migraciones** | Manual | Automáticas |
| **Navegador** | Manual | Automático |
| **Logs** | Separados | Unificados con colores |
| **Detención** | 2 Ctrl+C | 1 Ctrl+C |
| **Puertos** | 8000 y 3000 | 5021 y 5022 |

### **Beneficios**
- ✅ **Simplicidad**: Un solo comando para todo
- ✅ **Robustez**: Verificaciones automáticas
- ✅ **Experiencia**: Mejor UX para desarrolladores
- ✅ **Mantenimiento**: Fácil de usar y mantener
- ✅ **Productividad**: Inicio rápido del desarrollo

## 📚 Archivos Relacionados

- `run_geovigia.py` - Script principal
- `requirements.txt` - Dependencias del proyecto
- `geovigia_backend/manage.py` - Comando Django
- `geovigia-frontend/index.html` - Interfaz principal
- `README.md` - Documentación principal del proyecto

---

**🎉 ¡Sistema GeoVigia listo para usar!**  
*Ejecuta `python run_geovigia.py` y comienza a trabajar inmediatamente*

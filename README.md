# GeoVigia - Sistema de Vigilancia Inteligente

Sistema completo de vigilancia con backend Django y frontend web.

## 🚀 Inicio Rápido

### 🎯 <PERSON><PERSON><PERSON>mple<PERSON> (Recomendado)

```bash
python run_geovigia.py
```
**¡Eso es todo!** El sistema completo se inicia automáticamente con backend y frontend.

**URL Principal**: http://localhost:5022

### 🖥️ Inicio Manual (Desarrollo)

```bash
# Terminal 1 - Backend
cd geovigia_backend
python manage.py runserver 127.0.0.1:5021

# Terminal 2 - Frontend (en otra terminal)
cd geovigia-frontend
python -m http.server 5022
```

## 📋 Requisitos

- Python 3.8+
- Entorno virtual (recomendado)

### Instalación de dependencias:

```bash
# Crear entorno virtual (recomendado)
python -m venv geo
geo\Scripts\activate  # Windows
# source geo/bin/activate  # Linux/Mac

# Instalar dependencias
pip install -r requirements.txt
```

## 🌐 Acceso

Una vez iniciado el sistema:

- **🖥️ Sistema Principal**: http://localhost:5022
- **⚙️ Backend API**: http://127.0.0.1:5021
- **🔧 Admin Django**: http://127.0.0.1:5021/admin

## 🔑 Credenciales de Acceso

**Sistema GeoVigia:**
- **👨‍💼 Operador**: `operador` / `operador123`
- **👮‍♂️ Guardia**: `guardia` / `guardia123`
- **👤 Cliente**: `cliente` / `cliente123`
- **🔧 Admin Django**: `admin` / `admin123`

### 🎨 Funcionalidades Destacadas
- ✅ **Mapas interactivos** con seguimiento GPS en tiempo real
- ✅ **Analytics avanzados** con gráficos y heatmaps
- ✅ **Notificaciones push** y WebSocket en tiempo real
- ✅ **Gestión de usuarios** con roles diferenciados
- ✅ **Sistema completo** backend + frontend integrado
- ✅ **APIs REST completas** para todas las funcionalidades

## 🎯 Crear Datos de Demostración

Para poblar el sistema con datos de ejemplo:

```bash
python create_demo_data.py
```

Este script crea automáticamente:
- ✅ Usuarios de demostración para cada rol
- ✅ Propiedades de ejemplo con ubicaciones reales
- ✅ Rutas de patrullaje predefinidas
- ✅ Notificaciones de ejemplo

## 📁 Estructura del Proyecto

```
GeoVigia/
├── run_geovigia.py             # 🚀 Launcher del sistema completo
├── create_demo_data.py         # 📊 Crear datos de demostración
├── README.md                   # 📖 Documentación principal
├── mejoras.md                  # 🗺️ Roadmap y mejoras futuras
├── requirements.txt            # 📦 Dependencias del proyecto
├── geo/                        # 🐍 Entorno virtual Python
├── geovigia_backend/           # 🔧 Backend Django
│   ├── manage.py
│   ├── geovigia_backend/       # Configuración Django
│   ├── users/                  # App de usuarios
│   ├── properties/             # App de propiedades
│   ├── routes/                 # App de rutas
│   └── notifications/          # App de notificaciones
└── geovigia-frontend/          # 🌐 Frontend web
    ├── index.html              # Dashboard principal
    ├── cliente-dashboard.html  # Dashboard de cliente
    ├── guardia-dashboard.html  # Dashboard de guardia
    ├── css/                    # Estilos CSS
    └── js/                     # Scripts JavaScript
```

## 🔧 Configuración

El script `run_geovigia.py` incluye configuración personalizable:

- `BACKEND_PORT = 5021` - Puerto del servidor Django
- `FRONTEND_PORT = 5022` - Puerto del servidor frontend
- `REDIS_PORT = 5023` - Puerto del servidor Redis (automático)
- Abre automáticamente el navegador en el sistema principal
- Verificación automática de dependencias y puertos
- Inicio automático de Redis integrado

## 🛠️ Desarrollo

### Instalar dependencias del backend
```bash
cd geovigia_backend
pip install django djangorestframework django-cors-headers
```

### Aplicar migraciones
```bash
cd geovigia_backend
python manage.py migrate
```

### Crear superusuario
```bash
cd geovigia_backend
python manage.py createsuperuser
```

## 📝 Características

- ✅ Gestión de usuarios
- ✅ Administración de propiedades
- ✅ Sistema de rutas
- ✅ Dashboard en tiempo real
- ✅ API REST completa
- ✅ Interfaz web responsive
- ✅ Autenticación por tokens

## 🚫 Detener Servicios

Presiona `Ctrl+C` en la terminal donde ejecutaste el script para detener ambos servicios.

## 📞 Soporte

Para problemas o preguntas, revisa los logs en la consola donde ejecutaste el script.

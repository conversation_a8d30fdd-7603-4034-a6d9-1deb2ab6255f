# GeoVigia - Sistema de Vigilancia Inteligente

Sistema completo de vigilancia con backend Django y frontend web.

## 🚀 Inicio Rápido

### 🎯 Demo Interactivo (Recomendado)

```bash
python demo.py
```
**¡Eso es todo!** La demo se abre automáticamente en tu navegador.

**Demo URL**: http://localhost:3000/demo.html

### 🖥️ Sistema Completo (Desarrollo)

```bash
# Terminal 1 - Backend
cd geovigia_backend
python manage.py runserver 127.0.0.1:8000

# Terminal 2 - Frontend (en otra terminal)
cd geovigia-frontend
python -m http.server 3000
```

## 📋 Requisitos

- Python 3.8+
- Entorno virtual (recomendado)

### Instalación de dependencias:

```bash
# Crear entorno virtual (recomendado)
python -m venv geo
geo\Scripts\activate  # Windows
# source geo/bin/activate  # Linux/Mac

# Instalar dependencias
pip install -r requirements.txt
```

## 🌐 Acceso

Una vez iniciado el sistema:

- **🎯 Demo Interactiva**: http://localhost:3000/demo.html
- **🖥️ Sistema Principal**: http://localhost:3000/index.html
- **⚙️ Backend API**: http://127.0.0.1:8000 (opcional)
- **🔧 Admin Django**: http://127.0.0.1:8000/admin (opcional)

## 🎯 Demo Interactivo para Cliente

### 🔑 Credenciales de Acceso

**Para Demo Interactiva:**
- **👨‍💼 Operador**: `demo_operador` / `demo123`
- **👮‍♂️ Guardia**: `demo_guardia1` / `demo123`
- **👤 Cliente**: `demo_cliente1` / `demo123`

**Para Sistema Completo:**
- **👨‍💼 Operador**: `operador` / `operador123`
- **👮‍♂️ Guardia**: `guardia` / `guardia123`
- **👤 Cliente**: `cliente` / `cliente123`

### 🎨 Funcionalidades Destacadas
- ✅ **Mapas interactivos** con seguimiento GPS en tiempo real
- ✅ **Analytics avanzados** con gráficos y heatmaps
- ✅ **Notificaciones push** y WebSocket en tiempo real
- ✅ **Gestión de usuarios** con roles diferenciados
- ✅ **Navegación fluida** al sistema completo
- ✅ **Datos realistas** para demostración convincente

## 🎯 Crear Datos de Demostración

Para usar la demo interactiva con datos realistas:

```bash
python create_demo_data.py
```

Este script crea automáticamente:
- ✅ Usuarios de demostración para cada rol
- ✅ Propiedades de ejemplo con ubicaciones reales
- ✅ Rutas de patrullaje predefinidas
- ✅ Notificaciones de ejemplo

## 📁 Estructura del Proyecto

```
GeoVigia/
├── demo.py                     # 🚀 Launcher de demo interactiva
├── create_demo_data.py         # 📊 Crear datos de demostración
├── README.md                   # 📖 Documentación principal
├── mejoras.md                  # 🗺️ Roadmap y mejoras futuras
├── requirements.txt            # 📦 Dependencias del proyecto
├── geo/                        # 🐍 Entorno virtual Python
├── geovigia_backend/           # 🔧 Backend Django
│   ├── manage.py
│   ├── geovigia_backend/       # Configuración Django
│   ├── users/                  # App de usuarios
│   ├── properties/             # App de propiedades
│   ├── routes/                 # App de rutas
│   └── notifications/          # App de notificaciones
└── geovigia-frontend/          # 🌐 Frontend web
    ├── demo.html               # 🎯 Demo interactiva
    ├── index.html              # Dashboard principal
    ├── css/                    # Estilos CSS
    └── js/                     # Scripts JavaScript
```

## 🔧 Configuración

El script `demo.py` incluye configuración personalizable:

- `FRONTEND_PORT = 3000` - Puerto del servidor de demo
- Abre automáticamente el navegador en la demo

## 🛠️ Desarrollo

### Instalar dependencias del backend
```bash
cd geovigia_backend
pip install django djangorestframework django-cors-headers
```

### Aplicar migraciones
```bash
cd geovigia_backend
python manage.py migrate
```

### Crear superusuario
```bash
cd geovigia_backend
python manage.py createsuperuser
```

## 📝 Características

- ✅ Gestión de usuarios
- ✅ Administración de propiedades
- ✅ Sistema de rutas
- ✅ Dashboard en tiempo real
- ✅ API REST completa
- ✅ Interfaz web responsive
- ✅ Autenticación por tokens

## 🚫 Detener Servicios

Presiona `Ctrl+C` en la terminal donde ejecutaste el script para detener ambos servicios.

## 📞 Soporte

Para problemas o preguntas, revisa los logs en la consola donde ejecutaste el script.

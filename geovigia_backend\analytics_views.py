"""
Vista consolidada para analytics del dashboard
"""
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Count, Q
from datetime import datetime, timedelta
import requests
from django.conf import settings


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def dashboard_analytics(request):
    """
    Vista consolidada que obtiene todas las estadísticas para el dashboard de analytics
    """
    if not request.user.is_operador:
        return Response({
            'error': 'Solo los operadores pueden acceder a las estadísticas'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Obtener estadísticas de cada módulo
        base_url = f"http://127.0.0.1:8000/api"
        headers = {'Authorization': f'Token {request.auth}'}

        # Inicializar respuesta
        analytics_data = {
            'timestamp': timezone.now().isoformat(),
            'users': {},
            'properties': {},
            'notifications': {},
            'routes': {},
            'summary': {}
        }

        # Estadísticas de usuarios
        try:
            users_response = requests.get(
                f"{base_url}/users/analytics/stats/",
                headers=headers,
                timeout=5
            )
            if users_response.status_code == 200:
                analytics_data['users'] = users_response.json()
        except Exception as e:
            analytics_data['users'] = {'error': str(e)}

        # Estadísticas de propiedades
        try:
            properties_response = requests.get(
                f"{base_url}/properties/analytics/stats/",
                headers=headers,
                timeout=5
            )
            if properties_response.status_code == 200:
                analytics_data['properties'] = properties_response.json()
        except Exception as e:
            analytics_data['properties'] = {'error': str(e)}

        # Estadísticas de notificaciones
        try:
            notifications_response = requests.get(
                f"{base_url}/notifications/analytics/stats/",
                headers=headers,
                timeout=5
            )
            if notifications_response.status_code == 200:
                analytics_data['notifications'] = notifications_response.json()
        except Exception as e:
            analytics_data['notifications'] = {'error': str(e)}

        # Crear resumen consolidado para KPIs principales
        summary = {}

        # KPI 1: Guardias Activos
        if 'guardias' in analytics_data['users']:
            summary['active_guards'] = analytics_data['users']['guardias'].get('on_duty', 0)
        else:
            summary['active_guards'] = 0

        # KPI 2: Propiedades Monitoreadas
        if 'properties' in analytics_data['properties']:
            summary['monitored_properties'] = analytics_data['properties']['properties'].get('monitored', 0)
        else:
            summary['monitored_properties'] = 0

        # KPI 3: Alertas Hoy
        if 'alerts' in analytics_data['notifications']:
            summary['alerts_today'] = analytics_data['notifications']['alerts'].get('critical_today', 0)
        else:
            summary['alerts_today'] = 0

        # KPI 4: Tiempo de Respuesta Promedio
        if 'alerts' in analytics_data['notifications']:
            summary['avg_response_time'] = analytics_data['notifications']['alerts'].get('avg_response_time_minutes', 0)
        else:
            summary['avg_response_time'] = 0

        # KPI 5: Rutas Activas (simulado por ahora)
        summary['active_routes'] = 8  # Valor simulado hasta implementar rutas analytics

        # KPI 6: Eficiencia General (calculada)
        total_notifications = analytics_data['notifications'].get('notifications', {}).get('total', 1)
        read_notifications = total_notifications - analytics_data['notifications'].get('notifications', {}).get('unread', 0)
        summary['system_efficiency'] = round((read_notifications / max(total_notifications, 1)) * 100, 1)

        analytics_data['summary'] = summary

        return Response(analytics_data)

    except Exception as e:
        return Response({
            'error': f'Error al obtener estadísticas consolidadas: {str(e)}',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def kpi_summary(request):
    """
    Vista simplificada que retorna solo los KPIs principales para el dashboard
    """
    if not request.user.is_operador:
        return Response({
            'error': 'Solo los operadores pueden acceder a las estadísticas'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Importar modelos directamente para mejor performance
        from users.models import CustomUser, GuardiaProfile
        from properties.models import Property
        from notifications.models import Notification, NotificationType
        from django.utils import timezone
        from datetime import timedelta

        # KPI 1: Guardias en Servicio
        guardias_en_servicio = GuardiaProfile.objects.filter(
            user__is_active=True,
            is_on_duty=True
        ).count()

        # KPI 2: Propiedades Monitoreadas
        propiedades_monitoreadas = Property.objects.filter(
            is_monitored=True,
            status='activa'
        ).count()

        # KPI 3: Alertas de Hoy
        today = timezone.now().date()
        alertas_hoy = Notification.objects.filter(
            notification_type__in=[NotificationType.EMERGENCY, NotificationType.ALERT],
            created_at__date=today
        ).count()

        # KPI 4: Tiempo de Respuesta Promedio (últimas 24h)
        yesterday = timezone.now() - timedelta(hours=24)
        recent_notifications = Notification.objects.filter(
            is_read=True,
            read_at__isnull=False,
            created_at__gte=yesterday
        )

        avg_response_time = 0
        if recent_notifications.exists():
            total_time = 0
            count = 0
            for notif in recent_notifications[:50]:  # Limitar para performance
                if notif.read_at and notif.created_at:
                    diff = notif.read_at - notif.created_at
                    total_time += diff.total_seconds()
                    count += 1

            if count > 0:
                avg_response_time = round(total_time / count / 60, 1)  # En minutos

        # KPI 5: Rutas Activas (simulado)
        rutas_activas = 8  # Valor simulado

        # KPI 6: Usuarios Activos Totales
        usuarios_activos = CustomUser.objects.filter(is_active=True).count()

        # Comparación con día anterior (simulada)
        comparison = {
            'guardias_change': '+2',
            'properties_change': '+1',
            'alerts_change': '-1',
            'response_time_change': '-0.5'
        }

        return Response({
            'kpis': {
                'active_guards': {
                    'value': guardias_en_servicio,
                    'label': 'Guardias en Servicio',
                    'change': comparison['guardias_change'],
                    'icon': 'fas fa-user-shield'
                },
                'monitored_properties': {
                    'value': propiedades_monitoreadas,
                    'label': 'Propiedades Monitoreadas',
                    'change': comparison['properties_change'],
                    'icon': 'fas fa-home'
                },
                'alerts_today': {
                    'value': alertas_hoy,
                    'label': 'Alertas Hoy',
                    'change': comparison['alerts_change'],
                    'icon': 'fas fa-exclamation-triangle'
                },
                'avg_response_time': {
                    'value': f"{avg_response_time} min",
                    'label': 'Tiempo Promedio de Respuesta',
                    'change': comparison['response_time_change'],
                    'icon': 'fas fa-clock'
                },
                'active_routes': {
                    'value': rutas_activas,
                    'label': 'Rutas Activas',
                    'change': '0',
                    'icon': 'fas fa-route'
                },
                'active_users': {
                    'value': usuarios_activos,
                    'label': 'Usuarios Activos',
                    'change': '+3',
                    'icon': 'fas fa-users'
                }
            },
            'timestamp': timezone.now().isoformat(),
            'last_updated': timezone.now().strftime('%H:%M:%S')
        })

    except Exception as e:
        return Response({
            'error': f'Error al obtener KPIs: {str(e)}',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def charts_data(request):
    """
    Vista para obtener datos específicos para gráficos del dashboard
    """
    if not request.user.is_operador:
        return Response({
            'error': 'Solo los operadores pueden acceder a los datos de gráficos'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Importar modelos directamente
        from users.models import CustomUser, GuardiaProfile
        from properties.models import Property
        from notifications.models import Notification, NotificationType

        # 1. GRÁFICO: Actividad por horas del día (últimas 24 horas)
        now = timezone.now()
        hours_data = []
        hours_labels = []

        for i in range(24):
            hour_start = now.replace(hour=i, minute=0, second=0, microsecond=0)
            hour_end = hour_start + timedelta(hours=1)

            # Contar notificaciones en esa hora
            notifications_count = Notification.objects.filter(
                created_at__gte=hour_start,
                created_at__lt=hour_end
            ).count()

            hours_data.append(notifications_count)
            hours_labels.append(f"{i:02d}:00")

        # 2. GRÁFICO: Incidentes por tipo (últimos 7 días)
        week_ago = now - timedelta(days=7)
        incidents_by_type = Notification.objects.filter(
            created_at__gte=week_ago
        ).values('notification_type').annotate(
            count=Count('id')
        ).order_by('-count')

        # Preparar datos para gráfico de dona
        incident_types = []
        incident_counts = []
        incident_colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
        ]

        for i, incident in enumerate(incidents_by_type):
            incident_types.append(incident['notification_type'].title())
            incident_counts.append(incident['count'])

        # 3. GRÁFICO: Rendimiento semanal (últimos 7 días)
        weekly_data = []
        weekly_labels = []

        for i in range(7):
            day = now - timedelta(days=6-i)
            day_start = day.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)

            # Contar actividad del día
            daily_notifications = Notification.objects.filter(
                created_at__gte=day_start,
                created_at__lt=day_end
            ).count()

            weekly_data.append(daily_notifications)
            weekly_labels.append(day.strftime('%a %d'))

        # 4. GRÁFICO: Guardias activos vs inactivos
        total_guardias = CustomUser.objects.filter(user_type='guardia').count()
        guardias_activos = GuardiaProfile.objects.filter(
            user__is_active=True,
            is_on_duty=True
        ).count()
        guardias_inactivos = total_guardias - guardias_activos

        # 5. GRÁFICO: Propiedades por tipo
        properties_by_type = Property.objects.values('property_type').annotate(
            count=Count('id')
        ).order_by('-count')

        property_types = []
        property_counts = []

        for prop in properties_by_type:
            property_types.append(prop['property_type'].title())
            property_counts.append(prop['count'])

        return Response({
            'charts': {
                'hourly_activity': {
                    'type': 'line',
                    'title': 'Actividad por Horas (Hoy)',
                    'labels': hours_labels,
                    'datasets': [{
                        'label': 'Notificaciones',
                        'data': hours_data,
                        'borderColor': '#667eea',
                        'backgroundColor': 'rgba(102, 126, 234, 0.1)',
                        'tension': 0.4,
                        'fill': True
                    }]
                },
                'incidents_by_type': {
                    'type': 'doughnut',
                    'title': 'Incidentes por Tipo (7 días)',
                    'labels': incident_types,
                    'datasets': [{
                        'data': incident_counts,
                        'backgroundColor': incident_colors[:len(incident_counts)],
                        'borderWidth': 2,
                        'borderColor': '#fff'
                    }]
                },
                'weekly_performance': {
                    'type': 'bar',
                    'title': 'Rendimiento Semanal',
                    'labels': weekly_labels,
                    'datasets': [{
                        'label': 'Actividad Diaria',
                        'data': weekly_data,
                        'backgroundColor': 'rgba(102, 126, 234, 0.8)',
                        'borderColor': '#667eea',
                        'borderWidth': 1
                    }]
                },
                'guards_status': {
                    'type': 'pie',
                    'title': 'Estado de Guardias',
                    'labels': ['Activos', 'Inactivos'],
                    'datasets': [{
                        'data': [guardias_activos, guardias_inactivos],
                        'backgroundColor': ['#4CAF50', '#f44336'],
                        'borderWidth': 2,
                        'borderColor': '#fff'
                    }]
                },
                'properties_by_type': {
                    'type': 'bar',
                    'title': 'Propiedades por Tipo',
                    'labels': property_types,
                    'datasets': [{
                        'label': 'Cantidad',
                        'data': property_counts,
                        'backgroundColor': [
                            '#2196F3', '#4CAF50', '#FF9800',
                            '#E91E63', '#9C27B0', '#607D8B'
                        ][:len(property_counts)],
                        'borderWidth': 1
                    }]
                }
            },
            'timestamp': timezone.now().isoformat(),
            'period': {
                'hourly': 'Últimas 24 horas',
                'weekly': 'Últimos 7 días',
                'incidents': 'Últimos 7 días'
            }
        })

    except Exception as e:
        return Response({
            'error': f'Error al obtener datos de gráficos: {str(e)}',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def heatmap_data(request):
    """
    Vista para obtener datos de mapas de calor
    """
    if not request.user.is_operador:
        return Response({
            'error': 'Solo los operadores pueden acceder a los datos de heatmap'
        }, status=status.HTTP_403_FORBIDDEN)

    try:
        # Importar modelos directamente
        from users.models import CustomUser, GuardiaProfile
        from properties.models import Property
        from notifications.models import Notification
        from routes.models import Route, RoutePoint

        # Obtener parámetros de filtro
        days = int(request.GET.get('days', 7))  # Últimos N días
        incident_type = request.GET.get('type', 'all')  # Tipo de incidente

        # Fecha límite
        date_limit = timezone.now() - timedelta(days=days)

        # 1. HEATMAP DE ACTIVIDAD DE NOTIFICACIONES
        notifications_query = Notification.objects.filter(
            created_at__gte=date_limit
        )

        # Filtrar por tipo si se especifica
        if incident_type != 'all':
            notifications_query = notifications_query.filter(
                notification_type=incident_type
            )

        # Obtener coordenadas de notificaciones (simuladas por ahora)
        notification_points = []
        for notification in notifications_query:
            # Por ahora usamos coordenadas aleatorias cerca del centro de CDMX
            # En producción, estas vendrían de la ubicación real del incidente
            base_lat = 19.4326 + (hash(str(notification.id)) % 1000 - 500) / 10000
            base_lng = -99.1332 + (hash(str(notification.id + 1000)) % 1000 - 500) / 10000

            # Intensidad basada en prioridad y tipo
            intensity = 0.5
            if notification.priority == 'HIGH':
                intensity = 1.0
            elif notification.priority == 'MEDIUM':
                intensity = 0.7

            notification_points.append([base_lat, base_lng, intensity])

        # 2. HEATMAP DE ACTIVIDAD DE GUARDIAS
        guard_points = []
        active_guards = GuardiaProfile.objects.filter(
            user__is_active=True,
            is_on_duty=True
        )

        for guard in active_guards:
            # Coordenadas simuladas de guardias
            # En producción, estas vendrían del GPS real
            guard_lat = 19.4326 + (hash(str(guard.id)) % 800 - 400) / 8000
            guard_lng = -99.1332 + (hash(str(guard.id + 2000)) % 800 - 400) / 8000

            guard_points.append([guard_lat, guard_lng, 0.8])

        # 3. HEATMAP DE PROPIEDADES
        property_points = []
        properties = Property.objects.all()

        for prop in properties:
            if prop.latitude and prop.longitude:
                # Usar coordenadas reales de propiedades
                property_points.append([
                    float(prop.latitude),
                    float(prop.longitude),
                    0.6
                ])

        # 4. HEATMAP DE RUTAS (puntos de ruta)
        route_points = []
        routes = Route.objects.filter(is_active=True)

        for route in routes:
            try:
                # Obtener puntos de la ruta
                route_waypoints = RoutePoint.objects.filter(route=route)
                for point in route_waypoints:
                    if point.latitude and point.longitude:
                        route_points.append([
                            float(point.latitude),
                            float(point.longitude),
                            0.4
                        ])
            except:
                # Si no hay modelo RoutePoint, usar coordenadas simuladas
                route_lat = 19.4326 + (hash(str(route.id)) % 600 - 300) / 6000
                route_lng = -99.1332 + (hash(str(route.id + 3000)) % 600 - 300) / 6000
                route_points.append([route_lat, route_lng, 0.4])

        # 5. ESTADÍSTICAS DE HEATMAP
        total_incidents = len(notification_points)
        total_guards = len(guard_points)
        total_properties = len(property_points)
        total_routes = len(route_points)

        # Calcular zona de mayor actividad
        if notification_points:
            # Encontrar el centro de masa de las notificaciones
            avg_lat = sum(point[0] for point in notification_points) / len(notification_points)
            avg_lng = sum(point[1] for point in notification_points) / len(notification_points)
            hotspot = {'lat': avg_lat, 'lng': avg_lng}
        else:
            hotspot = {'lat': 19.4326, 'lng': -99.1332}  # Centro de CDMX por defecto

        return Response({
            'heatmaps': {
                'incidents': {
                    'name': 'Incidentes',
                    'data': notification_points,
                    'options': {
                        'radius': 25,
                        'blur': 15,
                        'maxZoom': 18,
                        'gradient': {
                            0.0: 'blue',
                            0.3: 'cyan',
                            0.5: 'lime',
                            0.7: 'yellow',
                            1.0: 'red'
                        }
                    }
                },
                'guards': {
                    'name': 'Guardias Activos',
                    'data': guard_points,
                    'options': {
                        'radius': 20,
                        'blur': 10,
                        'maxZoom': 18,
                        'gradient': {
                            0.0: 'navy',
                            0.5: 'blue',
                            1.0: 'cyan'
                        }
                    }
                },
                'properties': {
                    'name': 'Propiedades',
                    'data': property_points,
                    'options': {
                        'radius': 15,
                        'blur': 8,
                        'maxZoom': 18,
                        'gradient': {
                            0.0: 'green',
                            0.5: 'lime',
                            1.0: 'yellow'
                        }
                    }
                },
                'routes': {
                    'name': 'Rutas',
                    'data': route_points,
                    'options': {
                        'radius': 12,
                        'blur': 6,
                        'maxZoom': 18,
                        'gradient': {
                            0.0: 'purple',
                            0.5: 'magenta',
                            1.0: 'pink'
                        }
                    }
                }
            },
            'statistics': {
                'total_incidents': total_incidents,
                'total_guards': total_guards,
                'total_properties': total_properties,
                'total_routes': total_routes,
                'hotspot': hotspot,
                'period_days': days
            },
            'filters': {
                'available_types': [
                    'all', 'alert', 'info', 'warning',
                    'emergency', 'route_update', 'property_alert'
                ],
                'current_type': incident_type,
                'current_days': days
            },
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return Response({
            'error': f'Error al obtener datos de heatmap: {str(e)}',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@authentication_classes([TokenAuthentication])
@permission_classes([IsAuthenticated])
def temporal_heatmap_data(request):
    """
    Endpoint para obtener datos de heatmap temporal (actividad por horas)
    """
    try:
        from users.models import GuardiaProfile
        from properties.models import Property
        from notifications.models import Notification
        from datetime import datetime, timedelta
        import json

        # Parámetros
        date_str = request.GET.get('date', timezone.now().date().isoformat())
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # Datos por hora (0-23)
        hourly_data = []

        for hour in range(24):
            hour_start = timezone.make_aware(
                datetime.combine(target_date, datetime.min.time().replace(hour=hour))
            )
            hour_end = hour_start + timedelta(hours=1)

            # Contar actividad en esa hora
            notifications_count = Notification.objects.filter(
                created_at__gte=hour_start,
                created_at__lt=hour_end
            ).count()

            # Simular actividad de guardias (en producción vendría del GPS)
            guards_active = GuardiaProfile.objects.filter(
                user__is_active=True,
                is_on_duty=True
            ).count()

            # Calcular intensidad (0-1)
            max_activity = max(notifications_count, guards_active, 1)
            intensity = min(max_activity / 10.0, 1.0)  # Normalizar a 0-1

            hourly_data.append({
                'hour': hour,
                'hour_label': f"{hour:02d}:00",
                'notifications': notifications_count,
                'guards_active': guards_active,
                'intensity': intensity,
                'coordinates': [
                    19.4326 + (hour - 12) * 0.01,  # Distribuir en el mapa
                    -99.1332 + (hour - 12) * 0.01,
                    intensity
                ]
            })

        return Response({
            'temporal_heatmap': {
                'date': date_str,
                'hourly_data': hourly_data,
                'peak_hour': max(hourly_data, key=lambda x: x['intensity'])['hour'],
                'total_activity': sum(h['notifications'] for h in hourly_data)
            },
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return Response({
            'error': f'Error al obtener heatmap temporal: {str(e)}',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@authentication_classes([TokenAuthentication])
@permission_classes([IsAuthenticated])
def zone_analytics_data(request):
    """
    Endpoint para análisis por zonas geográficas
    """
    try:
        from users.models import GuardiaProfile
        from properties.models import Property
        from notifications.models import Notification
        from django.db.models import Count, Avg
        import math

        # Dividir el área en una cuadrícula de zonas
        center_lat, center_lng = 19.4326, -99.1332
        zone_size = 0.01  # Aproximadamente 1km
        zones_per_side = 10  # Cuadrícula 10x10

        zones_data = []

        for i in range(zones_per_side):
            for j in range(zones_per_side):
                # Calcular límites de la zona
                lat_min = center_lat + (i - zones_per_side/2) * zone_size
                lat_max = lat_min + zone_size
                lng_min = center_lng + (j - zones_per_side/2) * zone_size
                lng_max = lng_min + zone_size

                # Contar propiedades en la zona
                properties_in_zone = Property.objects.filter(
                    latitude__gte=lat_min,
                    latitude__lt=lat_max,
                    longitude__gte=lng_min,
                    longitude__lt=lng_max
                ).count()

                # Simular incidentes en la zona
                incidents_in_zone = max(0, properties_in_zone +
                                      (hash(f"{i}-{j}") % 5) - 2)

                # Calcular métricas de la zona
                zone_center_lat = (lat_min + lat_max) / 2
                zone_center_lng = (lng_min + lng_max) / 2

                # Calcular nivel de actividad (0-1)
                activity_level = min((properties_in_zone + incidents_in_zone) / 10.0, 1.0)

                # Clasificar zona por actividad
                if activity_level > 0.7:
                    zone_type = 'high'
                elif activity_level > 0.4:
                    zone_type = 'medium'
                else:
                    zone_type = 'low'

                zones_data.append({
                    'zone_id': f"zone_{i}_{j}",
                    'bounds': {
                        'lat_min': lat_min,
                        'lat_max': lat_max,
                        'lng_min': lng_min,
                        'lng_max': lng_max
                    },
                    'center': {
                        'lat': zone_center_lat,
                        'lng': zone_center_lng
                    },
                    'properties_count': properties_in_zone,
                    'incidents_count': incidents_in_zone,
                    'activity_level': activity_level,
                    'zone_type': zone_type,
                    'coordinates': [zone_center_lat, zone_center_lng, activity_level]
                })

        # Estadísticas generales
        total_zones = len(zones_data)
        high_activity_zones = len([z for z in zones_data if z['zone_type'] == 'high'])
        avg_activity = sum(z['activity_level'] for z in zones_data) / total_zones

        return Response({
            'zone_analytics': {
                'zones': zones_data,
                'statistics': {
                    'total_zones': total_zones,
                    'high_activity_zones': high_activity_zones,
                    'medium_activity_zones': len([z for z in zones_data if z['zone_type'] == 'medium']),
                    'low_activity_zones': len([z for z in zones_data if z['zone_type'] == 'low']),
                    'average_activity': round(avg_activity, 3),
                    'most_active_zone': max(zones_data, key=lambda x: x['activity_level'])['zone_id']
                }
            },
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return Response({
            'error': f'Error al obtener análisis de zonas: {str(e)}',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

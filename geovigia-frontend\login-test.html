<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>GeoVigia - Login Test</title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            margin: 1rem;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .login-header h2 {
            color: #333;
            margin: 0 0 0.5rem 0;
            font-size: 1.5rem;
        }
        
        .login-header p {
            color: #666;
            margin: 0;
        }
        
        .login-form {
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
        
        .input-group input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 1rem;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .login-btn:hover {
            background: #5a6fd8;
        }
        
        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .demo-credentials {
            border-top: 1px solid #e0e0e0;
            padding-top: 1.5rem;
        }
        
        .demo-credentials h4 {
            margin: 0 0 1rem 0;
            color: #333;
            text-align: center;
        }
        
        .credential-item {
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .credential-item:hover {
            background: #e9ecef;
        }
        
        .credential-item i {
            color: #667eea;
            width: 20px;
        }
        
        .demo-note {
            text-align: center;
            font-size: 0.875rem;
            color: #666;
            margin: 1rem 0 0 0;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-shield-alt"></i>
                <span>GeoVigia</span>
            </div>
            <h2>Sistema de Vigilancia Inteligente</h2>
            <p>Página de prueba de login con estilos</p>
        </div>
        
        <div id="success-message" class="success-message" style="display: none;">
            ¡Login exitoso! Los estilos funcionan correctamente.
        </div>
        
        <form id="login-form" class="login-form">
            <div class="form-group">
                <label for="username">Usuario</label>
                <div class="input-group">
                    <i class="fas fa-user"></i>
                    <input type="text" id="username" name="username" required placeholder="Ingresa tu usuario">
                </div>
            </div>
            
            <div class="form-group">
                <label for="password">Contraseña</label>
                <div class="input-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" id="password" name="password" required placeholder="Ingresa tu contraseña">
                </div>
            </div>
            
            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt"></i>
                Probar Login
            </button>
        </form>
        
        <div class="demo-credentials">
            <h4>Credenciales de Prueba</h4>
            <div class="credential-item" onclick="fillCredentials('operador', 'operador123')">
                <i class="fas fa-user-tie"></i>
                <span><strong>Operador:</strong> operador / operador123</span>
            </div>
            <div class="credential-item" onclick="fillCredentials('guardia', 'guardia123')">
                <i class="fas fa-user-shield"></i>
                <span><strong>Guardia:</strong> guardia / guardia123</span>
            </div>
            <div class="credential-item" onclick="fillCredentials('cliente', 'cliente123')">
                <i class="fas fa-user"></i>
                <span><strong>Cliente:</strong> cliente / cliente123</span>
            </div>
            <p class="demo-note">Haz click en cualquier credencial para llenar automáticamente</p>
        </div>
        
        <div style="text-align: center; margin-top: 2rem;">
            <button onclick="window.open('index.html', '_blank')" style="background: #6c757d; color: white; border: none; padding: 0.5rem 1rem; border-radius: 6px; cursor: pointer;">
                <i class="fas fa-external-link-alt"></i>
                Ir al Sistema Principal
            </button>
        </div>
    </div>

    <script>
        function fillCredentials(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.querySelector('.login-btn').focus();
        }
        
        document.getElementById('login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username && password) {
                document.getElementById('success-message').style.display = 'block';
                document.getElementById('success-message').innerHTML = `
                    ¡Login exitoso con usuario: <strong>${username}</strong>!<br>
                    Los estilos CSS funcionan correctamente.
                `;
                
                setTimeout(() => {
                    window.open('index.html', '_blank');
                }, 2000);
            }
        });
    </script>
</body>
</html>

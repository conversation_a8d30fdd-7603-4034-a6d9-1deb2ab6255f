/**
 * Módulo de Gestión de Rutas para GeoVigia Frontend
 */

class RoutesModule {
    constructor() {
        this.routes = [];
        this.table = null;
        this.filters = {
            search: '',
            status: '',
            frequency: ''
        };
    }

    /**
     * Carga el módulo de rutas
     */
    async load() {
        try {
            await this.loadRoutes();
            this.render();
            this.setupEventListeners();
        } catch (error) {
            console.error('Error cargando rutas:', error);
            GeoVigia.Components.Toast.show(
                'error',
                'Error',
                'Error al cargar las rutas'
            );
        }
    }

    /**
     * Carga la lista de rutas
     */
    async loadRoutes() {
        try {
            const response = await GeoVigia.API.Routes.getRoutes();
            this.routes = Array.isArray(response) ? response : (response.results || []);
        } catch (error) {
            console.error('Error cargando rutas:', error);
            this.routes = [];
        }
    }

    /**
     * Renderiza el módulo de rutas
     */
    render() {
        const container = document.getElementById('routes-page');
        if (!container) return;

        container.innerHTML = `
            <div class="routes-header">
                <div class="routes-actions">
                    <button class="btn btn-primary" id="add-route-btn">
                        <i class="fas fa-plus"></i>
                        Nueva Ruta
                    </button>
                    <button class="btn btn-ghost" id="refresh-routes-btn">
                        <i class="fas fa-sync-alt"></i>
                        Actualizar
                    </button>
                </div>
                
                <div class="routes-filters">
                    <div class="filter-group">
                        <select id="status-filter">
                            <option value="">Todos los estados</option>
                            <option value="activa">Activas</option>
                            <option value="inactiva">Inactivas</option>
                            <option value="mantenimiento">En Mantenimiento</option>
                            <option value="suspendida">Suspendidas</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <select id="frequency-filter">
                            <option value="">Todas las frecuencias</option>
                            <option value="diaria">Diaria</option>
                            <option value="semanal">Semanal</option>
                            <option value="quincenal">Quincenal</option>
                            <option value="mensual">Mensual</option>
                            <option value="personalizada">Personalizada</option>
                        </select>
                    </div>
                </div>
            </div>

            <div id="routes-table-container"></div>
        `;

        this.renderTable();
    }

    /**
     * Renderiza la tabla de rutas
     */
    renderTable() {
        const container = document.getElementById('routes-table-container');
        if (!container) return;

        const columns = [
            {
                key: 'name',
                title: 'Nombre',
                width: '200px'
            },
            {
                key: 'frequency',
                title: 'Frecuencia',
                width: '120px',
                render: (value) => GeoVigia.Utils.Format.capitalize(value)
            },
            {
                key: 'schedule',
                title: 'Horario',
                width: '150px',
                render: (value, row) => {
                    return `${GeoVigia.Utils.Date.formatTime(row.start_time)} - ${GeoVigia.Utils.Date.formatTime(row.end_time)}`;
                }
            },
            {
                key: 'status',
                title: 'Estado',
                width: '120px',
                render: (value) => {
                    return `<span class="status-badge ${value}">${GeoVigia.Utils.Format.capitalize(value)}</span>`;
                }
            },
            {
                key: 'priority_level',
                title: 'Prioridad',
                width: '100px',
                render: (value) => {
                    const colors = ['', 'success', 'info', 'warning', 'warning', 'error'];
                    return `<span class="status-badge ${colors[value] || 'info'}">${value}</span>`;
                }
            },
            {
                key: 'is_emergency_route',
                title: 'Emergencia',
                width: '100px',
                render: (value) => {
                    return value ? '<i class="fas fa-exclamation-triangle text-warning"></i>' : '';
                }
            },
            {
                key: 'total_properties',
                title: 'Propiedades',
                width: '100px'
            },
            {
                key: 'actions',
                title: 'Acciones',
                width: '180px',
                render: (value, row) => `
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-ghost" onclick="GeoVigia.Routes.editRoute(${row.id})" title="Editar">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-ghost" onclick="GeoVigia.Routes.viewRoute(${row.id})" title="Ver">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-ghost" onclick="GeoVigia.Routes.manageAssignments(${row.id})" title="Asignaciones">
                            <i class="fas fa-users"></i>
                        </button>
                        <button class="btn btn-sm btn-ghost" onclick="GeoVigia.Routes.manageCheckpoints(${row.id})" title="Puntos de Control">
                            <i class="fas fa-map-marker-alt"></i>
                        </button>
                    </div>
                `
            }
        ];

        this.table = new GeoVigia.Components.Table(container, {
            columns: columns,
            data: this.routes,
            searchPlaceholder: 'Buscar rutas...',
            emptyMessage: 'No se encontraron rutas'
        });
    }

    /**
     * Configura los event listeners
     */
    setupEventListeners() {
        // Botón de nueva ruta
        const addRouteBtn = document.getElementById('add-route-btn');
        if (addRouteBtn) {
            addRouteBtn.addEventListener('click', () => this.showRouteForm());
        }

        // Botón de actualizar
        const refreshBtn = document.getElementById('refresh-routes-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refresh());
        }

        // Filtros
        const statusFilter = document.getElementById('status-filter');
        const frequencyFilter = document.getElementById('frequency-filter');

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFilters();
            });
        }

        if (frequencyFilter) {
            frequencyFilter.addEventListener('change', (e) => {
                this.filters.frequency = e.target.value;
                this.applyFilters();
            });
        }
    }

    /**
     * Aplica los filtros a la tabla
     */
    applyFilters() {
        let filteredRoutes = [...this.routes];

        // Filtrar por estado
        if (this.filters.status) {
            filteredRoutes = filteredRoutes.filter(route => route.status === this.filters.status);
        }

        // Filtrar por frecuencia
        if (this.filters.frequency) {
            filteredRoutes = filteredRoutes.filter(route => route.frequency === this.filters.frequency);
        }

        // Actualizar tabla
        if (this.table) {
            this.table.setData(filteredRoutes);
        }
    }

    /**
     * Muestra el formulario de ruta
     */
    async showRouteForm(routeId = null) {
        GeoVigia.Components.Toast.show(
            'info',
            'Formulario de Rutas',
            'Formulario de creación/edición de rutas en desarrollo'
        );
    }

    /**
     * Ve los detalles de una ruta
     */
    async viewRoute(routeId) {
        try {
            const route = await GeoVigia.API.Routes.getRoute(routeId);
            
            const content = `
                <div class="route-details">
                    <div class="detail-row">
                        <strong>Nombre:</strong> ${route.name}
                    </div>
                    <div class="detail-row">
                        <strong>Frecuencia:</strong> ${GeoVigia.Utils.Format.capitalize(route.frequency)}
                    </div>
                    <div class="detail-row">
                        <strong>Horario:</strong> ${GeoVigia.Utils.Date.formatTime(route.start_time)} - ${GeoVigia.Utils.Date.formatTime(route.end_time)}
                    </div>
                    <div class="detail-row">
                        <strong>Duración estimada:</strong> ${route.estimated_duration_minutes} minutos
                    </div>
                    <div class="detail-row">
                        <strong>Estado:</strong> ${GeoVigia.Utils.Format.capitalize(route.status)}
                    </div>
                    <div class="detail-row">
                        <strong>Prioridad:</strong> ${route.priority_level}
                    </div>
                    <div class="detail-row">
                        <strong>Ruta de emergencia:</strong> ${route.is_emergency_route ? 'Sí' : 'No'}
                    </div>
                    <div class="detail-row">
                        <strong>Propiedades:</strong> ${route.total_properties || 0}
                    </div>
                    <div class="detail-row">
                        <strong>Puntos de control:</strong> ${route.checkpoint_count || 0}
                    </div>
                    ${route.description ? `<div class="detail-row"><strong>Descripción:</strong> ${route.description}</div>` : ''}
                </div>
            `;

            await GeoVigia.Components.Modal.show('Detalles de la Ruta', content, {
                showCancel: false,
                confirmText: 'Cerrar'
            });
        } catch (error) {
            GeoVigia.Components.Toast.show('error', 'Error', 'Error al cargar los detalles de la ruta');
        }
    }

    /**
     * Muestra los detalles de una ruta (llamado desde dashboard)
     */
    async showRouteDetails(routeId) {
        await this.viewRoute(routeId);
    }

    /**
     * Edita una ruta
     */
    async editRoute(routeId) {
        await this.showRouteForm(routeId);
    }

    /**
     * Gestiona las asignaciones de una ruta
     */
    async manageAssignments(routeId) {
        GeoVigia.Components.Toast.show(
            'info',
            'Asignaciones',
            'Gestión de asignaciones de guardias en desarrollo'
        );
    }

    /**
     * Gestiona los puntos de control de una ruta
     */
    async manageCheckpoints(routeId) {
        GeoVigia.Components.Toast.show(
            'info',
            'Puntos de Control',
            'Gestión de puntos de control en desarrollo'
        );
    }

    /**
     * Refresca la lista de rutas
     */
    async refresh() {
        await this.loadRoutes();
        this.applyFilters();
    }
}

// Crear instancia global del módulo de rutas
window.GeoVigia.Routes = new RoutesModule();

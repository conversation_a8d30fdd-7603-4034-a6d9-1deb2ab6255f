from django.urls import path
from . import views

app_name = 'properties'

urlpatterns = [
    # Gestión de propiedades
    path('', views.PropertyListCreateView.as_view(), name='property_list_create'),
    path('<int:pk>/', views.PropertyDetailView.as_view(), name='property_detail'),
    path('my-properties/', views.my_properties, name='my_properties'),
    path('locations/', views.properties_locations, name='properties_locations'),
    path('near/', views.properties_near_location, name='properties_near_location'),
    
    # Asignaciones de propiedades
    path('assignments/', views.PropertyOwnershipListCreateView.as_view(), name='property_assignments'),
    path('assignments/<int:pk>/', views.PropertyOwnershipDetailView.as_view(), name='property_assignment_detail'),
    path('<int:property_id>/assign/', views.assign_property_to_client, name='assign_property'),
    
    # Perímetros de propiedades
    path('perimeters/', views.PropertyPerimeterListCreateView.as_view(), name='property_perimeters'),
    path('perimeters/<int:pk>/', views.PropertyPerimeterDetailView.as_view(), name='property_perimeter_detail'),
    path('<int:property_id>/perimeter/', views.property_perimeter, name='property_perimeter'),
    path('<int:property_id>/perimeter/create/', views.create_property_perimeter, name='create_property_perimeter'),
]

from django.contrib import admin
from .models import Route, RouteProperty, GuardRoute, RouteCheckpoint, RouteExecution


class RoutePropertyInline(admin.TabularInline):
    model = RouteProperty
    extra = 1
    verbose_name = 'Propiedad'
    verbose_name_plural = 'Propiedades en la Ruta'
    ordering = ['order']


class RouteCheckpointInline(admin.TabularInline):
    model = RouteCheckpoint
    extra = 1
    verbose_name = 'Punto de Control'
    verbose_name_plural = 'Puntos de Control'
    ordering = ['order']


class GuardRouteInline(admin.TabularInline):
    model = GuardRoute
    extra = 1
    verbose_name = 'Asignación de Guardia'
    verbose_name_plural = 'Asignaciones de Guardias'


@admin.register(Route)
class RouteAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'status', 'frequency', 'start_time', 'end_time',
        'priority_level', 'is_emergency_route', 'created_at'
    ]
    list_filter = [
        'status', 'frequency', 'priority_level', 'is_emergency_route',
        'created_at'
    ]
    search_fields = ['name', 'description']
    ordering = ['-created_at']

    fieldsets = (
        ('Información Básica', {
            'fields': ('name', 'description', 'status')
        }),
        ('Configuración de Horarios', {
            'fields': ('frequency', 'start_time', 'end_time', 'estimated_duration_minutes')
        }),
        ('Días de la Semana', {
            'fields': (
                ('monday', 'tuesday', 'wednesday'),
                ('thursday', 'friday', 'saturday', 'sunday')
            ),
            'classes': ('collapse',)
        }),
        ('Configuración Avanzada', {
            'fields': ('max_deviation_meters', 'priority_level', 'is_emergency_route')
        }),
        ('Metadatos', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['created_at', 'updated_at']
    inlines = [RoutePropertyInline, RouteCheckpointInline, GuardRouteInline]

    def save_model(self, request, obj, form, change):
        if not change:  # Si es un nuevo objeto
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(RouteProperty)
class RoutePropertyAdmin(admin.ModelAdmin):
    list_display = [
        'route', 'property', 'order', 'estimated_time_minutes',
        'is_mandatory', 'created_at'
    ]
    list_filter = ['is_mandatory', 'created_at']
    search_fields = ['route__name', 'property__name']
    ordering = ['route', 'order']

    fieldsets = (
        ('Asignación', {
            'fields': ('route', 'property', 'order')
        }),
        ('Configuración', {
            'fields': ('estimated_time_minutes', 'is_mandatory')
        }),
        ('Notas', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )


@admin.register(GuardRoute)
class GuardRouteAdmin(admin.ModelAdmin):
    list_display = [
        'guard', 'route', 'status', 'start_date', 'end_date',
        'is_primary_guard', 'created_at'
    ]
    list_filter = ['status', 'is_primary_guard', 'start_date', 'created_at']
    search_fields = ['guard__username', 'route__name']
    ordering = ['-created_at']

    fieldsets = (
        ('Asignación', {
            'fields': ('guard', 'route', 'status')
        }),
        ('Fechas', {
            'fields': ('start_date', 'end_date')
        }),
        ('Configuración', {
            'fields': ('is_primary_guard',)
        }),
        ('Notas', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
        ('Metadatos', {
            'fields': ('assigned_by', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['created_at']


@admin.register(RouteCheckpoint)
class RouteCheckpointAdmin(admin.ModelAdmin):
    list_display = [
        'route', 'name', 'checkpoint_type', 'order',
        'is_mandatory', 'created_at'
    ]
    list_filter = ['checkpoint_type', 'is_mandatory', 'created_at']
    search_fields = ['route__name', 'name', 'description']
    ordering = ['route', 'order']

    fieldsets = (
        ('Información Básica', {
            'fields': ('route', 'name', 'checkpoint_type', 'order')
        }),
        ('Ubicación', {
            'fields': ('latitude', 'longitude')
        }),
        ('Configuración', {
            'fields': ('radius_meters', 'is_mandatory', 'estimated_time_minutes')
        }),
        ('Relaciones', {
            'fields': ('related_property',),
            'classes': ('collapse',)
        }),
        ('Descripción', {
            'fields': ('description',),
            'classes': ('collapse',)
        }),
    )


@admin.register(RouteExecution)
class RouteExecutionAdmin(admin.ModelAdmin):
    list_display = [
        'route', 'guard', 'status', 'start_time', 'end_time',
        'completion_percentage', 'is_delayed', 'created_at'
    ]
    list_filter = ['status', 'start_time', 'created_at']
    search_fields = ['route__name', 'guard__username']
    ordering = ['-start_time']

    fieldsets = (
        ('Ejecución', {
            'fields': ('route', 'guard', 'status')
        }),
        ('Tiempos', {
            'fields': (
                ('start_time', 'end_time'),
                ('planned_start_time', 'planned_end_time')
            )
        }),
        ('Métricas', {
            'fields': (
                ('checkpoints_visited', 'checkpoints_total'),
                ('properties_visited', 'properties_total'),
                'total_distance_meters'
            )
        }),
        ('Observaciones', {
            'fields': ('notes', 'incidents_reported'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ['created_at']

    def completion_percentage(self, obj):
        return f"{obj.completion_percentage:.1f}%"
    completion_percentage.short_description = 'Completitud'

    def is_delayed(self, obj):
        return "Sí" if obj.is_delayed else "No"
    is_delayed.short_description = 'Retrasado'
    is_delayed.boolean = True

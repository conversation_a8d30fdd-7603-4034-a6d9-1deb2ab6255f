#!/usr/bin/env python
"""
Script de prueba para las APIs del módulo de Rutas del sistema GeoVigia
Ejecutar con: python test_routes_apis.py
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://127.0.0.1:8000/api"

def get_auth_tokens():
    """Obtener tokens de autenticación para diferentes usuarios"""
    tokens = {}
    
    # Login como operador (admin)
    response = requests.post(f"{BASE_URL}/users/login/", json={
        "username": "admin",
        "password": "admin123"
    })
    if response.status_code == 200:
        tokens['operador'] = response.json()['token']
        print("✅ Token de operador obtenido")
    
    # Login como guardia (si existe)
    response = requests.post(f"{BASE_URL}/users/login/", json={
        "username": "guardia_test",
        "password": "testpass123"
    })
    if response.status_code == 200:
        tokens['guardia'] = response.json()['token']
        print("✅ Token de guardia obtenido")
    
    return tokens

def test_create_routes(tokens):
    """Prueba la creación de rutas"""
    print("\n=== Probando Creación de Rutas ===")
    
    if 'operador' not in tokens:
        print("❌ No hay token de operador disponible")
        return []
    
    routes_data = [
        {
            "name": "Ruta Matutina Centro",
            "description": "Recorrido matutino por el centro de la ciudad",
            "frequency": "diaria",
            "start_time": "08:00",
            "end_time": "12:00",
            "estimated_duration_minutes": 240,
            "max_deviation_meters": 50,
            "priority_level": 3,
            "is_emergency_route": False,
            "monday": True,
            "tuesday": True,
            "wednesday": True,
            "thursday": True,
            "friday": True,
            "saturday": False,
            "sunday": False
        },
        {
            "name": "Ruta Nocturna Seguridad",
            "description": "Recorrido nocturno de alta seguridad",
            "frequency": "diaria",
            "start_time": "22:00",
            "end_time": "06:00",
            "estimated_duration_minutes": 480,
            "max_deviation_meters": 30,
            "priority_level": 5,
            "is_emergency_route": True,
            "monday": True,
            "tuesday": True,
            "wednesday": True,
            "thursday": True,
            "friday": True,
            "saturday": True,
            "sunday": True
        }
    ]
    
    created_routes = []
    headers = {"Authorization": f"Token {tokens['operador']}"}
    
    for route_data in routes_data:
        try:
            response = requests.post(
                f"{BASE_URL}/routes/", 
                json=route_data, 
                headers=headers
            )
            
            if response.status_code == 201:
                data = response.json()
                created_routes.append(data)
                print(f"✅ Ruta '{route_data['name']}' creada exitosamente")
                print(f"   ID: {data['id']}, Prioridad: {data['priority_level']}")
            else:
                print(f"❌ Error creando '{route_data['name']}': {response.status_code}")
                print(f"   {response.text}")
        except Exception as e:
            print(f"❌ Excepción creando '{route_data['name']}': {e}")
    
    return created_routes

def test_guard_assignments(tokens, routes):
    """Prueba las asignaciones de guardias a rutas"""
    print("\n=== Probando Asignaciones de Guardias ===")
    
    if 'operador' not in tokens or not routes:
        print("❌ No hay token de operador o rutas disponibles")
        return
    
    headers = {"Authorization": f"Token {tokens['operador']}"}
    
    # Obtener lista de guardias
    response = requests.get(f"{BASE_URL}/users/list/", headers=headers)
    if response.status_code != 200:
        print("❌ No se pudo obtener lista de usuarios")
        return
    
    users_data = response.json()
    if isinstance(users_data, dict) and 'results' in users_data:
        users = users_data['results']
    else:
        users = users_data
    
    guardias = [user for user in users if user.get('user_type') == 'guardia']
    
    if not guardias:
        print("❌ No hay guardias disponibles para asignar")
        return
    
    # Asignar primera ruta al primer guardia
    route_id = routes[0]['id']
    guard_id = guardias[0]['id']
    
    today = datetime.now().date()
    assignment_data = {
        "guard_id": guard_id,
        "start_date": today.isoformat(),
        "end_date": (today + timedelta(days=30)).isoformat(),
        "is_primary_guard": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/routes/{route_id}/assign/",
            json=assignment_data,
            headers=headers
        )
        
        if response.status_code == 201:
            data = response.json()
            print(f"✅ Guardia asignado exitosamente")
            print(f"   Guardia: {data['assignment']['guard_username']}")
            print(f"   Ruta: {data['assignment']['route_name']}")
        else:
            print(f"❌ Error asignando guardia: {response.status_code}")
            print(f"   {response.text}")
    except Exception as e:
        print(f"❌ Excepción asignando guardia: {e}")

def test_route_checkpoints(tokens, routes):
    """Prueba la creación de puntos de control"""
    print("\n=== Probando Puntos de Control ===")
    
    if 'operador' not in tokens or not routes:
        print("❌ No hay token de operador o rutas disponibles")
        return
    
    headers = {"Authorization": f"Token {tokens['operador']}"}
    route_id = routes[0]['id']
    
    checkpoints_data = [
        {
            "route": route_id,
            "name": "Punto de Inicio",
            "checkpoint_type": "inicio",
            "latitude": "19.4326",
            "longitude": "-99.1332",
            "order": 1,
            "radius_meters": 20,
            "is_mandatory": True,
            "estimated_time_minutes": 2,
            "description": "Punto de inicio del recorrido"
        },
        {
            "route": route_id,
            "name": "Checkpoint Centro",
            "checkpoint_type": "intermedio",
            "latitude": "19.4285",
            "longitude": "-99.1277",
            "order": 2,
            "radius_meters": 25,
            "is_mandatory": True,
            "estimated_time_minutes": 5,
            "description": "Punto de control en el centro"
        },
        {
            "route": route_id,
            "name": "Punto Final",
            "checkpoint_type": "fin",
            "latitude": "19.4350",
            "longitude": "-99.1300",
            "order": 3,
            "radius_meters": 20,
            "is_mandatory": True,
            "estimated_time_minutes": 2,
            "description": "Punto final del recorrido"
        }
    ]
    
    for checkpoint_data in checkpoints_data:
        try:
            response = requests.post(
                f"{BASE_URL}/routes/checkpoints/",
                json=checkpoint_data,
                headers=headers
            )
            
            if response.status_code == 201:
                data = response.json()
                print(f"✅ Checkpoint '{checkpoint_data['name']}' creado exitosamente")
                print(f"   Orden: {data['order']}, Tipo: {data['checkpoint_type']}")
            else:
                print(f"❌ Error creando checkpoint: {response.status_code}")
                print(f"   {response.text}")
        except Exception as e:
            print(f"❌ Excepción creando checkpoint: {e}")

def test_route_execution(tokens, routes):
    """Prueba la ejecución de rutas por guardias"""
    print("\n=== Probando Ejecución de Rutas ===")
    
    if 'guardia' not in tokens or not routes:
        print("❌ No hay token de guardia o rutas disponibles")
        return
    
    headers = {"Authorization": f"Token {tokens['guardia']}"}
    route_id = routes[0]['id']
    
    # Iniciar ejecución de ruta
    try:
        response = requests.post(
            f"{BASE_URL}/routes/{route_id}/start/",
            headers=headers
        )
        
        if response.status_code == 201:
            data = response.json()
            execution_id = data['execution']['id']
            print(f"✅ Ejecución de ruta iniciada exitosamente")
            print(f"   ID Ejecución: {execution_id}")
            print(f"   Estado: {data['execution']['status']}")
            
            # Completar ejecución
            completion_data = {
                "notes": "Recorrido completado sin incidentes",
                "incidents_reported": 0
            }
            
            response = requests.post(
                f"{BASE_URL}/routes/executions/{execution_id}/complete/",
                json=completion_data,
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Ejecución completada exitosamente")
                print(f"   Duración: {data['execution']['duration_minutes']} minutos")
                print(f"   Completitud: {data['execution']['completion_percentage']:.1f}%")
            else:
                print(f"❌ Error completando ejecución: {response.status_code}")
        else:
            print(f"❌ Error iniciando ejecución: {response.status_code}")
            print(f"   {response.text}")
    except Exception as e:
        print(f"❌ Excepción en ejecución: {e}")

def test_routes_today(tokens):
    """Prueba la obtención de rutas activas para hoy"""
    print("\n=== Probando Rutas de Hoy ===")
    
    if 'operador' not in tokens:
        print("❌ No hay token de operador disponible")
        return
    
    headers = {"Authorization": f"Token {tokens['operador']}"}
    
    try:
        response = requests.get(f"{BASE_URL}/routes/today/", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Rutas de hoy obtenidas exitosamente")
            print(f"   Total de rutas activas hoy: {len(data)}")
            for route in data:
                print(f"   - {route['name']}: {route['start_time']} - {route['end_time']}")
        else:
            print(f"❌ Error obteniendo rutas de hoy: {response.status_code}")
    except Exception as e:
        print(f"❌ Excepción obteniendo rutas: {e}")

def main():
    """Función principal que ejecuta todas las pruebas"""
    print("🛣️ Iniciando pruebas de APIs del Módulo de Rutas")
    print("=" * 60)
    
    # Obtener tokens de autenticación
    tokens = get_auth_tokens()
    
    if not tokens:
        print("❌ No se pudieron obtener tokens de autenticación")
        return
    
    # Ejecutar pruebas en orden
    routes = test_create_routes(tokens)
    test_guard_assignments(tokens, routes)
    test_route_checkpoints(tokens, routes)
    test_route_execution(tokens, routes)
    test_routes_today(tokens)
    
    print("\n✅ Pruebas del módulo de rutas completadas")
    print("=" * 60)

if __name__ == "__main__":
    main()

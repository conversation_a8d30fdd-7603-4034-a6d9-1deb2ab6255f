/**
 * Componentes reutilizables para GeoVigia Frontend
 */

/**
 * Componente de notificaciones Toast
 */
class ToastComponent {
    constructor() {
        this.container = null;
        this.toasts = [];
        this.init();
    }

    init() {
        this.container = document.getElementById('toast-container');
        if (!this.container) {
            this.createContainer();
        }
    }

    createContainer() {
        this.container = document.createElement('div');
        this.container.id = 'toast-container';
        this.container.className = 'toast-container';
        document.body.appendChild(this.container);
    }

    show(type = 'info', title = '', message = '', duration = 5000) {
        const toast = this.createToast(type, title, message);
        this.container.appendChild(toast);
        this.toasts.push(toast);

        // Mostrar toast con animación
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto-remover después del tiempo especificado
        setTimeout(() => this.remove(toast), duration);

        // Limitar número máximo de toasts
        if (this.toasts.length > GeoVigia.APP_CONFIG.NOTIFICATIONS.MAX_NOTIFICATIONS) {
            this.remove(this.toasts[0]);
        }

        return toast;
    }

    createToast(type, title, message) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;

        const iconMap = {
            success: 'fas fa-check',
            error: 'fas fa-times',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        toast.innerHTML = `
            <div class="toast-icon">
                <i class="${iconMap[type] || iconMap.info}"></i>
            </div>
            <div class="toast-content">
                ${title ? `<div class="toast-title">${title}</div>` : ''}
                ${message ? `<div class="toast-message">${message}</div>` : ''}
            </div>
            <button class="toast-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Event listener para cerrar
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => this.remove(toast));

        return toast;
    }

    remove(toast) {
        if (!toast || !toast.parentNode) return;

        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
            this.toasts = this.toasts.filter(t => t !== toast);
        }, 300);
    }

    clear() {
        this.toasts.forEach(toast => this.remove(toast));
    }
}

/**
 * Componente de modales
 */
class ModalComponent {
    constructor() {
        this.container = null;
        this.currentModal = null;
        this.init();
    }

    init() {
        this.container = document.getElementById('modal-container');
        if (!this.container) {
            this.createContainer();
        }
    }

    createContainer() {
        this.container = document.createElement('div');
        this.container.id = 'modal-container';
        this.container.className = 'modal-container';
        document.body.appendChild(this.container);

        // Event listener para cerrar al hacer clic fuera
        this.container.addEventListener('click', (e) => {
            if (e.target === this.container) {
                this.close();
            }
        });

        // Event listener para cerrar con ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.currentModal) {
                this.close();
            }
        });
    }

    show(title, content, options = {}) {
        const modal = this.createModal(title, content, options);
        this.container.innerHTML = '';
        this.container.appendChild(modal);
        this.currentModal = modal;

        // Mostrar modal con animación
        setTimeout(() => this.container.classList.add('active'), 10);

        return new Promise((resolve) => {
            modal.resolve = resolve;
        });
    }

    createModal(title, content, options) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.width = options.width || 'auto';
        modal.style.maxWidth = options.maxWidth || '600px';

        const showFooter = options.showCancel !== false || options.showConfirm !== false;

        modal.innerHTML = `
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            ${showFooter ? `
                <div class="modal-footer">
                    ${options.showCancel !== false ? `
                        <button class="btn btn-ghost modal-cancel">
                            ${options.cancelText || 'Cancelar'}
                        </button>
                    ` : ''}
                    ${options.showConfirm !== false ? `
                        <button class="btn btn-primary modal-confirm">
                            ${options.confirmText || 'Aceptar'}
                        </button>
                    ` : ''}
                </div>
            ` : ''}
        `;

        // Event listeners
        const closeBtn = modal.querySelector('.modal-close');
        const cancelBtn = modal.querySelector('.modal-cancel');
        const confirmBtn = modal.querySelector('.modal-confirm');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.close();
                if (modal.resolve) modal.resolve(false);
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.close();
                if (modal.resolve) modal.resolve(false);
            });
        }

        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                this.close();
                if (modal.resolve) modal.resolve(true);
            });
        }

        return modal;
    }

    confirm(title, message, options = {}) {
        const content = `<p>${message}</p>`;
        return this.show(title, content, {
            ...options,
            showCancel: true,
            showConfirm: true,
            confirmText: options.confirmText || 'Confirmar',
            cancelText: options.cancelText || 'Cancelar'
        });
    }

    alert(title, message, options = {}) {
        const content = `<p>${message}</p>`;
        return this.show(title, content, {
            ...options,
            showCancel: false,
            showConfirm: true,
            confirmText: options.confirmText || 'Aceptar'
        });
    }

    close() {
        if (this.container) {
            this.container.classList.remove('active');
            setTimeout(() => {
                this.container.innerHTML = '';
                this.currentModal = null;
            }, 300);
        }
    }
}

/**
 * Componente de carga (Loading)
 */
class LoadingComponent {
    constructor() {
        this.activeLoaders = new Set();
    }

    show(target = document.body, message = 'Cargando...') {
        const loader = document.createElement('div');
        loader.className = 'loading-overlay';
        loader.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>${message}</p>
            </div>
        `;

        if (target === document.body) {
            loader.style.position = 'fixed';
            loader.style.zIndex = '9999';
        } else {
            loader.style.position = 'absolute';
            target.style.position = 'relative';
        }

        target.appendChild(loader);
        this.activeLoaders.add(loader);

        return loader;
    }

    hide(loader) {
        if (loader && loader.parentNode) {
            loader.parentNode.removeChild(loader);
            this.activeLoaders.delete(loader);
        }
    }

    hideAll() {
        this.activeLoaders.forEach(loader => this.hide(loader));
    }
}

/**
 * Componente de tabla con paginación
 */
class TableComponent {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        this.options = {
            columns: [],
            data: [],
            pageSize: GeoVigia.APP_CONFIG.PAGINATION.DEFAULT_PAGE_SIZE,
            showPagination: true,
            showSearch: true,
            searchPlaceholder: 'Buscar...',
            emptyMessage: 'No hay datos disponibles',
            ...options
        };
        
        this.currentPage = 1;
        this.filteredData = [];
        this.searchTerm = '';
        
        this.init();
    }

    init() {
        this.render();
        this.setupEventListeners();
    }

    render() {
        this.container.innerHTML = `
            ${this.options.showSearch ? this.renderSearch() : ''}
            <div class="table-container">
                <table class="table">
                    <thead>
                        ${this.renderHeader()}
                    </thead>
                    <tbody>
                        ${this.renderBody()}
                    </tbody>
                </table>
            </div>
            ${this.options.showPagination ? this.renderPagination() : ''}
        `;
    }

    renderSearch() {
        return `
            <div class="table-search">
                <div class="search-box">
                    <input type="text" placeholder="${this.options.searchPlaceholder}" value="${this.searchTerm}">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
        `;
    }

    renderHeader() {
        return `
            <tr>
                ${this.options.columns.map(col => `
                    <th style="${col.width ? `width: ${col.width}` : ''}">${col.title}</th>
                `).join('')}
            </tr>
        `;
    }

    renderBody() {
        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = startIndex + this.options.pageSize;
        const pageData = this.filteredData.slice(startIndex, endIndex);

        if (pageData.length === 0) {
            return `
                <tr>
                    <td colspan="${this.options.columns.length}" class="empty-state">
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <h3>Sin datos</h3>
                            <p>${this.options.emptyMessage}</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        return pageData.map(row => `
            <tr>
                ${this.options.columns.map(col => `
                    <td>${this.renderCell(row, col)}</td>
                `).join('')}
            </tr>
        `).join('');
    }

    renderCell(row, column) {
        if (column.render) {
            return column.render(row[column.key], row);
        }
        return row[column.key] || '';
    }

    renderPagination() {
        const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
        
        if (totalPages <= 1) return '';

        const pages = [];
        const maxVisiblePages = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            pages.push(i);
        }

        return `
            <div class="pagination">
                <button class="pagination-btn" ${this.currentPage === 1 ? 'disabled' : ''} data-page="${this.currentPage - 1}">
                    <i class="fas fa-chevron-left"></i>
                </button>
                
                ${pages.map(page => `
                    <button class="pagination-btn ${page === this.currentPage ? 'active' : ''}" data-page="${page}">
                        ${page}
                    </button>
                `).join('')}
                
                <button class="pagination-btn" ${this.currentPage === totalPages ? 'disabled' : ''} data-page="${this.currentPage + 1}">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        `;
    }

    setupEventListeners() {
        // Búsqueda
        const searchInput = this.container.querySelector('.search-box input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value;
                this.filterData();
                this.currentPage = 1;
                this.render();
                this.setupEventListeners();
            });
        }

        // Paginación
        const paginationBtns = this.container.querySelectorAll('.pagination-btn[data-page]');
        paginationBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const page = parseInt(btn.dataset.page);
                if (page !== this.currentPage && !btn.disabled) {
                    this.currentPage = page;
                    this.render();
                    this.setupEventListeners();
                }
            });
        });
    }

    filterData() {
        if (!this.searchTerm) {
            this.filteredData = [...this.options.data];
            return;
        }

        const term = this.searchTerm.toLowerCase();
        this.filteredData = this.options.data.filter(row => {
            return this.options.columns.some(col => {
                const value = row[col.key];
                return value && value.toString().toLowerCase().includes(term);
            });
        });
    }

    setData(data) {
        this.options.data = data;
        this.filterData();
        this.currentPage = 1;
        this.render();
        this.setupEventListeners();
    }

    refresh() {
        this.filterData();
        this.render();
        this.setupEventListeners();
    }
}

// Crear instancias globales de los componentes
window.GeoVigia.Components = {
    Toast: new ToastComponent(),
    Modal: new ModalComponent(),
    Loading: new LoadingComponent(),
    Table: TableComponent,
};

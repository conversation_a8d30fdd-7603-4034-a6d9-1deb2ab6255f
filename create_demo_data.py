#!/usr/bin/env python3
"""
Script para crear datos de demostración específicos para GeoVigia
Genera usuarios, propiedades, rutas y notificaciones para mostrar al cliente
"""

import os
import sys
import django
from datetime import datetime, timedelta
import random

# Configurar Django
sys.path.append('geovigia_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'geovigia_backend.settings')
django.setup()

from django.contrib.auth.hashers import make_password
from django.utils import timezone
from users.models import CustomUser, OperadorProfile, GuardiaProfile, ClienteProfile
from properties.models import Property, PropertyAssignment, SecurityPerimeter
from routes.models import Route, RouteExecution, CheckPoint
from notifications.models import Notification, NotificationType

def create_demo_users():
    """Crear usuarios específicos para la demo"""
    print("👥 Creando usuarios de demostración...")
    
    # 1. OPERADOR DEMO
    operador, created = CustomUser.objects.get_or_create(
        username='demo_operador',
        defaults={
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': 'Administrador Demo',
            'user_type': 'operador',
            'is_active': True,
            'password': make_password('demo123')
        }
    )
    
    if created:
        OperadorProfile.objects.get_or_create(
            user=operador,
            defaults={
                'employee_id': 'DEMO-OP001',
                'department': 'Demostración',
                'access_level': 'full'
            }
        )
        print(f"✅ Usuario demo operador creado: {operador.username}")
    
    # 2. GUARDIAS DEMO
    guardias_data = [
        {
            'username': 'demo_guardia1',
            'first_name': 'Miguel',
            'last_name': 'Seguridad Demo',
            'badge': 'DEMO-G001',
            'lat': 19.4336,
            'lng': -99.1312
        },
        {
            'username': 'demo_guardia2', 
            'first_name': 'Luis',
            'last_name': 'Vigilante Demo',
            'badge': 'DEMO-G002',
            'lat': 19.4316,
            'lng': -99.1352
        },
        {
            'username': 'demo_guardia3',
            'first_name': 'Ana',
            'last_name': 'Protección Demo',
            'badge': 'DEMO-G003',
            'lat': 19.4366,
            'lng': -99.1292
        }
    ]
    
    for guardia_data in guardias_data:
        guardia, created = CustomUser.objects.get_or_create(
            username=guardia_data['username'],
            defaults={
                'email': f"{guardia_data['username']}@geovigia.com",
                'first_name': guardia_data['first_name'],
                'last_name': guardia_data['last_name'],
                'user_type': 'guardia',
                'is_active': True,
                'password': make_password('demo123')
            }
        )
        
        if created:
            GuardiaProfile.objects.get_or_create(
                user=guardia,
                defaults={
                    'badge_number': guardia_data['badge'],
                    'shift': 'diurno',
                    'phone': f"+52 55 {random.randint(1000, 9999)} {random.randint(1000, 9999)}",
                    'emergency_contact': f"+52 55 {random.randint(1000, 9999)} {random.randint(1000, 9999)}",
                    'is_available': True,
                    'is_on_duty': True,
                    'current_latitude': guardia_data['lat'],
                    'current_longitude': guardia_data['lng']
                }
            )
            print(f"✅ Usuario demo guardia creado: {guardia.username}")
    
    # 3. CLIENTES DEMO
    clientes_data = [
        {
            'username': 'demo_cliente1',
            'first_name': 'María',
            'last_name': 'Propietaria Demo',
            'company': 'Inmobiliaria Demo S.A.'
        },
        {
            'username': 'demo_cliente2',
            'first_name': 'Roberto',
            'last_name': 'Empresario Demo',
            'company': 'Corporativo Demo'
        }
    ]
    
    for cliente_data in clientes_data:
        cliente, created = CustomUser.objects.get_or_create(
            username=cliente_data['username'],
            defaults={
                'email': f"{cliente_data['username']}@geovigia.com",
                'first_name': cliente_data['first_name'],
                'last_name': cliente_data['last_name'],
                'user_type': 'cliente',
                'is_active': True,
                'password': make_password('demo123')
            }
        )
        
        if created:
            ClienteProfile.objects.get_or_create(
                user=cliente,
                defaults={
                    'company_name': cliente_data['company'],
                    'contact_phone': f"+52 55 {random.randint(1000, 9999)} {random.randint(1000, 9999)}",
                    'address': f"Dirección Demo {random.randint(100, 999)}",
                    'subscription_type': 'premium',
                    'is_verified': True
                }
            )
            print(f"✅ Usuario demo cliente creado: {cliente.username}")

def create_demo_properties():
    """Crear propiedades específicas para la demo"""
    print("🏠 Creando propiedades de demostración...")
    
    # Obtener clientes demo
    clientes = CustomUser.objects.filter(username__startswith='demo_cliente')
    
    properties_data = [
        {
            'name': 'Casa Central Demo',
            'type': 'casa',
            'address': 'Av. Reforma 123, CDMX',
            'lat': 19.4326,
            'lng': -99.1332,
            'description': 'Propiedad principal para demostración'
        },
        {
            'name': 'Oficina Corporativa Demo',
            'type': 'oficina',
            'address': 'Polanco Business Center',
            'lat': 19.4356,
            'lng': -99.1302,
            'description': 'Oficinas corporativas de alta seguridad'
        },
        {
            'name': 'Apartamento Residencial Demo',
            'type': 'apartamento',
            'address': 'Torre Residencial Sur',
            'lat': 19.4296,
            'lng': -99.1362,
            'description': 'Apartamento de lujo en zona residencial'
        },
        {
            'name': 'Local Comercial Demo',
            'type': 'local',
            'address': 'Centro Comercial Norte',
            'lat': 19.4346,
            'lng': -99.1282,
            'description': 'Local comercial en zona de alto tráfico'
        },
        {
            'name': 'Bodega Industrial Demo',
            'type': 'bodega',
            'address': 'Zona Industrial Este',
            'lat': 19.4306,
            'lng': -99.1342,
            'description': 'Bodega para almacenamiento industrial'
        }
    ]
    
    for i, prop_data in enumerate(properties_data):
        cliente = clientes[i % len(clientes)] if clientes else None
        
        property_obj, created = Property.objects.get_or_create(
            name=prop_data['name'],
            defaults={
                'property_type': prop_data['type'],
                'address': prop_data['address'],
                'latitude': prop_data['lat'],
                'longitude': prop_data['lng'],
                'owner': cliente,
                'description': prop_data['description'],
                'is_monitored': True,
                'status': 'activa',
                'priority': 'alta' if i < 2 else 'media'
            }
        )
        
        if created:
            print(f"✅ Propiedad demo creada: {property_obj.name}")
            
            # Crear perímetro de seguridad
            SecurityPerimeter.objects.get_or_create(
                property=property_obj,
                defaults={
                    'perimeter_type': 'circular',
                    'center_latitude': prop_data['lat'],
                    'center_longitude': prop_data['lng'],
                    'radius': 100.0,
                    'is_active': True
                }
            )

def create_demo_routes():
    """Crear rutas específicas para la demo"""
    print("🛣️ Creando rutas de demostración...")
    
    # Obtener guardias demo
    guardias = CustomUser.objects.filter(username__startswith='demo_guardia')
    properties = Property.objects.filter(name__contains='Demo')
    
    routes_data = [
        {
            'name': 'Ruta Centro Demo',
            'description': 'Patrullaje del centro de la ciudad',
            'frequency': 'cada_2_horas'
        },
        {
            'name': 'Ruta Residencial Demo',
            'description': 'Vigilancia de zona residencial',
            'frequency': 'cada_4_horas'
        },
        {
            'name': 'Ruta Comercial Demo',
            'description': 'Supervisión de área comercial',
            'frequency': 'cada_3_horas'
        }
    ]
    
    for i, route_data in enumerate(routes_data):
        guardia = guardias[i % len(guardias)] if guardias else None
        
        route, created = Route.objects.get_or_create(
            name=route_data['name'],
            defaults={
                'description': route_data['description'],
                'assigned_guard': guardia,
                'frequency': route_data['frequency'],
                'is_active': True,
                'start_time': '08:00:00',
                'end_time': '20:00:00'
            }
        )
        
        if created:
            print(f"✅ Ruta demo creada: {route.name}")
            
            # Crear puntos de control
            route_properties = list(properties)[i:i+2]  # 2 propiedades por ruta
            for j, prop in enumerate(route_properties):
                CheckPoint.objects.get_or_create(
                    route=route,
                    property=prop,
                    defaults={
                        'order': j + 1,
                        'estimated_duration': 15,
                        'is_mandatory': True
                    }
                )

def create_demo_notifications():
    """Crear notificaciones específicas para la demo"""
    print("🔔 Creando notificaciones de demostración...")
    
    users = CustomUser.objects.filter(username__startswith='demo_')
    
    notifications_data = [
        {
            'title': 'Sistema Iniciado',
            'message': 'El sistema GeoVigia ha sido iniciado correctamente',
            'type': NotificationType.INFO,
            'priority': 'NORMAL'
        },
        {
            'title': 'Guardia en Ruta',
            'message': 'Miguel Seguridad ha iniciado su ruta de patrullaje',
            'type': NotificationType.ROUTE_UPDATE,
            'priority': 'NORMAL'
        },
        {
            'title': 'Propiedad Segura',
            'message': 'Casa Central Demo - Verificación completada sin incidentes',
            'type': NotificationType.PROPERTY_ALERT,
            'priority': 'LOW'
        },
        {
            'title': 'Alerta de Demostración',
            'message': 'Esta es una alerta de prueba para mostrar el sistema',
            'type': NotificationType.ALERT,
            'priority': 'HIGH'
        },
        {
            'title': 'Ruta Completada',
            'message': 'Ruta Centro Demo completada exitosamente',
            'type': NotificationType.ROUTE_UPDATE,
            'priority': 'NORMAL'
        }
    ]
    
    for i, notif_data in enumerate(notifications_data):
        user = users[i % len(users)] if users else None
        
        # Crear notificaciones en diferentes momentos del día
        created_time = timezone.now() - timedelta(hours=random.randint(0, 12))
        
        notification = Notification.objects.create(
            user=user,
            title=notif_data['title'],
            message=notif_data['message'],
            notification_type=notif_data['type'],
            priority=notif_data['priority'],
            is_sent=True,
            is_read=random.choice([True, False]),
            created_at=created_time
        )
        
        if notification.is_read:
            notification.read_at = created_time + timedelta(minutes=random.randint(1, 30))
            notification.save()
        
        print(f"✅ Notificación demo creada: {notification.title}")

def main():
    """Función principal para crear todos los datos de demo"""
    print("🚀 Iniciando creación de datos de demostración para GeoVigia...")
    print("=" * 60)
    
    try:
        # Crear usuarios demo
        create_demo_users()
        print()
        
        # Crear propiedades demo
        create_demo_properties()
        print()
        
        # Crear rutas demo
        create_demo_routes()
        print()
        
        # Crear notificaciones demo
        create_demo_notifications()
        print()
        
        print("=" * 60)
        print("✅ ¡Datos de demostración creados exitosamente!")
        print()
        print("🔑 Credenciales de acceso para la demo:")
        print("   👨‍💼 Operador: demo_operador / demo123")
        print("   👮‍♂️ Guardia: demo_guardia1 / demo123")
        print("   👤 Cliente: demo_cliente1 / demo123")
        print()
        print("🌐 Accede a la demo en:")
        print("   📱 Demo Interactivo: http://localhost:3000/demo.html")
        print("   🖥️ Sistema Completo: http://localhost:3000")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error creando datos de demo: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()

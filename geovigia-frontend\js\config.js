/**
 * Configuración global de la aplicación GeoVigia Frontend
 */

// Configuración de la API
const API_CONFIG = {
    BASE_URL: 'http://127.0.0.1:8000/api',
    TIMEOUT: 30000, // 30 segundos
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000, // 1 segundo
};

// Endpoints de la API
const API_ENDPOINTS = {
    // Autenticación
    AUTH: {
        LOGIN: '/users/login/',
        LOGOUT: '/users/logout/',
        PROFILE: '/users/profile/',
        CHANGE_PASSWORD: '/users/change-password/',
    },
    
    // Usuarios
    USERS: {
        LIST: '/users/list/',
        CREATE: '/users/register/',
        DETAIL: (id) => `/users/${id}/`,
        UPDATE: (id) => `/users/${id}/`,
        DELETE: (id) => `/users/${id}/`,
        ACTIVATE: (id) => `/users/${id}/activate/`,
        DEACTIVATE: (id) => `/users/${id}/deactivate/`,
        STATS: '/users/stats/',
    },
    
    // Propiedades
    PROPERTIES: {
        LIST: '/properties/',
        CREATE: '/properties/',
        DETAIL: (id) => `/properties/${id}/`,
        UPDATE: (id) => `/properties/${id}/`,
        DELETE: (id) => `/properties/${id}/`,
        MY_PROPERTIES: '/properties/my-properties/',
        NEARBY: '/properties/nearby/',
        STATS: '/properties/stats/',
    },
    
    // Perímetros
    PERIMETERS: {
        LIST: '/properties/perimeters/',
        CREATE: '/properties/perimeters/',
        DETAIL: (id) => `/properties/perimeters/${id}/`,
        UPDATE: (id) => `/properties/perimeters/${id}/`,
        DELETE: (id) => `/properties/perimeters/${id}/`,
        BY_PROPERTY: (propertyId) => `/properties/${propertyId}/perimeters/`,
    },
    
    // Rutas
    ROUTES: {
        LIST: '/routes/',
        CREATE: '/routes/',
        DETAIL: (id) => `/routes/${id}/`,
        UPDATE: (id) => `/routes/${id}/`,
        DELETE: (id) => `/routes/${id}/`,
        MY_ROUTES: '/routes/my-routes/',
        ACTIVE: '/routes/active/',
        TODAY: '/routes/today/',
        ASSIGN_GUARD: (routeId) => `/routes/${routeId}/assign/`,
        START_EXECUTION: (routeId) => `/routes/${routeId}/start/`,
        COMPLETE_EXECUTION: (executionId) => `/routes/executions/${executionId}/complete/`,
    },
    
    // Asignaciones de Guardias
    GUARD_ASSIGNMENTS: {
        LIST: '/routes/assignments/',
        CREATE: '/routes/assignments/',
        DETAIL: (id) => `/routes/assignments/${id}/`,
        UPDATE: (id) => `/routes/assignments/${id}/`,
        DELETE: (id) => `/routes/assignments/${id}/`,
    },
    
    // Puntos de Control
    CHECKPOINTS: {
        LIST: '/routes/checkpoints/',
        CREATE: '/routes/checkpoints/',
        DETAIL: (id) => `/routes/checkpoints/${id}/`,
        UPDATE: (id) => `/routes/checkpoints/${id}/`,
        DELETE: (id) => `/routes/checkpoints/${id}/`,
        BY_ROUTE: (routeId) => `/routes/${routeId}/checkpoints/`,
    },
    
    // Ejecuciones de Rutas
    EXECUTIONS: {
        LIST: '/routes/executions/',
        CREATE: '/routes/executions/',
        DETAIL: (id) => `/routes/executions/${id}/`,
        UPDATE: (id) => `/routes/executions/${id}/`,
        DELETE: (id) => `/routes/executions/${id}/`,
    },
};

// Configuración de la aplicación
const APP_CONFIG = {
    NAME: 'GeoVigia',
    VERSION: '1.0.0',
    DESCRIPTION: 'Sistema de Vigilancia Inteligente',
    
    // Configuración de paginación
    PAGINATION: {
        DEFAULT_PAGE_SIZE: 10,
        PAGE_SIZE_OPTIONS: [5, 10, 25, 50, 100],
    },
    
    // Configuración de notificaciones
    NOTIFICATIONS: {
        DURATION: 5000, // 5 segundos
        MAX_NOTIFICATIONS: 5,
    },
    
    // Configuración de mapas (para futuras implementaciones)
    MAPS: {
        DEFAULT_CENTER: {
            lat: 19.4326,
            lng: -99.1332, // Ciudad de México
        },
        DEFAULT_ZOOM: 13,
        MAX_ZOOM: 18,
        MIN_ZOOM: 8,
    },
    
    // Configuración de tiempo real
    REALTIME: {
        REFRESH_INTERVAL: 30000, // 30 segundos
        HEARTBEAT_INTERVAL: 60000, // 1 minuto
    },
    
    // Configuración de archivos
    FILES: {
        MAX_SIZE: 5 * 1024 * 1024, // 5MB
        ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
        ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.pdf'],
    },
};

// Configuración de validación
const VALIDATION_CONFIG = {
    // Usuarios
    USER: {
        USERNAME: {
            MIN_LENGTH: 3,
            MAX_LENGTH: 30,
            PATTERN: /^[a-zA-Z0-9_]+$/,
        },
        PASSWORD: {
            MIN_LENGTH: 8,
            MAX_LENGTH: 128,
            REQUIRE_UPPERCASE: true,
            REQUIRE_LOWERCASE: true,
            REQUIRE_NUMBERS: true,
            REQUIRE_SPECIAL: false,
        },
        EMAIL: {
            PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        },
        PHONE: {
            PATTERN: /^\+?[\d\s\-\(\)]+$/,
            MIN_LENGTH: 10,
            MAX_LENGTH: 15,
        },
    },
    
    // Propiedades
    PROPERTY: {
        NAME: {
            MIN_LENGTH: 2,
            MAX_LENGTH: 100,
        },
        ADDRESS: {
            MIN_LENGTH: 5,
            MAX_LENGTH: 200,
        },
        COORDINATES: {
            LAT_MIN: -90,
            LAT_MAX: 90,
            LNG_MIN: -180,
            LNG_MAX: 180,
        },
    },
    
    // Rutas
    ROUTE: {
        NAME: {
            MIN_LENGTH: 3,
            MAX_LENGTH: 100,
        },
        DESCRIPTION: {
            MAX_LENGTH: 500,
        },
        DURATION: {
            MIN_MINUTES: 15,
            MAX_MINUTES: 1440, // 24 horas
        },
        PRIORITY: {
            MIN: 1,
            MAX: 5,
        },
    },
};

// Configuración de UI
const UI_CONFIG = {
    // Temas
    THEMES: {
        LIGHT: 'light',
        DARK: 'dark',
        AUTO: 'auto',
    },
    
    // Idiomas
    LANGUAGES: {
        ES: 'es',
        EN: 'en',
    },
    
    // Animaciones
    ANIMATIONS: {
        DURATION_FAST: 150,
        DURATION_NORMAL: 250,
        DURATION_SLOW: 350,
        EASING: 'ease-in-out',
    },
    
    // Breakpoints responsivos
    BREAKPOINTS: {
        XS: 480,
        SM: 640,
        MD: 768,
        LG: 1024,
        XL: 1280,
        XXL: 1536,
    },
};

// Configuración de estados
const STATUS_CONFIG = {
    // Estados de usuarios
    USER_STATUS: {
        ACTIVE: 'activo',
        INACTIVE: 'inactivo',
        SUSPENDED: 'suspendido',
        PENDING: 'pendiente',
    },
    
    // Estados de propiedades
    PROPERTY_STATUS: {
        ACTIVE: 'activa',
        INACTIVE: 'inactiva',
        MAINTENANCE: 'mantenimiento',
        SUSPENDED: 'suspendida',
    },
    
    // Estados de rutas
    ROUTE_STATUS: {
        ACTIVE: 'activa',
        INACTIVE: 'inactiva',
        MAINTENANCE: 'mantenimiento',
        SUSPENDED: 'suspendida',
    },
    
    // Estados de ejecuciones
    EXECUTION_STATUS: {
        STARTED: 'iniciada',
        IN_PROGRESS: 'en_progreso',
        COMPLETED: 'completada',
        INCOMPLETE: 'incompleta',
        CANCELLED: 'cancelada',
    },
    
    // Tipos de usuarios
    USER_TYPES: {
        OPERATOR: 'operador',
        GUARD: 'guardia',
        CLIENT: 'cliente',
    },
    
    // Tipos de propiedades
    PROPERTY_TYPES: {
        RESIDENTIAL: 'residencial',
        COMMERCIAL: 'comercial',
        INDUSTRIAL: 'industrial',
        MIXED: 'mixto',
    },
    
    // Frecuencias de rutas
    ROUTE_FREQUENCIES: {
        DAILY: 'diaria',
        WEEKLY: 'semanal',
        BIWEEKLY: 'quincenal',
        MONTHLY: 'mensual',
        CUSTOM: 'personalizada',
    },
    
    // Tipos de puntos de control
    CHECKPOINT_TYPES: {
        START: 'inicio',
        PROPERTY: 'propiedad',
        INTERMEDIATE: 'intermedio',
        END: 'fin',
        EMERGENCY: 'emergencia',
    },
};

// Configuración de colores para estados
const COLOR_CONFIG = {
    STATUS_COLORS: {
        active: '#10b981',
        inactive: '#ef4444',
        pending: '#f59e0b',
        completed: '#3b82f6',
        maintenance: '#8b5cf6',
        suspended: '#6b7280',
    },
    
    PRIORITY_COLORS: {
        1: '#10b981', // Baja - Verde
        2: '#3b82f6', // Normal - Azul
        3: '#f59e0b', // Media - Amarillo
        4: '#f97316', // Alta - Naranja
        5: '#ef4444', // Crítica - Rojo
    },
    
    USER_TYPE_COLORS: {
        operador: '#3b82f6',
        guardia: '#10b981',
        cliente: '#8b5cf6',
    },
};

// Configuración de localStorage
const STORAGE_CONFIG = {
    KEYS: {
        AUTH_TOKEN: 'geovigia_auth_token',
        USER_DATA: 'geovigia_user_data',
        THEME: 'geovigia_theme',
        LANGUAGE: 'geovigia_language',
        SIDEBAR_COLLAPSED: 'geovigia_sidebar_collapsed',
        LAST_ROUTE: 'geovigia_last_route',
        PREFERENCES: 'geovigia_preferences',
    },
    
    EXPIRY: {
        AUTH_TOKEN: 24 * 60 * 60 * 1000, // 24 horas
        USER_DATA: 60 * 60 * 1000, // 1 hora
        PREFERENCES: 30 * 24 * 60 * 60 * 1000, // 30 días
    },
};

// Configuración de mensajes
const MESSAGES_CONFIG = {
    SUCCESS: {
        LOGIN: 'Sesión iniciada correctamente',
        LOGOUT: 'Sesión cerrada correctamente',
        SAVE: 'Datos guardados correctamente',
        UPDATE: 'Datos actualizados correctamente',
        DELETE: 'Elemento eliminado correctamente',
        CREATE: 'Elemento creado correctamente',
    },
    
    ERROR: {
        LOGIN_FAILED: 'Error al iniciar sesión. Verifique sus credenciales.',
        NETWORK_ERROR: 'Error de conexión. Verifique su conexión a internet.',
        SERVER_ERROR: 'Error del servidor. Intente nuevamente más tarde.',
        VALIDATION_ERROR: 'Por favor, corrija los errores en el formulario.',
        UNAUTHORIZED: 'No tiene permisos para realizar esta acción.',
        NOT_FOUND: 'El elemento solicitado no fue encontrado.',
        TIMEOUT: 'La operación ha excedido el tiempo límite.',
    },
    
    CONFIRM: {
        DELETE: '¿Está seguro de que desea eliminar este elemento?',
        LOGOUT: '¿Está seguro de que desea cerrar sesión?',
        CANCEL: '¿Está seguro de que desea cancelar? Los cambios no guardados se perderán.',
        RESET: '¿Está seguro de que desea restablecer el formulario?',
    },
};

// Exportar configuraciones para uso global
window.GeoVigia = {
    API_CONFIG,
    API_ENDPOINTS,
    APP_CONFIG,
    VALIDATION_CONFIG,
    UI_CONFIG,
    STATUS_CONFIG,
    COLOR_CONFIG,
    STORAGE_CONFIG,
    MESSAGES_CONFIG,
};

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GeoVigia - Demo Interactivo</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">

    <!-- Estilos principales -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/analytics.css">
    <link rel="stylesheet" href="css/advanced-analytics.css">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <!-- Chart.js CSS -->
    <style>
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .demo-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            background: white;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 1rem 0;
        }

        .feature-card {
            padding: 1.5rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f8f9fa;
            transition: transform 0.2s;
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .feature-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .demo-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            margin: 0.5rem;
            transition: background 0.2s;
        }

        .demo-button:hover {
            background: #5a6fd8;
        }

        .demo-button.secondary {
            background: #6c757d;
        }

        .demo-button.secondary:hover {
            background: #5a6268;
        }

        .credentials-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-warning { background: #ffc107; }

        .mini-map {
            height: 200px;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .chart-mini {
            height: 150px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <!-- Demo Header -->
    <div class="demo-header">
        <h1><i class="fas fa-shield-alt"></i> GeoVigia - Demo Interactivo</h1>
        <p>Sistema de Vigilancia Inteligente - Demostración de Funcionalidades</p>
        <div style="margin-top: 1rem;">
            <span class="status-indicator status-online"></span>
            <span>Sistema Operativo</span>
            <span style="margin-left: 2rem;">
                <i class="fas fa-clock"></i>
                <span id="demo-time"></span>
            </span>
        </div>
    </div>

    <!-- Navegación Demo -->
    <div style="text-align: center; margin: 2rem 0;">
        <button class="demo-button" onclick="showSection('overview')">
            <i class="fas fa-home"></i> Resumen General
        </button>
        <button class="demo-button" onclick="showSection('maps')">
            <i class="fas fa-map"></i> Mapas Interactivos
        </button>
        <button class="demo-button" onclick="showSection('analytics')">
            <i class="fas fa-chart-line"></i> Analytics Avanzados
        </button>
        <button class="demo-button" onclick="showSection('notifications')">
            <i class="fas fa-bell"></i> Notificaciones
        </button>
        <button class="demo-button" onclick="showSection('users')">
            <i class="fas fa-users"></i> Gestión de Usuarios
        </button>

    </div>

    <!-- Contenido Principal -->
    <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 1rem;">

        <!-- Sección: Resumen General -->
        <div id="overview-section" class="demo-section">
            <h2><i class="fas fa-tachometer-alt"></i> Resumen General del Sistema</h2>

            <!-- Credenciales de Acceso -->
            <div class="credentials-box">
                <h3><i class="fas fa-key"></i> Credenciales de Acceso Demo</h3>
                <div class="demo-grid">
                    <div>
                        <strong>👨‍💼 Operador:</strong><br>
                        Usuario: <code>operador</code><br>
                        Contraseña: <code>operador123</code>
                    </div>
                    <div>
                        <strong>👮‍♂️ Guardia:</strong><br>
                        Usuario: <code>guardia</code><br>
                        Contraseña: <code>guardia123</code>
                    </div>
                    <div>
                        <strong>👤 Cliente:</strong><br>
                        Usuario: <code>cliente</code><br>
                        Contraseña: <code>cliente123</code>
                    </div>
                </div>
            </div>

            <!-- Estadísticas en Tiempo Real -->
            <h3>📊 Estadísticas del Sistema</h3>
            <div class="demo-grid">
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-users"></i></div>
                    <h4>Usuarios Activos</h4>
                    <div style="font-size: 2rem; font-weight: bold; color: #28a745;" id="demo-users">24</div>
                    <p>Guardias en servicio</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-home"></i></div>
                    <h4>Propiedades</h4>
                    <div style="font-size: 2rem; font-weight: bold; color: #007bff;" id="demo-properties">156</div>
                    <p>Bajo vigilancia</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-route"></i></div>
                    <h4>Rutas Activas</h4>
                    <div style="font-size: 2rem; font-weight: bold; color: #ffc107;" id="demo-routes">8</div>
                    <p>En ejecución</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-bell"></i></div>
                    <h4>Alertas Hoy</h4>
                    <div style="font-size: 2rem; font-weight: bold; color: #dc3545;" id="demo-alerts">3</div>
                    <p>Reportes recibidos</p>
                </div>
            </div>

            <!-- Funcionalidades Implementadas -->
            <h3>✅ Funcionalidades Implementadas</h3>
            <div class="demo-grid">
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-shield-alt"></i></div>
                    <h4>Sistema de Autenticación</h4>
                    <p>JWT tokens, permisos granulares por rol, sesiones seguras</p>
                    <button class="demo-button" onclick="testLogin()">Probar Login</button>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-map-marked-alt"></i></div>
                    <h4>Mapas Interactivos</h4>
                    <p>Leaflet, marcadores dinámicos, herramientas de dibujo</p>
                    <button class="demo-button" onclick="showSection('maps')">Ver Mapas</button>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-chart-area"></i></div>
                    <h4>Analytics Avanzados</h4>
                    <p>Heatmaps, gráficos predictivos, widgets interactivos</p>
                    <button class="demo-button" onclick="showSection('analytics')">Ver Analytics</button>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-broadcast-tower"></i></div>
                    <h4>Notificaciones en Tiempo Real</h4>
                    <p>WebSocket, Push API, notificaciones por rol</p>
                    <button class="demo-button" onclick="showSection('notifications')">Ver Notificaciones</button>
                </div>
            </div>
        </div>

        <!-- Sección: Mapas -->
        <div id="maps-section" class="demo-section" style="display: none;">
            <h2><i class="fas fa-map"></i> Mapas Interactivos</h2>
            <p>Sistema completo de mapas con Leaflet, marcadores dinámicos y herramientas de dibujo.</p>

            <div style="text-align: center; margin: 1rem 0;">
                <button class="demo-button" onclick="initDemoMap()">
                    <i class="fas fa-play"></i> Inicializar Mapa Demo
                </button>
                <button class="demo-button" onclick="loadDemoProperties()">
                    <i class="fas fa-home"></i> Cargar Propiedades
                </button>
                <button class="demo-button" onclick="loadDemoGuards()">
                    <i class="fas fa-user-shield"></i> Mostrar Guardias
                </button>
            </div>

            <div id="demo-map" class="mini-map" style="height: 400px;"></div>

            <div class="demo-grid">
                <div class="feature-card">
                    <h4>🏠 Marcadores de Propiedades</h4>
                    <p>Iconos personalizados, popups informativos, estados dinámicos</p>
                </div>
                <div class="feature-card">
                    <h4>👮‍♂️ Seguimiento de Guardias</h4>
                    <p>Ubicación en tiempo real, rutas de patrullaje, estado de servicio</p>
                </div>
                <div class="feature-card">
                    <h4>🛡️ Perímetros de Seguridad</h4>
                    <p>Dibujo de zonas, geofencing, alertas automáticas</p>
                </div>
            </div>
        </div>

        <!-- Sección: Analytics -->
        <div id="analytics-section" class="demo-section" style="display: none;">
            <h2><i class="fas fa-chart-line"></i> Analytics Avanzados</h2>
            <p>Fase 3 completada: Heatmaps, gráficos predictivos y widgets interactivos.</p>

            <div style="text-align: center; margin: 1rem 0;">
                <button class="demo-button" onclick="loadDemoCharts()">
                    <i class="fas fa-chart-bar"></i> Cargar Gráficos
                </button>
                <button class="demo-button" onclick="loadDemoHeatmap()">
                    <i class="fas fa-fire"></i> Mostrar Heatmap
                </button>
                <button class="demo-button" onclick="loadDemoWidgets()">
                    <i class="fas fa-th-large"></i> Widgets Interactivos
                </button>
            </div>

            <div class="demo-grid">
                <div class="feature-card">
                    <h4>📈 Gráficos de Tendencias</h4>
                    <canvas id="demo-chart" class="chart-mini"></canvas>
                    <p>Análisis temporal con predicciones</p>
                </div>
                <div class="feature-card">
                    <h4>🔥 Mapas de Calor</h4>
                    <div id="demo-heatmap" class="mini-map"></div>
                    <p>Densidad de actividad por zonas</p>
                </div>
            </div>
        </div>

        <!-- Sección: Notificaciones -->
        <div id="notifications-section" class="demo-section" style="display: none;">
            <h2><i class="fas fa-bell"></i> Sistema de Notificaciones</h2>
            <p>Notificaciones en tiempo real con WebSocket y Push API del navegador.</p>

            <div style="text-align: center; margin: 1rem 0;">
                <button class="demo-button" onclick="requestNotificationPermissions()">
                    <i class="fas fa-bell"></i> Solicitar Permisos
                </button>
                <button class="demo-button" onclick="testWebSocket()">
                    <i class="fas fa-wifi"></i> Probar WebSocket
                </button>
                <button class="demo-button" onclick="testPushNotification()">
                    <i class="fas fa-mobile-alt"></i> Probar Push Notification
                </button>
                <button class="demo-button" onclick="sendDemoAlert()">
                    <i class="fas fa-exclamation-triangle"></i> Enviar Alerta Demo
                </button>
            </div>

            <div id="notifications-demo-area" style="min-height: 200px; border: 1px solid #ddd; border-radius: 8px; padding: 1rem;">
                <p style="text-align: center; color: #666;">Las notificaciones aparecerán aquí...</p>
            </div>

            <div class="demo-grid">
                <div class="feature-card">
                    <h4>🔄 WebSocket en Tiempo Real</h4>
                    <p>Conexión bidireccional, reconexión automática, grupos por rol</p>
                </div>
                <div class="feature-card">
                    <h4>📱 Push Notifications</h4>
                    <p>Service Worker, notificaciones del navegador, acciones personalizadas</p>
                </div>
                <div class="feature-card">
                    <h4>🚨 Alertas de Emergencia</h4>
                    <p>Prioridades, escalamiento automático, notificaciones críticas</p>
                </div>
            </div>
        </div>

        <!-- Sección: Usuarios -->
        <div id="users-section" class="demo-section" style="display: none;">
            <h2><i class="fas fa-users"></i> Gestión de Usuarios</h2>
            <p>Sistema completo de gestión de usuarios con roles diferenciados.</p>

            <div style="text-align: center; margin: 1rem 0;">
                <button class="demo-button" onclick="loadDemoUsers()">
                    <i class="fas fa-list"></i> Cargar Lista de Usuarios
                </button>
                <button class="demo-button" onclick="showUserStats()">
                    <i class="fas fa-chart-pie"></i> Estadísticas
                </button>
            </div>

            <div id="users-demo-area">
                <!-- Los usuarios se cargarán aquí -->
            </div>

            <div class="demo-grid">
                <div class="feature-card">
                    <h4>👨‍💼 Operadores</h4>
                    <p>Acceso completo, gestión del sistema, supervisión general</p>
                </div>
                <div class="feature-card">
                    <h4>👮‍♂️ Guardias</h4>
                    <p>Rutas asignadas, ubicación GPS, reportes de actividad</p>
                </div>
                <div class="feature-card">
                    <h4>👤 Clientes</h4>
                    <p>Propiedades asignadas, monitoreo específico, alertas personalizadas</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="js/config.js"></script>

    <script>
        // Variables globales para la demo
        let demoMap = null;
        let demoChart = null;
        let demoHeatmap = null;
        let demoStats = {
            users: 24,
            properties: 156,
            routes: 8,
            alerts: 3
        };

        // Inicialización de la demo
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Iniciando Demo de GeoVigia...');

            // Mostrar sección inicial
            showSection('overview');

            // Actualizar reloj
            updateDemoTime();
            setInterval(updateDemoTime, 1000);

            // Animar estadísticas
            animateStats();
            setInterval(animateStats, 5000);

            console.log('✅ Demo inicializada correctamente');
        });

        // Funciones de navegación
        function showSection(sectionName) {
            // Ocultar todas las secciones
            const sections = document.querySelectorAll('.demo-section');
            sections.forEach(section => section.style.display = 'none');

            // Mostrar sección seleccionada
            const targetSection = document.getElementById(sectionName + '-section');
            if (targetSection) {
                targetSection.style.display = 'block';

                // Inicializar contenido específico de la sección
                if (sectionName === 'maps') {
                    setTimeout(() => initDemoMap(), 500);
                } else if (sectionName === 'analytics') {
                    setTimeout(() => loadDemoCharts(), 500);
                } else if (sectionName === 'notifications') {
                    setTimeout(() => setupNotificationsDemo(), 500);
                }
            }

            // Actualizar botones activos
            const buttons = document.querySelectorAll('.demo-button');
            buttons.forEach(btn => btn.classList.remove('active'));
        }

        // Funciones de tiempo y animaciones
        function updateDemoTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('es-ES');
            const timeElement = document.getElementById('demo-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        function animateStats() {
            // Simular cambios pequeños en las estadísticas
            demoStats.users += Math.floor(Math.random() * 3) - 1;
            demoStats.alerts += Math.floor(Math.random() * 2);

            // Mantener valores realistas
            demoStats.users = Math.max(20, Math.min(30, demoStats.users));
            demoStats.alerts = Math.max(0, Math.min(10, demoStats.alerts));

            // Actualizar DOM
            updateElement('demo-users', demoStats.users);
            updateElement('demo-properties', demoStats.properties);
            updateElement('demo-routes', demoStats.routes);
            updateElement('demo-alerts', demoStats.alerts);
        }

        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                element.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 200);
            }
        }

        // Funciones de mapas
        function initDemoMap() {
            if (demoMap) {
                demoMap.remove();
            }

            const mapContainer = document.getElementById('demo-map');
            if (!mapContainer) return;

            try {
                demoMap = L.map('demo-map').setView([19.4326, -99.1332], 13);

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(demoMap);

                showToast('Mapa inicializado correctamente', 'success');

                // Cargar datos demo automáticamente
                setTimeout(() => {
                    loadDemoProperties();
                    loadDemoGuards();
                }, 1000);

            } catch (error) {
                console.error('Error inicializando mapa:', error);
                showToast('Error al inicializar el mapa', 'error');
            }
        }

        function loadDemoProperties() {
            if (!demoMap) {
                showToast('Primero inicializa el mapa', 'warning');
                return;
            }

            // Propiedades de ejemplo
            const properties = [
                { lat: 19.4326, lng: -99.1332, name: 'Casa Central', type: 'casa' },
                { lat: 19.4356, lng: -99.1302, name: 'Oficina Norte', type: 'oficina' },
                { lat: 19.4296, lng: -99.1362, name: 'Apartamento Sur', type: 'apartamento' },
                { lat: 19.4346, lng: -99.1282, name: 'Local Comercial', type: 'local' },
                { lat: 19.4306, lng: -99.1342, name: 'Bodega Este', type: 'bodega' }
            ];

            properties.forEach(prop => {
                const icon = L.divIcon({
                    html: '<i class="fas fa-home" style="color: #007bff; font-size: 20px;"></i>',
                    iconSize: [30, 30],
                    className: 'custom-div-icon'
                });

                L.marker([prop.lat, prop.lng], { icon })
                    .addTo(demoMap)
                    .bindPopup(`
                        <strong>${prop.name}</strong><br>
                        Tipo: ${prop.type}<br>
                        Estado: <span style="color: green;">Activa</span><br>
                        <button onclick="showPropertyDetails('${prop.name}')">Ver Detalles</button>
                    `);
            });

            showToast('Propiedades cargadas en el mapa', 'success');
        }

        function loadDemoGuards() {
            if (!demoMap) {
                showToast('Primero inicializa el mapa', 'warning');
                return;
            }

            // Guardias de ejemplo
            const guards = [
                { lat: 19.4336, lng: -99.1312, name: 'Miguel Seguridad', status: 'en_ruta' },
                { lat: 19.4316, lng: -99.1352, name: 'Carlos Vigilante', status: 'patrullando' },
                { lat: 19.4366, lng: -99.1292, name: 'Ana Protección', status: 'disponible' }
            ];

            guards.forEach(guard => {
                const color = guard.status === 'en_ruta' ? '#28a745' :
                             guard.status === 'patrullando' ? '#ffc107' : '#6c757d';

                const icon = L.divIcon({
                    html: `<i class="fas fa-user-shield" style="color: ${color}; font-size: 18px;"></i>`,
                    iconSize: [25, 25],
                    className: 'custom-div-icon'
                });

                L.marker([guard.lat, guard.lng], { icon })
                    .addTo(demoMap)
                    .bindPopup(`
                        <strong>${guard.name}</strong><br>
                        Estado: ${guard.status}<br>
                        Última actualización: ${new Date().toLocaleTimeString()}<br>
                        <button onclick="trackGuard('${guard.name}')">Seguir</button>
                    `);
            });

            showToast('Guardias cargados en el mapa', 'success');
        }

        // Funciones de analytics
        function loadDemoCharts() {
            const ctx = document.getElementById('demo-chart');
            if (!ctx) return;

            if (demoChart) {
                demoChart.destroy();
            }

            const data = {
                labels: ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'],
                datasets: [{
                    label: 'Incidentes',
                    data: [12, 19, 3, 5, 2, 3, 8],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Predicción',
                    data: [null, null, null, null, null, 6, 9],
                    borderColor: '#ff6b6b',
                    backgroundColor: 'rgba(255, 107, 107, 0.1)',
                    borderDash: [5, 5],
                    tension: 0.4
                }]
            };

            demoChart = new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Tendencias y Predicciones'
                        }
                    }
                }
            });

            showToast('Gráficos de analytics cargados', 'success');
        }

        function loadDemoHeatmap() {
            const mapContainer = document.getElementById('demo-heatmap');
            if (!mapContainer) return;

            if (demoHeatmap) {
                demoHeatmap.remove();
            }

            try {
                demoHeatmap = L.map('demo-heatmap').setView([19.4326, -99.1332], 12);

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(demoHeatmap);

                // Simular datos de heatmap
                const heatData = [];
                for (let i = 0; i < 50; i++) {
                    heatData.push([
                        19.4326 + (Math.random() - 0.5) * 0.1,
                        -99.1332 + (Math.random() - 0.5) * 0.1,
                        Math.random()
                    ]);
                }

                // Nota: En la implementación real se usaría leaflet.heat
                // Por ahora mostramos círculos para simular el heatmap
                heatData.forEach(point => {
                    L.circle([point[0], point[1]], {
                        radius: point[2] * 200,
                        color: 'red',
                        fillColor: 'red',
                        fillOpacity: point[2] * 0.5
                    }).addTo(demoHeatmap);
                });

                showToast('Heatmap de actividad cargado', 'success');

            } catch (error) {
                console.error('Error cargando heatmap:', error);
                showToast('Error al cargar heatmap', 'error');
            }
        }

        function loadDemoWidgets() {
            showToast('Widgets interactivos disponibles en el sistema completo', 'info');
        }

        // Funciones de notificaciones
        function setupNotificationsDemo() {
            const area = document.getElementById('notifications-demo-area');
            if (area) {
                area.innerHTML = `
                    <div style="text-align: center; padding: 2rem;">
                        <h4>🔔 Centro de Notificaciones Demo</h4>
                        <p>Usa los botones arriba para probar las diferentes funcionalidades</p>
                        <div style="margin: 1rem 0; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                            <small><strong>💡 Tip:</strong> Las push notifications aparecerán en tu sistema operativo.
                            Si no las ves, verifica que los permisos estén habilitados.</small>
                        </div>
                        <div id="demo-notifications-list" style="text-align: left; max-height: 300px; overflow-y: auto;"></div>
                    </div>
                `;
            }

            // Agregar notificación inicial si la lista está vacía
            const list = document.getElementById('demo-notifications-list');
            if (list && list.children.length === 0) {
                addDemoNotification('Sistema', 'Centro de notificaciones inicializado correctamente', 'info');
            }
        }

        function requestNotificationPermissions() {
            showToast('Solicitando permisos de notificación...', 'info');
            setupNotificationsDemo();

            if ('Notification' in window) {
                const currentPermission = Notification.permission;

                if (currentPermission === 'granted') {
                    addDemoNotification('Permisos', 'Los permisos de notificación ya están concedidos', 'success');
                    showToast('Permisos ya concedidos', 'success');
                } else if (currentPermission === 'denied') {
                    addDemoNotification('Permisos', 'Los permisos están denegados. Ve a configuración del navegador para habilitarlos.', 'warning');
                    showToast('Permisos denegados - Revisa configuración del navegador', 'warning');
                } else {
                    Notification.requestPermission().then(permission => {
                        if (permission === 'granted') {
                            addDemoNotification('Permisos', '✅ Permisos de notificación concedidos correctamente', 'success');
                            showToast('¡Permisos concedidos!', 'success');
                        } else {
                            addDemoNotification('Permisos', 'Permisos de notificación denegados', 'error');
                            showToast('Permisos denegados', 'error');
                        }
                    });
                }
            } else {
                addDemoNotification('Permisos', 'Este navegador no soporta notificaciones', 'error');
                showToast('Navegador no compatible', 'error');
            }
        }

        function testWebSocket() {
            setupNotificationsDemo();
            addDemoNotification('WebSocket', 'Conexión establecida correctamente', 'success');
            setTimeout(() => {
                addDemoNotification('WebSocket', 'Mensaje de prueba recibido', 'info');
            }, 1500);
            showToast('WebSocket simulado correctamente', 'success');
        }

        function testPushNotification() {
            showToast('Probando push notifications...', 'info');

            // Asegurar que el área de notificaciones existe
            setupNotificationsDemo();

            if ('Notification' in window) {
                if (Notification.permission === 'granted') {
                    // Ya tenemos permisos, enviar notificación
                    sendPushNotification();
                } else if (Notification.permission === 'denied') {
                    // Permisos denegados
                    addDemoNotification('Push API', 'Permisos de notificación denegados. Habilítalos en la configuración del navegador.', 'error');
                    showToast('Permisos de notificación denegados', 'error');
                } else {
                    // Solicitar permisos
                    Notification.requestPermission().then(permission => {
                        if (permission === 'granted') {
                            sendPushNotification();
                        } else {
                            addDemoNotification('Push API', 'Permisos de notificación requeridos para esta funcionalidad', 'warning');
                            showToast('Permisos de notificación requeridos', 'warning');
                        }
                    });
                }
            } else {
                addDemoNotification('Push API', 'Este navegador no soporta notificaciones push', 'error');
                showToast('Navegador no compatible con push notifications', 'error');
            }
        }

        function sendPushNotification() {
            try {
                // Crear notificación del navegador
                const notification = new Notification('🚀 GeoVigia Demo', {
                    body: '✅ Notificación push de prueba enviada correctamente',
                    icon: 'favicon.svg',
                    tag: 'geovigia-demo',
                    requireInteraction: false,
                    silent: false
                });

                // Manejar click en la notificación
                notification.onclick = function() {
                    window.focus();
                    notification.close();
                    showToast('Notificación clickeada', 'success');
                };

                // Agregar a la lista de demo
                addDemoNotification('Push API', 'Notificación push enviada al navegador correctamente', 'success');
                showToast('¡Push notification enviada!', 'success');

                // Auto-cerrar después de 5 segundos
                setTimeout(() => {
                    notification.close();
                }, 5000);

            } catch (error) {
                console.error('Error enviando push notification:', error);
                addDemoNotification('Push API', `Error: ${error.message}`, 'error');
                showToast('Error enviando push notification', 'error');
            }
        }

        function sendDemoAlert() {
            showToast('Enviando alerta de emergencia...', 'warning');
            setupNotificationsDemo();

            addDemoNotification('🚨 Alerta de Emergencia', 'Actividad sospechosa detectada en Zona Norte', 'error');

            // Simular notificación push de emergencia si hay permisos
            if (Notification.permission === 'granted') {
                new Notification('🚨 GeoVigia - Alerta de Emergencia', {
                    body: 'Actividad sospechosa detectada en Zona Norte',
                    icon: 'favicon.svg',
                    tag: 'emergency-alert',
                    requireInteraction: true,
                    silent: false
                });
            }

            setTimeout(() => {
                addDemoNotification('Sistema', 'Guardia más cercano notificado automáticamente', 'warning');
            }, 2000);

            setTimeout(() => {
                addDemoNotification('Respuesta', 'Guardia en camino - ETA: 3 minutos', 'info');
            }, 4000);
        }

        function addDemoNotification(type, message, level) {
            const list = document.getElementById('demo-notifications-list');
            if (!list) return;

            const colors = {
                success: '#28a745',
                info: '#17a2b8',
                warning: '#ffc107',
                error: '#dc3545'
            };

            const notification = document.createElement('div');
            notification.style.cssText = `
                background: ${colors[level] || '#6c757d'};
                color: white;
                padding: 0.75rem;
                margin: 0.5rem 0;
                border-radius: 6px;
                animation: slideIn 0.3s ease;
            `;

            notification.innerHTML = `
                <strong>${type}</strong><br>
                ${message}<br>
                <small>${new Date().toLocaleTimeString()}</small>
            `;

            list.insertBefore(notification, list.firstChild);

            // Limitar a 5 notificaciones
            while (list.children.length > 5) {
                list.removeChild(list.lastChild);
            }
        }

        // Funciones de usuarios
        function loadDemoUsers() {
            const area = document.getElementById('users-demo-area');
            if (!area) return;

            const users = [
                { name: 'Carlos Administrador', type: 'operador', status: 'online', lastSeen: 'Ahora' },
                { name: 'Miguel Seguridad', type: 'guardia', status: 'online', lastSeen: '2 min' },
                { name: 'Ana Propietaria', type: 'cliente', status: 'offline', lastSeen: '1 hora' },
                { name: 'Luis Vigilante', type: 'guardia', status: 'online', lastSeen: '5 min' },
                { name: 'María Cliente', type: 'cliente', status: 'online', lastSeen: '10 min' }
            ];

            let html = '<h4>👥 Lista de Usuarios del Sistema</h4><div class="demo-grid">';

            users.forEach(user => {
                const statusColor = user.status === 'online' ? '#28a745' : '#6c757d';
                const typeIcon = user.type === 'operador' ? 'fa-user-tie' :
                               user.type === 'guardia' ? 'fa-user-shield' : 'fa-user';

                html += `
                    <div class="feature-card">
                        <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                            <i class="fas ${typeIcon}" style="margin-right: 0.5rem; color: #667eea;"></i>
                            <strong>${user.name}</strong>
                        </div>
                        <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                            <span class="status-indicator" style="background: ${statusColor};"></span>
                            <span style="text-transform: capitalize;">${user.type}</span>
                        </div>
                        <small>Última actividad: ${user.lastSeen}</small>
                    </div>
                `;
            });

            html += '</div>';
            area.innerHTML = html;

            showToast('Lista de usuarios cargada', 'success');
        }

        function showUserStats() {
            showToast('Estadísticas: 5 usuarios activos, 3 guardias en servicio, 2 clientes conectados', 'info');
        }

        // Funciones auxiliares
        function testLogin() {
            showToast('Mostrando pantalla de login...', 'info');

            // Crear modal con la pantalla de login
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                ">
                    <button onclick="this.closest('.modal-overlay').remove()" style="
                        position: absolute;
                        top: 20px;
                        right: 20px;
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        font-size: 24px;
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    ">×</button>

                    <div style="
                        background: white;
                        border-radius: 16px;
                        padding: 2rem;
                        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
                        width: 100%;
                        max-width: 400px;
                        margin: 1rem;
                    ">
                        <div style="text-align: center; margin-bottom: 2rem;">
                            <div style="
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                gap: 0.5rem;
                                font-size: 1.5rem;
                                font-weight: bold;
                                color: #667eea;
                                margin-bottom: 1rem;
                            ">
                                <i class="fas fa-shield-alt"></i>
                                <span>GeoVigia</span>
                            </div>
                            <h2 style="color: #333; margin: 0 0 0.5rem 0; font-size: 1.5rem;">
                                Sistema de Vigilancia Inteligente
                            </h2>
                            <p style="color: #666; margin: 0;">
                                Ingresa tus credenciales para acceder
                            </p>
                        </div>

                        <form style="margin-bottom: 2rem;">
                            <div style="margin-bottom: 1.5rem;">
                                <label style="
                                    display: block;
                                    margin-bottom: 0.5rem;
                                    color: #333;
                                    font-weight: 500;
                                ">Usuario</label>
                                <div style="position: relative;">
                                    <i class="fas fa-user" style="
                                        position: absolute;
                                        left: 1rem;
                                        top: 50%;
                                        transform: translateY(-50%);
                                        color: #666;
                                    "></i>
                                    <input type="text" value="operador" style="
                                        width: 100%;
                                        padding: 1rem 1rem 1rem 3rem;
                                        border: 2px solid #e0e0e0;
                                        border-radius: 8px;
                                        font-size: 1rem;
                                        box-sizing: border-box;
                                    " placeholder="Ingresa tu usuario">
                                </div>
                            </div>

                            <div style="margin-bottom: 1.5rem;">
                                <label style="
                                    display: block;
                                    margin-bottom: 0.5rem;
                                    color: #333;
                                    font-weight: 500;
                                ">Contraseña</label>
                                <div style="position: relative;">
                                    <i class="fas fa-lock" style="
                                        position: absolute;
                                        left: 1rem;
                                        top: 50%;
                                        transform: translateY(-50%);
                                        color: #666;
                                    "></i>
                                    <input type="password" value="operador123" style="
                                        width: 100%;
                                        padding: 1rem 1rem 1rem 3rem;
                                        border: 2px solid #e0e0e0;
                                        border-radius: 8px;
                                        font-size: 1rem;
                                        box-sizing: border-box;
                                    " placeholder="Ingresa tu contraseña">
                                </div>
                            </div>

                            <button type="button" onclick="showLoginSuccess()" style="
                                width: 100%;
                                padding: 1rem;
                                background: #667eea;
                                color: white;
                                border: none;
                                border-radius: 8px;
                                font-size: 1rem;
                                font-weight: 600;
                                cursor: pointer;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                gap: 0.5rem;
                            ">
                                <i class="fas fa-sign-in-alt"></i>
                                Iniciar Sesión
                            </button>
                        </form>

                        <div style="
                            border-top: 1px solid #e0e0e0;
                            padding-top: 1.5rem;
                        ">
                            <h4 style="margin: 0 0 1rem 0; color: #333; text-align: center;">
                                Credenciales de Demo
                            </h4>
                            <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                <div style="
                                    padding: 0.75rem;
                                    background: #f8f9fa;
                                    border-radius: 6px;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                ">
                                    <i class="fas fa-user-tie" style="color: #667eea; width: 20px;"></i>
                                    <span><strong>Operador:</strong> operador / operador123</span>
                                </div>
                                <div style="
                                    padding: 0.75rem;
                                    background: #f8f9fa;
                                    border-radius: 6px;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                ">
                                    <i class="fas fa-user-shield" style="color: #667eea; width: 20px;"></i>
                                    <span><strong>Guardia:</strong> guardia / guardia123</span>
                                </div>
                                <div style="
                                    padding: 0.75rem;
                                    background: #f8f9fa;
                                    border-radius: 6px;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                ">
                                    <i class="fas fa-user" style="color: #667eea; width: 20px;"></i>
                                    <span><strong>Cliente:</strong> cliente / cliente123</span>
                                </div>
                            </div>
                            <p style="
                                text-align: center;
                                font-size: 0.875rem;
                                color: #666;
                                margin: 1rem 0 0 0;
                            ">Esta es una demostración de la pantalla de login</p>
                        </div>
                    </div>
                </div>
            `;

            modal.className = 'modal-overlay';
            document.body.appendChild(modal);

            // Agregar estilos de animación
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        function showLoginSuccess() {
            showToast('¡Login exitoso! Esta es solo una demostración.', 'success');
            setTimeout(() => {
                const modal = document.querySelector('.modal-overlay');
                if (modal) modal.remove();
            }, 2000);
        }



        function showPropertyDetails(name) {
            showToast(`Mostrando detalles de: ${name}`, 'info');
        }

        function trackGuard(name) {
            showToast(`Siguiendo a: ${name}`, 'info');
        }

        function showToast(message, type = 'info') {
            const colors = {
                success: '#28a745',
                info: '#17a2b8',
                warning: '#ffc107',
                error: '#dc3545'
            };

            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type] || '#6c757d'};
                color: white;
                padding: 1rem;
                border-radius: 6px;
                z-index: 10000;
                animation: slideInRight 0.3s ease;
                max-width: 300px;
            `;

            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // Estilos de animación
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(-100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            .custom-div-icon {
                background: none;
                border: none;
            }

            .demo-button.active {
                background: #5a6fd8;
                transform: scale(0.95);
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

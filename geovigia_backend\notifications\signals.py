from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model
import logging

from .models import Notification, NotificationType, NotificationPriority

logger = logging.getLogger(__name__)
User = get_user_model()

# Importar channel_layer solo cuando sea necesario
def get_channel_layer_safe():
    try:
        from channels.layers import get_channel_layer
        return get_channel_layer()
    except ImportError:
        logger.warning("Channels no está disponible")
        return None

def send_realtime_notification_safe(notification):
    """Envía notificación en tiempo real de manera segura"""
    try:
        # Importar solo cuando sea necesario
        from .views import send_realtime_notification
        send_realtime_notification(notification)
    except ImportError:
        logger.warning("Sistema de notificaciones en tiempo real no disponible")
    except Exception as e:
        logger.error(f"Error enviando notificación en tiempo real: {e}")

def send_websocket_message_safe(group, message):
    """Envía mensaje WebSocket de manera segura"""
    try:
        from asgiref.sync import async_to_sync
        channel_layer = get_channel_layer_safe()
        if channel_layer:
            async_to_sync(channel_layer.group_send)(group, message)
    except ImportError:
        logger.warning("WebSocket no disponible")
    except Exception as e:
        logger.error(f"Error enviando mensaje WebSocket: {e}")


@receiver(post_save, sender=User)
def user_created_notification(sender, instance, created, **kwargs):
    """Envía notificación cuando se crea un nuevo usuario"""
    if created:
        try:
            # Notificar a todos los operadores
            operadores = User.objects.filter(user_type='operador')

            for operador in operadores:
                notification = Notification.objects.create(
                    recipient=operador,
                    title='Nuevo Usuario Registrado',
                    message=f'Se ha registrado un nuevo usuario: {instance.get_full_name() or instance.username}',
                    notification_type=NotificationType.INFO,
                    priority=NotificationPriority.NORMAL,
                    extra_data={
                        'user_id': instance.id,
                        'user_type': instance.user_type,
                        'username': instance.username
                    }
                )

                # Enviar en tiempo real
                send_realtime_notification_safe(notification)

        except Exception as e:
            logger.error(f"Error enviando notificación de nuevo usuario: {e}")


@receiver(post_save, sender='properties.Property')
def property_created_notification(sender, instance, created, **kwargs):
    """Envía notificación cuando se crea una nueva propiedad"""
    if created:
        try:
            # Notificar a todos los operadores
            operadores = User.objects.filter(user_type='operador')

            for operador in operadores:
                notification = Notification.objects.create(
                    recipient=operador,
                    title='Nueva Propiedad Registrada',
                    message=f'Se ha registrado una nueva propiedad: {instance.name}',
                    notification_type=NotificationType.INFO,
                    priority=NotificationPriority.NORMAL,
                    extra_data={
                        'property_id': instance.id,
                        'property_name': instance.name,
                        'owner': instance.owner.get_full_name() if instance.owner else 'Sin propietario'
                    }
                )

                # Enviar en tiempo real
                send_realtime_notification_safe(notification)

        except Exception as e:
            logger.error(f"Error enviando notificación de nueva propiedad: {e}")


@receiver(post_save, sender='routes.Route')
def route_updated_notification(sender, instance, created, **kwargs):
    """Envía notificación cuando se actualiza una ruta"""
    try:
        action = 'creada' if created else 'actualizada'

        # Notificar al guardia asignado si existe
        if instance.assigned_guard:
            notification = Notification.objects.create(
                recipient=instance.assigned_guard,
                title=f'Ruta {action.title()}',
                message=f'Tu ruta "{instance.name}" ha sido {action}',
                notification_type=NotificationType.ROUTE_UPDATE,
                priority=NotificationPriority.HIGH,
                extra_data={
                    'route_id': instance.id,
                    'route_name': instance.name,
                    'action': action
                }
            )

            # Enviar en tiempo real
            send_realtime_notification_safe(notification)

            # Enviar actualización de ruta via WebSocket
            route_data = {
                'id': instance.id,
                'name': instance.name,
                'description': instance.description,
                'is_active': instance.is_active,
                'action': action
            }

            send_websocket_message_safe(
                f"user_{instance.assigned_guard.id}",
                {
                    'type': 'route_update',
                    'route': route_data
                }
            )

    except Exception as e:
        logger.error(f"Error enviando notificación de ruta: {e}")


def send_emergency_alert(user, alert_type, message, location=None):
    """
    Envía una alerta de emergencia a todos los operadores y guardias

    Args:
        user: Usuario que envía la alerta
        alert_type: Tipo de alerta
        message: Mensaje de la alerta
        location: Ubicación opcional
    """
    try:
        # Obtener todos los operadores y guardias
        recipients = User.objects.filter(
            user_type__in=['operador', 'guardia']
        )

        for recipient in recipients:
            notification = Notification.objects.create(
                recipient=recipient,
                sender=user,
                title='🚨 ALERTA DE EMERGENCIA',
                message=message,
                notification_type=NotificationType.EMERGENCY,
                priority=NotificationPriority.CRITICAL,
                extra_data={
                    'alert_type': alert_type,
                    'sender_type': user.user_type,
                    'location': location or {},
                    'requires_response': True
                }
            )

            # Enviar en tiempo real
            send_realtime_notification_safe(notification)

        # Enviar alerta general via WebSocket
        alert_data = {
            'id': str(notification.id),
            'type': alert_type,
            'message': message,
            'sender': user.get_full_name() or user.username,
            'sender_type': user.user_type,
            'location': location or {},
            'timestamp': notification.created_at.isoformat()
        }

        # Enviar a operadores
        send_websocket_message_safe(
            "role_operador",
            {
                'type': 'alert_message',
                'alert': alert_data
            }
        )

        # Enviar a guardias
        send_websocket_message_safe(
            "role_guardia",
            {
                'type': 'alert_message',
                'alert': alert_data
            }
        )

        logger.info(f"Alerta de emergencia enviada por {user.username}: {alert_type}")

    except Exception as e:
        logger.error(f"Error enviando alerta de emergencia: {e}")


def send_property_alert(property_instance, alert_type, message):
    """
    Envía una alerta específica de una propiedad

    Args:
        property_instance: Instancia de la propiedad
        alert_type: Tipo de alerta
        message: Mensaje de la alerta
    """
    try:
        # Notificar al propietario
        if property_instance.owner:
            notification = Notification.objects.create(
                recipient=property_instance.owner,
                title=f'Alerta en {property_instance.name}',
                message=message,
                notification_type=NotificationType.PROPERTY_ALERT,
                priority=NotificationPriority.HIGH,
                extra_data={
                    'property_id': property_instance.id,
                    'property_name': property_instance.name,
                    'alert_type': alert_type
                }
            )

            send_realtime_notification_safe(notification)

        # Notificar a operadores
        operadores = User.objects.filter(user_type='operador')
        for operador in operadores:
            notification = Notification.objects.create(
                recipient=operador,
                title=f'Alerta en Propiedad: {property_instance.name}',
                message=message,
                notification_type=NotificationType.PROPERTY_ALERT,
                priority=NotificationPriority.HIGH,
                extra_data={
                    'property_id': property_instance.id,
                    'property_name': property_instance.name,
                    'alert_type': alert_type,
                    'owner': property_instance.owner.get_full_name() if property_instance.owner else 'Sin propietario'
                }
            )

            send_realtime_notification_safe(notification)

    except Exception as e:
        logger.error(f"Error enviando alerta de propiedad: {e}")


def send_guard_status_update(guard, status, location=None):
    """
    Envía actualización de estado de guardia

    Args:
        guard: Usuario guardia
        status: Nuevo estado
        location: Ubicación actual
    """
    try:
        # Notificar a operadores
        operadores = User.objects.filter(user_type='operador')

        for operador in operadores:
            notification = Notification.objects.create(
                recipient=operador,
                title=f'Estado de Guardia: {guard.get_full_name() or guard.username}',
                message=f'Estado actualizado a: {status}',
                notification_type=NotificationType.GUARD_STATUS,
                priority=NotificationPriority.NORMAL,
                extra_data={
                    'guard_id': guard.id,
                    'guard_name': guard.get_full_name() or guard.username,
                    'status': status,
                    'location': location or {}
                }
            )

            send_realtime_notification_safe(notification)

        # Enviar actualización via WebSocket
        status_data = {
            'guard_id': guard.id,
            'guard_name': guard.get_full_name() or guard.username,
            'status': status,
            'location': location or {},
            'timestamp': notification.created_at.isoformat()
        }

        send_websocket_message_safe(
            "role_operador",
            {
                'type': 'system_message',
                'message': {
                    'type': 'guard_status_update',
                    'data': status_data
                }
            }
        )

    except Exception as e:
        logger.error(f"Error enviando actualización de estado de guardia: {e}")

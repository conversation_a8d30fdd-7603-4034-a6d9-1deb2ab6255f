"""
URL configuration for geovigia_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from rest_framework.authtoken.views import obtain_auth_token
from analytics_views import dashboard_analytics, kpi_summary, charts_data, heatmap_data, temporal_heatmap_data, zone_analytics_data

urlpatterns = [
    path('admin/', admin.site.urls),

    # API URLs
    path('api/users/', include('users.urls')),
    path('api/properties/', include('properties.urls')),
    path('api/routes/', include('routes.urls')),
    path('api/notifications/', include('notifications.urls')),
    path('api-token-auth/', obtain_auth_token, name='api_token_auth'),

    # Analytics URLs
    path('api/analytics/dashboard/', dashboard_analytics, name='dashboard_analytics'),
    path('api/analytics/kpis/', kpi_summary, name='kpi_summary'),
    path('api/analytics/charts/', charts_data, name='charts_data'),
    path('api/analytics/heatmap/', heatmap_data, name='heatmap_data'),
    path('api/analytics/temporal-heatmap/', temporal_heatmap_data, name='temporal_heatmap_data'),
    path('api/analytics/zone-analytics/', zone_analytics_data, name='zone_analytics_data'),
]

/**
 * Sistema de Autenticación para GeoVigia
 * Man<PERSON>a login, logout y verificación de sesiones
 */

// Configuración de autenticación
const AUTH_CONFIG = {
    tokenKey: 'geovigia_token',
    userKey: 'geovigia_user',
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 horas
};

// Usuarios de demostración
const DEMO_USERS = {
    'operador': {
        password: 'operador123',
        type: 'operador',
        name: '<PERSON>',
        permissions: ['all']
    },
    'guardia': {
        password: 'guardia123',
        type: 'guardia',
        name: '<PERSON>',
        permissions: ['dashboard', 'routes', 'monitoring', 'maps']
    },
    'cliente': {
        password: 'cliente123',
        type: 'cliente',
        name: 'Ana Propietaria',
        permissions: ['dashboard', 'properties', 'monitoring']
    },
    'demo_operador': {
        password: 'demo123',
        type: 'operador',
        name: '<PERSON>',
        permissions: ['all']
    },
    'demo_guardia1': {
        password: 'demo123',
        type: 'guardia',
        name: '<PERSON>',
        permissions: ['dashboard', 'routes', 'monitoring', 'maps']
    },
    'demo_cliente1': {
        password: 'demo123',
        type: 'cliente',
        name: 'María Demo',
        permissions: ['dashboard', 'properties', 'monitoring']
    }
};

/**
 * Verificar si el usuario está autenticado
 */
function checkAuthentication() {
    const token = localStorage.getItem(AUTH_CONFIG.tokenKey);
    const user = localStorage.getItem(AUTH_CONFIG.userKey);
    
    if (!token || !user) {
        showLoginScreen();
        return false;
    }
    
    try {
        const userData = JSON.parse(user);
        const tokenData = JSON.parse(token);
        
        // Verificar si el token ha expirado
        if (Date.now() > tokenData.expires) {
            logout();
            return false;
        }
        
        // Usuario autenticado, mostrar aplicación
        showMainApp(userData);
        return true;
        
    } catch (error) {
        console.error('Error verificando autenticación:', error);
        logout();
        return false;
    }
}

/**
 * Mostrar pantalla de login
 */
function showLoginScreen() {
    const loginScreen = document.getElementById('login-screen');
    const mainApp = document.getElementById('main-app');
    
    if (loginScreen) loginScreen.style.display = 'flex';
    if (mainApp) mainApp.style.display = 'none';
    
    // Configurar evento de login
    setupLoginForm();
}

/**
 * Mostrar aplicación principal
 */
function showMainApp(userData) {
    const loginScreen = document.getElementById('login-screen');
    const mainApp = document.getElementById('main-app');
    
    if (loginScreen) loginScreen.style.display = 'none';
    if (mainApp) mainApp.style.display = 'block';
    
    // Actualizar información del usuario en la interfaz
    updateUserInterface(userData);
}

/**
 * Configurar formulario de login
 */
function setupLoginForm() {
    const loginForm = document.getElementById('login-form');
    if (!loginForm) return;
    
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        
        if (!username || !password) {
            showToast('Por favor ingresa usuario y contraseña', 'error');
            return;
        }
        
        // Intentar autenticación
        attemptLogin(username, password);
    });
}

/**
 * Intentar login con credenciales
 */
async function attemptLogin(username, password) {
    const loginBtn = document.querySelector('.login-btn');

    // Mostrar estado de carga
    if (loginBtn) {
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verificando...';
        loginBtn.disabled = true;
    }

    try {
        // Intentar autenticación con el backend
        const response = await fetch(`${API_CONFIG.BASE_URL}/users/login/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        if (response.ok) {
            const data = await response.json();

            // Login exitoso con backend
            const userData = {
                username: data.user.username,
                name: `${data.user.first_name} ${data.user.last_name}`,
                type: data.user.user_type,
                email: data.user.email,
                permissions: getUserPermissions(data.user.user_type),
                loginTime: Date.now()
            };

            const tokenData = {
                token: data.token,
                expires: Date.now() + AUTH_CONFIG.sessionTimeout,
                user: username
            };

            // Guardar en localStorage
            localStorage.setItem(AUTH_CONFIG.userKey, JSON.stringify(userData));
            localStorage.setItem(AUTH_CONFIG.tokenKey, JSON.stringify(tokenData));

            showToast(`¡Bienvenido ${userData.name}!`, 'success');

            // Mostrar aplicación
            setTimeout(() => {
                showMainApp(userData);

                // Redirigir según tipo de usuario
                redirectUserByType(userData.type);
            }, 1000);

        } else {
            // Error del servidor
            const errorData = await response.json();
            showToast(errorData.message || 'Error de autenticación', 'error');
        }

    } catch (error) {
        console.error('Error de conexión:', error);

        // Fallback a autenticación local si el backend no está disponible
        const user = DEMO_USERS[username];

        if (user && user.password === password) {
            // Login exitoso con datos locales
            const userData = {
                username: username,
                name: user.name,
                type: user.type,
                permissions: user.permissions,
                loginTime: Date.now()
            };

            const tokenData = {
                token: generateToken(),
                expires: Date.now() + AUTH_CONFIG.sessionTimeout,
                user: username
            };

            // Guardar en localStorage
            localStorage.setItem(AUTH_CONFIG.userKey, JSON.stringify(userData));
            localStorage.setItem(AUTH_CONFIG.tokenKey, JSON.stringify(tokenData));

            showToast(`¡Bienvenido ${user.name}! (Modo offline)`, 'warning');

            // Mostrar aplicación
            setTimeout(() => {
                showMainApp(userData);

                // Redirigir según tipo de usuario
                redirectUserByType(user.type);
            }, 1000);

        } else {
            // Login fallido
            showToast('Usuario o contraseña incorrectos', 'error');
        }
    }

    // Restaurar botón
    if (loginBtn) {
        loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Iniciar Sesión';
        loginBtn.disabled = false;
    }
}

/**
 * Redirigir usuario según su tipo
 */
function redirectUserByType(userType) {
    switch (userType) {
        case 'guardia':
            // Verificar si existe dashboard específico para guardia
            if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
                const guardiaFile = 'guardia-dashboard.html';
                if (checkFileExists(guardiaFile)) {
                    window.location.href = guardiaFile;
                    return;
                }
            }
            break;
            
        case 'cliente':
            // Verificar si existe dashboard específico para cliente
            if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
                const clienteFile = 'cliente-dashboard.html';
                if (checkFileExists(clienteFile)) {
                    window.location.href = clienteFile;
                    return;
                }
            }
            break;
            
        case 'operador':
        default:
            // Los operadores usan el dashboard principal
            break;
    }
}

/**
 * Verificar si un archivo existe (método simple)
 */
function checkFileExists(filename) {
    // En un entorno real, esto sería una verificación más robusta
    // Por ahora, asumimos que los archivos existen
    return true;
}

/**
 * Actualizar interfaz con información del usuario
 */
function updateUserInterface(userData) {
    const userNameElement = document.getElementById('user-name');
    if (userNameElement) {
        userNameElement.textContent = userData.name;
    }
    
    // Actualizar título de la página
    document.title = `GeoVigia - ${userData.name} (${userData.type})`;
}

/**
 * Llenar credenciales automáticamente
 */
function fillCredentials(username, password) {
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    
    if (usernameInput) usernameInput.value = username;
    if (passwordInput) passwordInput.value = password;
    
    // Enfocar el botón de login
    const loginBtn = document.querySelector('.login-btn');
    if (loginBtn) loginBtn.focus();
    
    showToast('Credenciales llenadas automáticamente', 'info');
}

/**
 * Cerrar sesión
 */
function logout() {
    // Limpiar localStorage
    localStorage.removeItem(AUTH_CONFIG.tokenKey);
    localStorage.removeItem(AUTH_CONFIG.userKey);
    
    // Mostrar pantalla de login
    showLoginScreen();
    
    // Limpiar formulario
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.reset();
    }
    
    showToast('Sesión cerrada correctamente', 'info');
}

/**
 * Obtener permisos según tipo de usuario
 */
function getUserPermissions(userType) {
    switch (userType) {
        case 'operador':
            return ['all'];
        case 'guardia':
            return ['dashboard', 'routes', 'monitoring', 'maps'];
        case 'cliente':
            return ['dashboard', 'properties', 'monitoring'];
        default:
            return ['dashboard'];
    }
}

/**
 * Generar token simple
 */
function generateToken() {
    return 'geovigia_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
}

/**
 * Obtener usuario actual
 */
function getCurrentUser() {
    const user = localStorage.getItem(AUTH_CONFIG.userKey);
    return user ? JSON.parse(user) : null;
}

/**
 * Verificar permisos del usuario
 */
function hasPermission(permission) {
    const user = getCurrentUser();
    if (!user) return false;
    
    return user.permissions.includes('all') || user.permissions.includes(permission);
}

/**
 * Mostrar toast notification
 */
function showToast(message, type = 'info') {
    // Crear elemento toast si no existe
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }
    
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    const icon = type === 'success' ? 'check' : 
                 type === 'error' ? 'times' : 
                 type === 'warning' ? 'exclamation-triangle' : 'info';
    
    toast.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <span>${message}</span>
    `;
    
    toastContainer.appendChild(toast);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 3000);
}

// Inicializar autenticación cuando se carga la página
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔐 Inicializando sistema de autenticación...');
    
    // No verificar autenticación automáticamente aquí
    // Dejar que el script principal lo maneje
});

// Exponer funciones globalmente
window.checkAuthentication = checkAuthentication;
window.logout = logout;
window.fillCredentials = fillCredentials;
window.getCurrentUser = getCurrentUser;
window.hasPermission = hasPermission;

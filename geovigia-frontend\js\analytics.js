/**
 * Analytics Dashboard - KPIs en Tiempo Real
 * Maneja la visualización de estadísticas y métricas del sistema
 */

class AnalyticsDashboard {
    constructor() {
        this.apiBaseUrl = API_CONFIG.BASE_URL;
        this.updateInterval = 30000; // 30 segundos
        this.intervalId = null;
        this.isLoading = false;
    }

    /**
     * Inicializar el dashboard de analytics
     */
    init() {
        console.log('🚀 Inicializando Analytics Dashboard...');
        this.createAnalyticsSection();
        this.loadKPIs();
        this.startAutoUpdate();
    }

    /**
     * Crear la sección de analytics en el dashboard
     */
    createAnalyticsSection() {
        const mainContent = document.querySelector('.main-content');
        if (!mainContent) {
            console.error('No se encontró .main-content');
            return;
        }

        // Buscar si ya existe la sección de analytics
        let analyticsSection = document.getElementById('analytics-section');

        if (!analyticsSection) {
            // Crear nueva sección de analytics
            analyticsSection = document.createElement('div');
            analyticsSection.id = 'analytics-section';
            analyticsSection.className = 'analytics-section';
            analyticsSection.innerHTML = this.getAnalyticsHTML();

            // Insertar después del header pero antes de las tarjetas existentes
            const firstCard = mainContent.querySelector('.card');
            if (firstCard) {
                mainContent.insertBefore(analyticsSection, firstCard);
            } else {
                mainContent.appendChild(analyticsSection);
            }
        }
    }

    /**
     * HTML para la sección de analytics
     */
    getAnalyticsHTML() {
        return `
            <div class="analytics-header">
                <h2>📊 Analytics en Tiempo Real</h2>
                <div class="analytics-controls">
                    <span class="last-update">Última actualización: <span id="last-update-time">--:--:--</span></span>
                    <button id="refresh-analytics" class="btn-refresh" title="Actualizar ahora">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>

            <div class="kpis-container">
                <div class="kpi-card" id="kpi-guards">
                    <div class="kpi-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="kpi-content">
                        <div class="kpi-value" id="guards-value">--</div>
                        <div class="kpi-label">Guardias en Servicio</div>
                        <div class="kpi-change" id="guards-change">--</div>
                    </div>
                </div>

                <div class="kpi-card" id="kpi-properties">
                    <div class="kpi-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="kpi-content">
                        <div class="kpi-value" id="properties-value">--</div>
                        <div class="kpi-label">Propiedades Monitoreadas</div>
                        <div class="kpi-change" id="properties-change">--</div>
                    </div>
                </div>

                <div class="kpi-card" id="kpi-alerts">
                    <div class="kpi-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="kpi-content">
                        <div class="kpi-value" id="alerts-value">--</div>
                        <div class="kpi-label">Alertas Hoy</div>
                        <div class="kpi-change" id="alerts-change">--</div>
                    </div>
                </div>

                <div class="kpi-card" id="kpi-response">
                    <div class="kpi-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="kpi-content">
                        <div class="kpi-value" id="response-value">--</div>
                        <div class="kpi-label">Tiempo Promedio de Respuesta</div>
                        <div class="kpi-change" id="response-change">--</div>
                    </div>
                </div>

                <div class="kpi-card" id="kpi-routes">
                    <div class="kpi-icon">
                        <i class="fas fa-route"></i>
                    </div>
                    <div class="kpi-content">
                        <div class="kpi-value" id="routes-value">--</div>
                        <div class="kpi-label">Rutas Activas</div>
                        <div class="kpi-change" id="routes-change">--</div>
                    </div>
                </div>

                <div class="kpi-card" id="kpi-users">
                    <div class="kpi-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="kpi-content">
                        <div class="kpi-value" id="users-value">--</div>
                        <div class="kpi-label">Usuarios Activos</div>
                        <div class="kpi-change" id="users-change">--</div>
                    </div>
                </div>
            </div>

            <div class="analytics-status">
                <div id="analytics-loading" class="loading-indicator" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> Cargando datos...
                </div>
                <div id="analytics-error" class="error-indicator" style="display: none;">
                    <i class="fas fa-exclamation-circle"></i> Error al cargar datos
                </div>
            </div>
        `;
    }

    /**
     * Cargar KPIs desde la API
     */
    async loadKPIs() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoading(true);
        this.hideError();

        try {
            // Verificar autenticación usando el sistema de auth.js
            const tokenData = localStorage.getItem('geovigia_token');
            const userData = localStorage.getItem('geovigia_user');

            if (!tokenData || !userData) {
                // Si no hay autenticación, usar datos simulados
                console.log('📊 Usando datos simulados para analytics');
                this.loadSimulatedKPIs();
                return;
            }

            // Intentar cargar datos reales del backend
            const token = JSON.parse(tokenData).token;
            const response = await fetch(`${this.apiBaseUrl}/analytics/kpis/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Token ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                // Si el backend no responde, usar datos simulados
                console.log('📊 Backend no disponible, usando datos simulados');
                this.loadSimulatedKPIs();
                return;
            }

            const data = await response.json();
            this.updateKPIs(data.kpis);
            this.updateLastUpdateTime(data.last_updated);

            console.log('✅ KPIs actualizados correctamente desde backend');

        } catch (error) {
            console.log('📊 Error conectando con backend, usando datos simulados:', error.message);
            this.loadSimulatedKPIs();
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    /**
     * Cargar KPIs simulados para demostración
     */
    loadSimulatedKPIs() {
        const simulatedKPIs = {
            'active_guards': {
                value: Math.floor(Math.random() * 10) + 20,
                change: '+' + Math.floor(Math.random() * 5) + '%'
            },
            'monitored_properties': {
                value: Math.floor(Math.random() * 50) + 150,
                change: '+' + Math.floor(Math.random() * 3) + '%'
            },
            'alerts_today': {
                value: Math.floor(Math.random() * 8) + 2,
                change: '-' + Math.floor(Math.random() * 2) + '%'
            },
            'avg_response_time': {
                value: Math.floor(Math.random() * 5) + 3 + ' min',
                change: '-' + Math.floor(Math.random() * 10) + '%'
            },
            'active_routes': {
                value: Math.floor(Math.random() * 5) + 8,
                change: '+' + Math.floor(Math.random() * 2) + '%'
            },
            'active_users': {
                value: Math.floor(Math.random() * 8) + 24,
                change: '+' + Math.floor(Math.random() * 4) + '%'
            }
        };

        this.updateKPIs(simulatedKPIs);
        this.updateLastUpdateTime(new Date().toLocaleTimeString());
        console.log('✅ KPIs simulados cargados correctamente');
    }

    /**
     * Actualizar los valores de los KPIs en la interfaz
     */
    updateKPIs(kpis) {
        // Actualizar cada KPI
        Object.keys(kpis).forEach(kpiKey => {
            const kpi = kpis[kpiKey];
            this.updateKPI(kpiKey, kpi);
        });
    }

    /**
     * Actualizar un KPI individual
     */
    updateKPI(kpiKey, kpiData) {
        const mapping = {
            'active_guards': 'guards',
            'monitored_properties': 'properties',
            'alerts_today': 'alerts',
            'avg_response_time': 'response',
            'active_routes': 'routes',
            'active_users': 'users'
        };

        const elementKey = mapping[kpiKey];
        if (!elementKey) return;

        // Actualizar valor
        const valueElement = document.getElementById(`${elementKey}-value`);
        if (valueElement) {
            valueElement.textContent = kpiData.value;
            this.animateValue(valueElement);
        }

        // Actualizar cambio
        const changeElement = document.getElementById(`${elementKey}-change`);
        if (changeElement && kpiData.change) {
            changeElement.textContent = kpiData.change;
            changeElement.className = 'kpi-change ' + this.getChangeClass(kpiData.change);
        }
    }

    /**
     * Obtener clase CSS para el cambio (positivo/negativo)
     */
    getChangeClass(change) {
        if (change.startsWith('+')) return 'positive';
        if (change.startsWith('-')) return 'negative';
        return 'neutral';
    }

    /**
     * Animar el valor cuando cambia
     */
    animateValue(element) {
        element.style.transform = 'scale(1.1)';
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 200);
    }

    /**
     * Actualizar hora de última actualización
     */
    updateLastUpdateTime(timeString) {
        const timeElement = document.getElementById('last-update-time');
        if (timeElement) {
            timeElement.textContent = timeString || new Date().toLocaleTimeString();
        }
    }

    /**
     * Mostrar/ocultar indicador de carga
     */
    showLoading(show) {
        const loadingElement = document.getElementById('analytics-loading');
        if (loadingElement) {
            loadingElement.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * Mostrar error
     */
    showError(message) {
        const errorElement = document.getElementById('analytics-error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    /**
     * Ocultar error
     */
    hideError() {
        const errorElement = document.getElementById('analytics-error');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    /**
     * Iniciar actualización automática
     */
    startAutoUpdate() {
        // Limpiar intervalo anterior si existe
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }

        // Configurar nuevo intervalo
        this.intervalId = setInterval(() => {
            this.loadKPIs();
        }, this.updateInterval);

        // Configurar botón de actualización manual
        const refreshButton = document.getElementById('refresh-analytics');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this.loadKPIs();
            });
        }

        console.log(`🔄 Auto-actualización configurada cada ${this.updateInterval/1000} segundos`);
    }

    /**
     * Detener actualización automática
     */
    stopAutoUpdate() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log('⏹️ Auto-actualización detenida');
        }
    }

    /**
     * Destruir el dashboard
     */
    destroy() {
        this.stopAutoUpdate();
        const analyticsSection = document.getElementById('analytics-section');
        if (analyticsSection) {
            analyticsSection.remove();
        }
    }
}

// Instancia global del dashboard de analytics
window.analyticsDashboard = null;

// Función para inicializar analytics
function initAnalytics() {
    if (!window.analyticsDashboard) {
        window.analyticsDashboard = new AnalyticsDashboard();
        window.analyticsDashboard.init();
    }
}

// Función para destruir analytics
function destroyAnalytics() {
    if (window.analyticsDashboard) {
        window.analyticsDashboard.destroy();
        window.analyticsDashboard = null;
    }
}

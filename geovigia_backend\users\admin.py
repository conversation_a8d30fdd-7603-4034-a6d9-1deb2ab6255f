from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import CustomUser, ClienteProfile, GuardiaProfile, OperadorProfile


class ClienteProfileInline(admin.StackedInline):
    model = ClienteProfile
    can_delete = False
    verbose_name_plural = 'Perfil de Cliente'


class GuardiaProfileInline(admin.StackedInline):
    model = GuardiaProfile
    can_delete = False
    verbose_name_plural = 'Perfil de Guardia'


class OperadorProfileInline(admin.StackedInline):
    model = OperadorProfile
    can_delete = False
    verbose_name_plural = 'Perfil de Operador'


class CustomUserAdmin(UserAdmin):
    model = CustomUser
    list_display = [
        'username', 'email', 'first_name', 'last_name',
        'user_type', 'is_active_service', 'created_at'
    ]
    list_filter = ['user_type', 'is_active_service', 'is_active', 'created_at']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    ordering = ['-created_at']

    fieldsets = UserAdmin.fieldsets + (
        ('Información Adicional', {
            'fields': ('user_type', 'phone_number', 'address', 'is_active_service')
        }),
        ('Fechas', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    readonly_fields = ['created_at', 'updated_at']

    def get_inlines(self, request, obj):
        if not obj:
            return []

        if obj.user_type == 'cliente':
            return [ClienteProfileInline]
        elif obj.user_type == 'guardia':
            return [GuardiaProfileInline]
        elif obj.user_type == 'operador':
            return [OperadorProfileInline]

        return []


@admin.register(ClienteProfile)
class ClienteProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'emergency_contact_name', 'home_alone_frequency']
    search_fields = ['user__username', 'emergency_contact_name']
    list_filter = ['home_alone_frequency']


@admin.register(GuardiaProfile)
class GuardiaProfileAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'employee_id', 'shift_start', 'shift_end',
        'is_on_duty', 'last_location_update'
    ]
    search_fields = ['user__username', 'employee_id']
    list_filter = ['is_on_duty', 'shift_start', 'shift_end']
    readonly_fields = ['last_location_update']


@admin.register(OperadorProfile)
class OperadorProfileAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'department', 'can_manage_users',
        'can_manage_routes', 'can_view_reports'
    ]
    search_fields = ['user__username', 'department']
    list_filter = ['department', 'can_manage_users', 'can_manage_routes']


# Registrar el modelo CustomUser con el admin personalizado
admin.site.register(CustomUser, CustomUserAdmin)

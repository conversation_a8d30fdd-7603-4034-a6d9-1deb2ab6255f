from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()


class NotificationType(models.TextChoices):
    """Tipos de notificaciones disponibles"""
    ALERT = 'alert', 'Alerta'
    INFO = 'info', 'Información'
    WARNING = 'warning', 'Advertencia'
    SUCCESS = 'success', 'Éxito'
    EMERGENCY = 'emergency', 'Emergencia'
    ROUTE_UPDATE = 'route_update', 'Actualización de Ruta'
    PROPERTY_ALERT = 'property_alert', 'Alerta de Propiedad'
    GUARD_STATUS = 'guard_status', 'Estado de Guardia'


class NotificationPriority(models.TextChoices):
    """Prioridades de notificaciones"""
    LOW = 'low', 'Baja'
    NORMAL = 'normal', 'Normal'
    HIGH = 'high', 'Alta'
    CRITICAL = 'critical', 'Crítica'


class Notification(models.Model):
    """Modelo para notificaciones del sistema"""
    
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )
    
    # Destinatario de la notificación
    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications',
        verbose_name='Destinatario'
    )
    
    # Remitente (opcional, puede ser el sistema)
    sender = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sent_notifications',
        verbose_name='Remitente'
    )
    
    # Contenido de la notificación
    title = models.CharField(
        max_length=200,
        verbose_name='Título'
    )
    
    message = models.TextField(
        verbose_name='Mensaje'
    )
    
    # Metadatos
    notification_type = models.CharField(
        max_length=20,
        choices=NotificationType.choices,
        default=NotificationType.INFO,
        verbose_name='Tipo'
    )
    
    priority = models.CharField(
        max_length=10,
        choices=NotificationPriority.choices,
        default=NotificationPriority.NORMAL,
        verbose_name='Prioridad'
    )
    
    # Estados
    is_read = models.BooleanField(
        default=False,
        verbose_name='Leída'
    )
    
    is_sent = models.BooleanField(
        default=False,
        verbose_name='Enviada'
    )
    
    # Datos adicionales (JSON)
    extra_data = models.JSONField(
        default=dict,
        blank=True,
        verbose_name='Datos Adicionales'
    )
    
    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Creada'
    )
    
    sent_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Enviada en'
    )
    
    read_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Leída en'
    )
    
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Expira en'
    )

    class Meta:
        verbose_name = 'Notificación'
        verbose_name_plural = 'Notificaciones'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', '-created_at']),
            models.Index(fields=['notification_type', '-created_at']),
            models.Index(fields=['priority', '-created_at']),
            models.Index(fields=['is_read', '-created_at']),
        ]

    def __str__(self):
        return f"{self.title} -> {self.recipient.username}"

    def mark_as_read(self):
        """Marca la notificación como leída"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])

    def mark_as_sent(self):
        """Marca la notificación como enviada"""
        if not self.is_sent:
            self.is_sent = True
            self.sent_at = timezone.now()
            self.save(update_fields=['is_sent', 'sent_at'])

    @property
    def is_expired(self):
        """Verifica si la notificación ha expirado"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    def to_dict(self):
        """Convierte la notificación a diccionario para WebSocket"""
        return {
            'id': str(self.id),
            'title': self.title,
            'message': self.message,
            'type': self.notification_type,
            'priority': self.priority,
            'is_read': self.is_read,
            'created_at': self.created_at.isoformat(),
            'extra_data': self.extra_data,
            'sender': self.sender.username if self.sender else 'Sistema',
        }


class PushSubscription(models.Model):
    """Modelo para suscripciones push del navegador"""
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='push_subscriptions',
        verbose_name='Usuario'
    )
    
    endpoint = models.URLField(
        verbose_name='Endpoint'
    )
    
    p256dh_key = models.TextField(
        verbose_name='Clave P256DH'
    )
    
    auth_key = models.TextField(
        verbose_name='Clave Auth'
    )
    
    user_agent = models.TextField(
        blank=True,
        verbose_name='User Agent'
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name='Activa'
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Creada'
    )
    
    last_used = models.DateTimeField(
        auto_now=True,
        verbose_name='Último Uso'
    )

    class Meta:
        verbose_name = 'Suscripción Push'
        verbose_name_plural = 'Suscripciones Push'
        unique_together = ['user', 'endpoint']

    def __str__(self):
        return f"Push subscription for {self.user.username}"


class WebSocketConnection(models.Model):
    """Modelo para rastrear conexiones WebSocket activas"""
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='websocket_connections',
        verbose_name='Usuario'
    )
    
    channel_name = models.CharField(
        max_length=255,
        unique=True,
        verbose_name='Nombre del Canal'
    )
    
    connected_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Conectado en'
    )
    
    last_activity = models.DateTimeField(
        auto_now=True,
        verbose_name='Última Actividad'
    )
    
    user_agent = models.TextField(
        blank=True,
        verbose_name='User Agent'
    )
    
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name='Dirección IP'
    )

    class Meta:
        verbose_name = 'Conexión WebSocket'
        verbose_name_plural = 'Conexiones WebSocket'
        ordering = ['-connected_at']

    def __str__(self):
        return f"WebSocket: {self.user.username} ({self.channel_name})"

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from notifications.models import Notification, NotificationType, NotificationPriority
from notifications.signals import send_emergency_alert, send_property_alert, send_guard_status_update
import time

User = get_user_model()


class Command(BaseCommand):
    help = 'Comando para probar el sistema de notificaciones'

    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['basic', 'emergency', 'property', 'guard', 'all'],
            default='basic',
            help='Tipo de test a ejecutar'
        )
        parser.add_argument(
            '--count',
            type=int,
            default=1,
            help='Número de notificaciones a crear'
        )
        parser.add_argument(
            '--delay',
            type=float,
            default=1.0,
            help='Delay entre notificaciones en segundos'
        )

    def handle(self, *args, **options):
        test_type = options['type']
        count = options['count']
        delay = options['delay']

        self.stdout.write(
            self.style.SUCCESS(f'🔔 Iniciando test de notificaciones: {test_type}')
        )

        if test_type == 'basic' or test_type == 'all':
            self.test_basic_notifications(count, delay)

        if test_type == 'emergency' or test_type == 'all':
            self.test_emergency_alerts(count, delay)

        if test_type == 'property' or test_type == 'all':
            self.test_property_alerts(count, delay)

        if test_type == 'guard' or test_type == 'all':
            self.test_guard_status(count, delay)

        self.stdout.write(
            self.style.SUCCESS('✅ Test de notificaciones completado')
        )

    def test_basic_notifications(self, count, delay):
        """Prueba notificaciones básicas"""
        self.stdout.write('📝 Probando notificaciones básicas...')
        
        # Obtener usuarios de prueba
        users = User.objects.all()[:3]
        if not users:
            self.stdout.write(
                self.style.WARNING('⚠️ No hay usuarios disponibles para el test')
            )
            return

        notification_types = [
            (NotificationType.INFO, NotificationPriority.NORMAL, 'Información de prueba'),
            (NotificationType.WARNING, NotificationPriority.HIGH, 'Advertencia de prueba'),
            (NotificationType.SUCCESS, NotificationPriority.NORMAL, 'Operación exitosa'),
        ]

        for i in range(count):
            for user in users:
                for notif_type, priority, message in notification_types:
                    notification = Notification.objects.create(
                        recipient=user,
                        title=f'Test {notif_type.title()} #{i+1}',
                        message=f'{message} para {user.username}',
                        notification_type=notif_type,
                        priority=priority,
                        extra_data={
                            'test': True,
                            'iteration': i+1,
                            'timestamp': time.time()
                        }
                    )
                    
                    self.stdout.write(f'  ✓ Notificación creada: {notification.title}')
                    
                    if delay > 0:
                        time.sleep(delay)

    def test_emergency_alerts(self, count, delay):
        """Prueba alertas de emergencia"""
        self.stdout.write('🚨 Probando alertas de emergencia...')
        
        # Obtener un cliente para simular la alerta
        cliente = User.objects.filter(user_type='cliente').first()
        if not cliente:
            self.stdout.write(
                self.style.WARNING('⚠️ No hay clientes disponibles para el test')
            )
            return

        alert_types = [
            ('casa_sola', 'Casa sola - Solicitud de vigilancia'),
            ('actitud_sospechosa', 'Actitud sospechosa detectada'),
            ('solicitar_ayuda', 'Solicitud de ayuda urgente'),
            ('emergencia', 'Emergencia crítica')
        ]

        for i in range(count):
            for alert_type, message in alert_types:
                send_emergency_alert(
                    user=cliente,
                    alert_type=alert_type,
                    message=f'{message} - Test #{i+1}',
                    location={
                        'latitude': 19.4326 + (i * 0.001),
                        'longitude': -99.1332 + (i * 0.001),
                        'address': f'Dirección de prueba #{i+1}'
                    }
                )
                
                self.stdout.write(f'  🚨 Alerta enviada: {alert_type}')
                
                if delay > 0:
                    time.sleep(delay)

    def test_property_alerts(self, count, delay):
        """Prueba alertas de propiedades"""
        self.stdout.write('🏠 Probando alertas de propiedades...')
        
        # Importar modelo de propiedades
        try:
            from properties.models import Property
            properties = Property.objects.all()[:2]
            
            if not properties:
                self.stdout.write(
                    self.style.WARNING('⚠️ No hay propiedades disponibles para el test')
                )
                return

            alert_types = [
                ('intrusion', 'Posible intrusión detectada'),
                ('movement', 'Movimiento detectado en perímetro'),
                ('alarm', 'Alarma activada'),
                ('maintenance', 'Mantenimiento requerido')
            ]

            for i in range(count):
                for prop in properties:
                    for alert_type, message in alert_types:
                        send_property_alert(
                            property_instance=prop,
                            alert_type=alert_type,
                            message=f'{message} - Test #{i+1}'
                        )
                        
                        self.stdout.write(f'  🏠 Alerta de propiedad: {prop.name} - {alert_type}')
                        
                        if delay > 0:
                            time.sleep(delay)
                            
        except ImportError:
            self.stdout.write(
                self.style.WARNING('⚠️ Modelo de propiedades no disponible')
            )

    def test_guard_status(self, count, delay):
        """Prueba actualizaciones de estado de guardia"""
        self.stdout.write('👮 Probando actualizaciones de estado de guardia...')
        
        # Obtener guardias
        guardias = User.objects.filter(user_type='guardia')[:2]
        if not guardias:
            self.stdout.write(
                self.style.WARNING('⚠️ No hay guardias disponibles para el test')
            )
            return

        status_types = [
            ('en_servicio', 'Guardia en servicio'),
            ('en_ruta', 'Guardia en ruta de patrullaje'),
            ('en_descanso', 'Guardia en descanso'),
            ('fuera_servicio', 'Guardia fuera de servicio')
        ]

        for i in range(count):
            for guardia in guardias:
                for status, description in status_types:
                    send_guard_status_update(
                        guard=guardia,
                        status=status,
                        location={
                            'latitude': 19.4326 + (i * 0.002),
                            'longitude': -99.1332 + (i * 0.002),
                            'timestamp': time.time()
                        }
                    )
                    
                    self.stdout.write(f'  👮 Estado de guardia: {guardia.username} - {status}')
                    
                    if delay > 0:
                        time.sleep(delay)

    def create_test_users_if_needed(self):
        """Crea usuarios de prueba si no existen"""
        user_types = ['operador', 'guardia', 'cliente']
        
        for user_type in user_types:
            if not User.objects.filter(user_type=user_type).exists():
                user = User.objects.create_user(
                    username=f'test_{user_type}',
                    email=f'test_{user_type}@geovigia.com',
                    password='testpass123',
                    user_type=user_type,
                    first_name=f'Test {user_type.title()}',
                    last_name='User'
                )
                self.stdout.write(f'  ✓ Usuario de prueba creado: {user.username}')

    def cleanup_test_notifications(self):
        """Limpia notificaciones de prueba"""
        deleted_count = Notification.objects.filter(
            extra_data__test=True
        ).delete()[0]
        
        self.stdout.write(f'🧹 {deleted_count} notificaciones de prueba eliminadas')

    def show_statistics(self):
        """Muestra estadísticas del sistema"""
        total_notifications = Notification.objects.count()
        unread_notifications = Notification.objects.filter(is_read=False).count()
        
        self.stdout.write('\n📊 Estadísticas del Sistema:')
        self.stdout.write(f'  Total de notificaciones: {total_notifications}')
        self.stdout.write(f'  Notificaciones no leídas: {unread_notifications}')
        
        # Estadísticas por tipo
        for notif_type in NotificationType.choices:
            count = Notification.objects.filter(notification_type=notif_type[0]).count()
            self.stdout.write(f'  {notif_type[1]}: {count}')

    def handle_cleanup(self, *args, **options):
        """Comando para limpiar notificaciones de prueba"""
        self.cleanup_test_notifications()
        
    def handle_stats(self, *args, **options):
        """Comando para mostrar estadísticas"""
        self.show_statistics()

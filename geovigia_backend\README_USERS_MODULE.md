# Módulo de Usuarios - Sistema GeoVigia

## Descripción
Este es el primer módulo completado del sistema GeoVigia. Maneja la autenticación, autorización y gestión de usuarios para los tres tipos de usuarios del sistema: Cliente, Guardia y Operador.

## Características Implementadas

### 🔐 Autenticación y Autorización
- Sistema de autenticación basado en tokens
- Tres tipos de usuarios: Cliente, Guardia, Operador
- Permisos personalizados por tipo de usuario
- Registro y login de usuarios

### 👥 Gestión de Usuarios
- Modelo de usuario personalizado (`CustomUser`)
- Perfiles específicos para cada tipo de usuario:
  - **ClienteProfile**: Contactos de emergencia, frecuencia de uso
  - **GuardiaProfile**: Horarios, ubicación actual, estado de servicio
  - **OperadorProfile**: Permisos de gestión

### 📍 Funcionalidades Específicas
- **Para Guardias**: 
  - Actualización de ubicación en tiempo real
  - Control de estado de servicio (en servicio/fuera de servicio)
- **Para Operadores**: 
  - Gestión completa de usuarios
  - Visualización de todos los perfiles

## APIs Disponibles

### Autenticación
- `POST /api/users/login/` - Iniciar sesión
- `POST /api/users/logout/` - Cerrar sesión
- `POST /api/users/register/` - Registrar nuevo usuario

### Perfil de Usuario
- `GET /api/users/profile/` - Obtener perfil del usuario autenticado
- `PUT /api/users/profile/update/` - Actualizar perfil

### Gestión de Usuarios (Solo Operadores)
- `GET /api/users/list/` - Listar todos los usuarios
- `GET /api/users/<id>/` - Obtener usuario específico
- `PUT /api/users/<id>/` - Actualizar usuario específico
- `DELETE /api/users/<id>/` - Eliminar usuario

### Funciones de Guardia
- `POST /api/users/guardia/location/` - Actualizar ubicación
- `POST /api/users/guardia/duty/` - Cambiar estado de servicio

## Estructura de Datos

### Usuario Base (CustomUser)
```json
{
  "id": 1,
  "username": "usuario_test",
  "email": "<EMAIL>",
  "first_name": "Nombre",
  "last_name": "Apellido",
  "user_type": "cliente|guardia|operador",
  "phone_number": "+1234567890",
  "address": "Dirección completa",
  "is_active_service": true,
  "created_at": "2025-05-28T20:00:00Z",
  "updated_at": "2025-05-28T20:00:00Z"
}
```

### Perfil de Cliente
```json
{
  "emergency_contact_name": "Contacto de Emergencia",
  "emergency_contact_phone": "+1234567890",
  "home_alone_frequency": 5
}
```

### Perfil de Guardia
```json
{
  "employee_id": "G0001",
  "shift_start": "08:00:00",
  "shift_end": "20:00:00",
  "is_on_duty": true,
  "current_latitude": "19.4326",
  "current_longitude": "-99.1332",
  "last_location_update": "2025-05-28T20:30:00Z"
}
```

### Perfil de Operador
```json
{
  "department": "Administración",
  "can_manage_users": true,
  "can_manage_routes": true,
  "can_view_reports": true
}
```

## Instalación y Configuración

### Requisitos
- Python 3.8+
- Django 5.2+
- Django REST Framework
- django-cors-headers

### Configuración
1. Las migraciones ya están aplicadas
2. Superusuario creado: `admin` / `admin123`
3. Servidor ejecutándose en: `http://127.0.0.1:8000`

### Panel de Administración
Accede a `http://127.0.0.1:8000/admin/` con las credenciales del superusuario para gestionar usuarios desde la interfaz web.

## Pruebas
Ejecuta el script de pruebas para verificar todas las funcionalidades:
```bash
python test_apis.py
```

## Próximos Módulos
1. **Módulo de Propiedades**: Gestión de propiedades y perímetros
2. **Módulo de Rutas**: Definición y gestión de recorridos
3. **Módulo de Alertas**: Sistema de alertas y notificaciones
4. **Módulo de Seguimiento**: Tracking en tiempo real

## Notas Técnicas
- Autenticación por tokens para APIs
- Permisos personalizados implementados
- Modelos optimizados para escalabilidad
- Código modularizado (archivos < 400 líneas)
- Documentación completa en código

---
**Estado**: ✅ COMPLETADO
**Fecha**: Mayo 2025
**Versión**: 1.0

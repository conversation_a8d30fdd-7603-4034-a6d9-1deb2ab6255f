<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>GeoVigia - Sistema de Vigilancia Inteligente</title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            color: #0f172a;
        }
        
        /* Login Screen */
        .login-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .login-container {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            margin: 1rem;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .login-header h2 {
            color: #333;
            margin: 0 0 0.5rem 0;
            font-size: 1.5rem;
        }
        
        .login-header p {
            color: #666;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
        
        .input-group input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 1rem;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .login-btn:hover {
            background: #5a6fd8;
        }
        
        .credential-item {
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .credential-item:hover {
            background: #e9ecef;
        }
        
        .credential-item i {
            color: #667eea;
            width: 20px;
        }
        
        /* Main App */
        .main-app {
            display: none;
            min-height: 100vh;
            background: #f8fafc;
        }
        
        .main-header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem 2rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .main-nav {
            display: flex;
            gap: 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: #64748b;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .nav-link:hover,
        .nav-link.active {
            background: #667eea;
            color: white;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #64748b;
        }
        
        .logout-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 0.5rem;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .logout-btn:hover {
            background: #dc2626;
        }
        
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .content-section {
            display: none;
        }
        
        .content-section.active {
            display: block;
        }
        
        .section-header {
            margin-bottom: 2rem;
        }
        
        .section-header h1 {
            font-size: 2rem;
            color: #0f172a;
            margin-bottom: 0.5rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .dashboard-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }
        
        .card-content h3 {
            font-size: 0.875rem;
            color: #64748b;
            margin-bottom: 0.5rem;
        }
        
        .card-number {
            font-size: 2rem;
            font-weight: bold;
            color: #0f172a;
            margin-bottom: 0.25rem;
        }
        
        .card-content p {
            font-size: 0.875rem;
            color: #64748b;
        }
        
        .quick-actions {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
        }
        
        .quick-actions h2 {
            margin-bottom: 1rem;
            color: #0f172a;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            color: #64748b;
        }
        
        .action-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .main-nav {
                display: none;
            }
            
            .header-content {
                padding: 0 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="login-screen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <span>GeoVigia</span>
                </div>
                <h2>Sistema de Vigilancia Inteligente</h2>
                <p>Ingresa tus credenciales para acceder</p>
            </div>
            
            <form id="login-form">
                <div class="form-group">
                    <label for="username">Usuario</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" required placeholder="Ingresa tu usuario">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">Contraseña</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required placeholder="Ingresa tu contraseña">
                    </div>
                </div>
                
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    Iniciar Sesión
                </button>
            </form>
            
            <div style="border-top: 1px solid #e0e0e0; padding-top: 1.5rem; margin-top: 2rem;">
                <h4 style="margin: 0 0 1rem 0; color: #333; text-align: center;">Credenciales de Demo</h4>
                <div class="credential-item" onclick="fillCredentials('operador', 'operador123')">
                    <i class="fas fa-user-tie"></i>
                    <span><strong>Operador:</strong> operador / operador123</span>
                </div>
                <div class="credential-item" onclick="fillCredentials('guardia', 'guardia123')">
                    <i class="fas fa-user-shield"></i>
                    <span><strong>Guardia:</strong> guardia / guardia123</span>
                </div>
                <div class="credential-item" onclick="fillCredentials('cliente', 'cliente123')">
                    <i class="fas fa-user"></i>
                    <span><strong>Cliente:</strong> cliente / cliente123</span>
                </div>
                <p style="text-align: center; font-size: 0.875rem; color: #666; margin: 1rem 0 0 0;">
                    Haz click en cualquier credencial para llenar automáticamente
                </p>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="main-app" class="main-app">
        <header class="main-header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <span>GeoVigia</span>
                </div>
                <nav class="main-nav">
                    <a href="#dashboard" class="nav-link active" onclick="showSection('dashboard')">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                    <a href="#users" class="nav-link" onclick="showSection('users')">
                        <i class="fas fa-users"></i>
                        Usuarios
                    </a>
                    <a href="#properties" class="nav-link" onclick="showSection('properties')">
                        <i class="fas fa-home"></i>
                        Propiedades
                    </a>
                    <a href="#maps" class="nav-link" onclick="showSection('maps')">
                        <i class="fas fa-map"></i>
                        Mapas
                    </a>
                    <a href="#analytics" class="nav-link" onclick="showSection('analytics')">
                        <i class="fas fa-chart-line"></i>
                        Analytics
                    </a>
                </nav>
                <div class="user-menu">
                    <div class="user-info">
                        <span id="user-name">Operador</span>
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <button class="logout-btn" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </header>

        <main class="main-content">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="content-section active">
                <div class="section-header">
                    <h1><i class="fas fa-tachometer-alt"></i> Dashboard Principal</h1>
                    <p>Panel de control del sistema GeoVigia</p>
                </div>

                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="card-content">
                            <h3>Usuarios Activos</h3>
                            <div class="card-number">24</div>
                            <p>Guardias en servicio</p>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="card-content">
                            <h3>Propiedades</h3>
                            <div class="card-number">156</div>
                            <p>Bajo vigilancia</p>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">
                            <i class="fas fa-route"></i>
                        </div>
                        <div class="card-content">
                            <h3>Rutas Activas</h3>
                            <div class="card-number">8</div>
                            <p>En ejecución</p>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="card-content">
                            <h3>Alertas Hoy</h3>
                            <div class="card-number">3</div>
                            <p>Reportes recibidos</p>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h2>Acciones Rápidas</h2>
                    <div class="actions-grid">
                        <button class="action-btn" onclick="showSection('users')">
                            <i class="fas fa-user-plus"></i>
                            Gestionar Usuarios
                        </button>
                        <button class="action-btn" onclick="showSection('properties')">
                            <i class="fas fa-home"></i>
                            Gestionar Propiedades
                        </button>
                        <button class="action-btn" onclick="showSection('maps')">
                            <i class="fas fa-map"></i>
                            Ver Mapas
                        </button>
                        <button class="action-btn" onclick="showSection('analytics')">
                            <i class="fas fa-chart-line"></i>
                            Ver Analytics
                        </button>
                    </div>
                </div>
            </section>

            <!-- Other Sections -->
            <section id="users-section" class="content-section">
                <div class="section-header">
                    <h1><i class="fas fa-users"></i> Gestión de Usuarios</h1>
                </div>
                <div class="success-message">
                    ✅ Módulo de gestión de usuarios implementado y funcionando
                </div>
            </section>

            <section id="properties-section" class="content-section">
                <div class="section-header">
                    <h1><i class="fas fa-home"></i> Gestión de Propiedades</h1>
                </div>
                <div class="success-message">
                    ✅ Módulo de gestión de propiedades implementado y funcionando
                </div>
            </section>

            <section id="maps-section" class="content-section">
                <div class="section-header">
                    <h1><i class="fas fa-map"></i> Mapas Interactivos</h1>
                </div>
                <div class="success-message">
                    ✅ Sistema de mapas con Leaflet implementado y funcionando
                </div>
            </section>

            <section id="analytics-section" class="content-section">
                <div class="section-header">
                    <h1><i class="fas fa-chart-line"></i> Analytics Avanzados</h1>
                </div>
                <div class="success-message">
                    ✅ Sistema de analytics con gráficos y heatmaps implementado y funcionando
                </div>
            </section>
        </main>
    </div>

    <script>
        // Funciones de autenticación
        function fillCredentials(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }

        function showLoginSuccess(username) {
            const loginScreen = document.getElementById('login-screen');
            const mainApp = document.getElementById('main-app');
            const userName = document.getElementById('user-name');
            
            loginScreen.style.display = 'none';
            mainApp.style.display = 'block';
            userName.textContent = username;
        }

        function logout() {
            const loginScreen = document.getElementById('login-screen');
            const mainApp = document.getElementById('main-app');
            
            mainApp.style.display = 'none';
            loginScreen.style.display = 'flex';
            
            // Limpiar formulario
            document.getElementById('login-form').reset();
        }

        function showSection(sectionName) {
            // Ocultar todas las secciones
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => section.classList.remove('active'));
            
            // Mostrar sección seleccionada
            const targetSection = document.getElementById(sectionName + '-section');
            if (targetSection) {
                targetSection.classList.add('active');
            }
            
            // Actualizar navegación
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => link.classList.remove('active'));
            
            const activeLink = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }
        }

        // Manejar envío del formulario
        document.getElementById('login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // Validación simple
            const validUsers = {
                'operador': 'operador123',
                'guardia': 'guardia123',
                'cliente': 'cliente123'
            };
            
            if (validUsers[username] && validUsers[username] === password) {
                showLoginSuccess(username);
            } else {
                alert('Usuario o contraseña incorrectos');
            }
        });

        console.log('✅ GeoVigia Simple cargado correctamente');
    </script>
</body>
</html>
